'use client';

import { useState } from 'react';
import FileUpload from '@/components/FileUpload';
import Dashboard from '@/components/Dashboard';
import BatchDashboard from '@/components/BatchDashboard';

export default function Home() {
  const [currentAnalysis, setCurrentAnalysis] = useState<string | null>(null);
  const [currentBatch, setCurrentBatch] = useState<{ batchId: string; fileResults: any[] } | null>(null);

  const handleSingleAnalysis = (analysisId: string) => {
    setCurrentAnalysis(analysisId);
    setCurrentBatch(null);
  };

  const handleBatchAnalysis = (batchId: string, fileResults: any[]) => {
    setCurrentBatch({ batchId, fileResults });
    setCurrentAnalysis(null);
  };

  if (currentAnalysis) {
    return <Dashboard analysisId={currentAnalysis} />;
  }

  if (currentBatch) {
    return <BatchDashboard batchId={currentBatch.batchId} fileResults={currentBatch.fileResults} />;
  }

  return (
    <FileUpload
      onAnalysisComplete={handleSingleAnalysis}
      onBatchAnalysisComplete={handleBatchAnalysis}
    />
  );
}
