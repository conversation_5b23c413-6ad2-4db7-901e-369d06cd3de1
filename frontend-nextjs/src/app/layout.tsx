import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AnalysisProvider } from "@/context/AnalysisContext";
import Header from "@/components/Header";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Log Analyzer - Enterprise Log Analysis Tool",
  description: "Advanced log analysis tool with enterprise structured log parsing capabilities",
  keywords: ["log analysis", "enterprise logs", "monitoring", "analytics"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AnalysisProvider>
          <div className="min-h-screen bg-gray-50">
            <Header />
            <main className="container mx-auto px-4 py-8">
              {children}
            </main>
          </div>
        </AnalysisProvider>
      </body>
    </html>
  );
}
