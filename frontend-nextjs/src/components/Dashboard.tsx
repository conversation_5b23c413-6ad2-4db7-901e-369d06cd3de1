'use client';

import { useState, useEffect } from 'react';
import { useAnalysis } from '@/context/AnalysisContext';
import ErrorAnalysisChart from './charts/ErrorAnalysisChart';
import Timeline<PERSON>hart from './charts/TimelineChart';
import PatternAnalysisChart from './charts/PatternAnalysisChart';
import PerformanceChart from './charts/PerformanceChart';
import CustomerChart from './charts/CustomerChart';
import ApplicationChart from './charts/ApplicationChart';
import LogSearch from './LogSearch';
import InsightsPanel from './InsightsPanel';

interface DashboardProps {
  analysisId: string;
}

export default function Dashboard({ analysisId }: DashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setAnalysisData: setContextAnalysisData } = useAnalysis();

  useEffect(() => {
    loadAnalysisData();
  }, [analysisId]);

  const loadAnalysisData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [summary, errorAnalysis, timeAnalysis, patternAnalysis] = await Promise.all([
        fetch(`/api/analysis/${analysisId}/summary`).then(res => res.json()),
        fetch(`/api/analysis/${analysisId}/errors`).then(res => res.json()),
        fetch(`/api/analysis/${analysisId}/timeline`).then(res => res.json()),
        fetch(`/api/analysis/${analysisId}/patterns`).then(res => res.json()),
      ]);

      // Try to get enterprise-specific analysis data
      let performanceAnalysis = {};
      let customerAnalysis = {};
      let applicationAnalysis = {};

      try {
        const response = await fetch(`/api/analysis/${analysisId}/performance`);
        if (response.ok) {
          performanceAnalysis = await response.json();
        }
      } catch (e) {
        console.log('Performance analysis not available');
      }

      try {
        const response = await fetch(`/api/analysis/${analysisId}/customers`);
        if (response.ok) {
          customerAnalysis = await response.json();
        }
      } catch (e) {
        console.log('Customer analysis not available');
      }

      try {
        const response = await fetch(`/api/analysis/${analysisId}/applications`);
        if (response.ok) {
          applicationAnalysis = await response.json();
        }
      } catch (e) {
        console.log('Application analysis not available');
      }

      const combinedData = {
        summary,
        errorAnalysis,
        timeAnalysis,
        patternAnalysis,
        performanceAnalysis,
        customerAnalysis,
        applicationAnalysis,
      };

      setAnalysisData(combinedData);
      setContextAnalysisData(combinedData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analysis data');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'errors', label: 'Error Analysis', icon: '🚨' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'customers', label: 'Customers', icon: '👥' },
    { id: 'applications', label: 'Applications', icon: '📱' },
    { id: 'patterns', label: 'Patterns', icon: '🔍' },
    { id: 'timeline', label: 'Timeline', icon: '📈' },
    { id: 'search', label: 'Search Logs', icon: '🔎' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading analysis data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="text-red-400">⚠️</div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Analysis</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with summary */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">Analysis ID: {analysisId}</span>
            <button
              onClick={() => window.location.reload()}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Refresh
            </button>
          </div>
        </div>

        {analysisData?.summary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-blue-600 text-sm font-medium">Total Entries</div>
              <div className="text-blue-900 text-2xl font-bold">
                {analysisData.summary.summary?.total_entries?.toLocaleString() || 0}
              </div>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4">
              <div className="text-red-600 text-sm font-medium">Error Rate</div>
              <div className="text-red-900 text-2xl font-bold">
                {analysisData.summary.summary?.error_rate?.toFixed(1) || 0}%
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-green-600 text-sm font-medium">Health Status</div>
              <div className="text-green-900 text-lg font-bold capitalize">
                {analysisData.summary.summary?.health_status || 'Unknown'}
              </div>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-purple-600 text-sm font-medium">Parse Success</div>
              <div className="text-purple-900 text-2xl font-bold">
                {analysisData.summary.summary?.parse_success_rate?.toFixed(1) || 0}%
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Insights Panel */}
      <InsightsPanel data={analysisData} />

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <ErrorAnalysisChart data={analysisData.errorAnalysis} />
                <PatternAnalysisChart data={analysisData.patternAnalysis} />
              </div>
              {analysisData.performanceAnalysis?.performance_analysis && (
                <PerformanceChart data={analysisData.performanceAnalysis} />
              )}
              <TimelineChart data={analysisData.timeAnalysis} />
            </div>
          )}

          {activeTab === 'errors' && (
            <ErrorAnalysisChart data={analysisData.errorAnalysis} detailed={true} />
          )}

          {activeTab === 'performance' && (
            <PerformanceChart data={analysisData.performanceAnalysis} detailed={true} />
          )}

          {activeTab === 'customers' && (
            <CustomerChart data={analysisData.customerAnalysis} detailed={true} />
          )}

          {activeTab === 'applications' && (
            <ApplicationChart data={analysisData.applicationAnalysis} detailed={true} />
          )}

          {activeTab === 'patterns' && (
            <PatternAnalysisChart data={analysisData.patternAnalysis} detailed={true} />
          )}

          {activeTab === 'timeline' && (
            <TimelineChart data={analysisData.timeAnalysis} detailed={true} />
          )}

          {activeTab === 'search' && (
            <LogSearch analysisId={analysisId} />
          )}
        </div>
      </div>
    </div>
  );
}
