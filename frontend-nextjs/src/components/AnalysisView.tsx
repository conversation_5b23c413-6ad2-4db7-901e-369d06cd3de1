'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAnalysis } from '@/context/AnalysisContext';
import Dashboard from './Dashboard';

interface AnalysisViewProps {
  analysisId: string;
}

export default function AnalysisView({ analysisId }: AnalysisViewProps) {
  const { setCurrentAnalysis } = useAnalysis();
  const router = useRouter();

  useEffect(() => {
    if (analysisId) {
      setCurrentAnalysis(analysisId);
    }
  }, [analysisId, setCurrentAnalysis]);

  if (!analysisId) {
    router.push('/');
    return null;
  }

  return <Dashboard analysisId={analysisId} />;
}
