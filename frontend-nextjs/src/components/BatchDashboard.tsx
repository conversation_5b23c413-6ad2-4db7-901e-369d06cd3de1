'use client';

import { useState, useEffect } from 'react';
import { useAnalysis } from '@/context/AnalysisContext';
import RouteComparisonChart from './charts/RouteComparisonChart';
import BatchSummaryChart from './charts/BatchSummaryChart';
import FileComparisonTable from './FileComparisonTable';

interface BatchDashboardProps {
  batchId: string;
  fileResults: any[];
}

export default function BatchDashboard({ batchId, fileResults }: BatchDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [batchData, setBatchData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { setAnalysisData } = useAnalysis();

  useEffect(() => {
    loadBatchData();
  }, [batchId]);

  const loadBatchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/batch-analysis/${batchId}`);
      if (!response.ok) {
        throw new Error('Failed to load batch analysis');
      }

      const data = await response.json();
      setBatchData(data);
      setAnalysisData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load batch data');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'routes', label: 'Route Comparison', icon: '🛣️' },
    { id: 'files', label: 'File Analysis', icon: '📁' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'insights', label: 'Insights', icon: '💡' },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading batch analysis...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="text-red-400">⚠️</div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Batch Analysis</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const summary = batchData?.comparative_analysis?.summary_comparison;
  const routeComparison = batchData?.comparative_analysis?.route_comparison;
  const insights = batchData?.comparative_analysis?.aggregated_insights || [];

  return (
    <div className="space-y-6">
      {/* Header with batch summary */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Batch Analysis Results</h2>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">Batch ID: {batchId}</span>
            <button
              onClick={loadBatchData}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Refresh
            </button>
          </div>
        </div>

        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="text-blue-600 text-sm font-medium">Files Analyzed</div>
              <div className="text-blue-900 text-2xl font-bold">
                {summary.total_files}
              </div>
            </div>
            
            <div className="bg-green-50 rounded-lg p-4">
              <div className="text-green-600 text-sm font-medium">Total Entries</div>
              <div className="text-green-900 text-2xl font-bold">
                {summary.total_entries?.toLocaleString() || 0}
              </div>
            </div>
            
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="text-purple-600 text-sm font-medium">API Routes</div>
              <div className="text-purple-900 text-2xl font-bold">
                {summary.total_routes || 0}
              </div>
            </div>
            
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="text-yellow-600 text-sm font-medium">Avg Response</div>
              <div className="text-yellow-900 text-2xl font-bold">
                {summary.avg_response_time?.toFixed(0) || 0}ms
              </div>
            </div>
            
            <div className="bg-red-50 rounded-lg p-4">
              <div className="text-red-600 text-sm font-medium">Error Rate</div>
              <div className="text-red-900 text-2xl font-bold">
                {summary.overall_error_rate?.toFixed(1) || 0}%
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Insights Panel */}
      {insights.length > 0 && (
        <div className="bg-white rounded-lg p-6 border">
          <div className="flex items-center mb-4">
            <div className="text-blue-600 text-xl mr-3">💡</div>
            <h3 className="text-lg font-semibold text-gray-900">Batch Insights</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {insights.map((insight: string, index: number) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3"></div>
                <p className="text-sm text-gray-700">{insight}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-8">
              <BatchSummaryChart data={batchData} />
              {routeComparison && Object.keys(routeComparison).length > 0 && (
                <RouteComparisonChart data={routeComparison} fileResults={fileResults} />
              )}
            </div>
          )}

          {activeTab === 'routes' && routeComparison && (
            <RouteComparisonChart 
              data={routeComparison} 
              fileResults={fileResults} 
              detailed={true} 
            />
          )}

          {activeTab === 'files' && (
            <FileComparisonTable 
              batchResults={batchData?.batch_results || []} 
              detailed={true} 
            />
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Performance Comparison</h3>
              {batchData?.batch_results?.map((result: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">{result.file_name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {result.analysis_results.route_analysis?.summary?.avg_response_time?.toFixed(0) || 0}ms
                      </div>
                      <div className="text-sm text-gray-500">Avg Response Time</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {result.analysis_results.route_analysis?.summary?.total_requests?.toLocaleString() || 0}
                      </div>
                      <div className="text-sm text-gray-500">Total Requests</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {result.analysis_results.route_analysis?.summary?.total_routes || 0}
                      </div>
                      <div className="text-sm text-gray-500">Routes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {result.analysis_results.summary?.error_rate?.toFixed(1) || 0}%
                      </div>
                      <div className="text-sm text-gray-500">Error Rate</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'insights' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Detailed Insights</h3>
              
              {/* Health Status Distribution */}
              {summary?.health_status_distribution && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Health Status Distribution</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(summary.health_status_distribution).map(([status, count]) => (
                      <div key={status} className="text-center">
                        <div className={`text-2xl font-bold ${
                          status === 'healthy' ? 'text-green-600' :
                          status === 'warning' ? 'text-yellow-600' :
                          status === 'critical' ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {count as number}
                        </div>
                        <div className="text-sm text-gray-500 capitalize">{status}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Format Distribution */}
              {summary?.format_distribution && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Log Format Distribution</h4>
                  <div className="space-y-2">
                    {Object.entries(summary.format_distribution).map(([format, count]) => (
                      <div key={format} className="flex justify-between items-center">
                        <span className="text-sm font-medium capitalize">{format.replace('_', ' ')}</span>
                        <span className="text-sm text-gray-600">{count as number} files</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
