'use client';

import { useAnalysis } from '@/context/AnalysisContext';

export default function Header() {
  const { currentAnalysis, setCurrentAnalysis } = useAnalysis();

  const handleNewAnalysis = () => {
    setCurrentAnalysis(null);
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900">
              🔍 Log Analyzer
            </h1>
            <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
              Enterprise Edition
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            {currentAnalysis && (
              <button
                onClick={handleNewAnalysis}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                New Analysis
              </button>
            )}
            
            <div className="text-sm text-gray-500">
              <span className="inline-flex items-center">
                <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                Backend Connected
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
