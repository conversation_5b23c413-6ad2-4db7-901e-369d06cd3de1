'use client';

import { useState, useRef } from 'react';
import { useAnalysis } from '@/context/AnalysisContext';

interface FileUploadProps {
  onAnalysisComplete: (analysisId: string) => void;
  onBatchAnalysisComplete?: (batchId: string, fileResults: any[]) => void;
}

export default function FileUpload({ onAnalysisComplete, onBatchAnalysisComplete }: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [batchMode, setBatchMode] = useState(false);
  const [batchProgress, setBatchProgress] = useState<{ [key: string]: number }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { setIsLoading } = useAnalysis();

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      const { valid, errors } = validateFiles(files);

      if (errors.length > 0) {
        setError(errors.join(' '));
        return;
      }

      setError(null);

      if (valid.length === 1) {
        handleFileUpload(valid[0]);
      } else {
        setSelectedFiles(valid);
        setBatchMode(true);
      }
    }
  };

  const validateFiles = (files: File[]): { valid: File[]; errors: string[] } => {
    const maxFileSize = 500 * 1024 * 1024; // 500MB
    const maxFiles = 50;
    const valid: File[] = [];
    const errors: string[] = [];

    if (files.length > maxFiles) {
      errors.push(`Too many files selected. Maximum ${maxFiles} files allowed, but ${files.length} were selected.`);
      return { valid, errors };
    }

    files.forEach(file => {
      if (file.size > maxFileSize) {
        errors.push(`${file.name} is too large (${(file.size / 1024 / 1024).toFixed(1)}MB). Maximum size is 500MB.`);
      } else {
        valid.push(file);
      }
    });

    return { valid, errors };
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      const { valid, errors } = validateFiles(fileArray);

      if (errors.length > 0) {
        setError(errors.join(' '));
        return;
      }

      setError(null);

      if (valid.length === 1) {
        handleFileUpload(valid[0]);
      } else {
        setSelectedFiles(valid);
        setBatchMode(true);
      }
    }
  };

  const handleFileUpload = async (file: File) => {
    setIsUploading(true);
    setIsLoading(true);
    setError(null);
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      setUploadProgress(100);

      // Simulate processing time for better UX
      setTimeout(() => {
        onAnalysisComplete(result.analysis_id);
        setIsUploading(false);
        setIsLoading(false);
      }, 1000);

    } catch (err) {
      let errorMessage = 'Upload failed';

      if (err instanceof Error) {
        errorMessage = err.message;
      }

      // Handle specific error types
      if (errorMessage.includes('413') || errorMessage.includes('File too large')) {
        errorMessage = `File too large. Please ensure your log file is under 500MB. Current file: ${(file.size / 1024 / 1024).toFixed(1)}MB`;
      } else if (errorMessage.includes('413') || errorMessage.includes('Too many files')) {
        errorMessage = 'Too many files selected. Maximum 50 files allowed in batch upload.';
      }

      setError(errorMessage);
      setIsUploading(false);
      setIsLoading(false);
      setUploadProgress(0);
    }
  };

  const handleBatchUpload = async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);
    setIsLoading(true);
    setError(null);
    setBatchProgress({});

    try {
      const results = [];

      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const formData = new FormData();
        formData.append('file', file);

        setBatchProgress(prev => ({ ...prev, [file.name]: 0 }));

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          let errorMessage = errorData.error || 'Upload failed';

          // Handle specific error cases
          if (response.status === 413) {
            if (errorData.message?.includes('File too large')) {
              errorMessage = `${file.name} is too large (${(file.size / 1024 / 1024).toFixed(1)}MB). Maximum size is 500MB.`;
            } else if (errorData.message?.includes('Too many files')) {
              errorMessage = 'Too many files selected. Maximum 50 files allowed.';
            }
          }

          throw new Error(errorMessage);
        }

        const result = await response.json();
        results.push({
          fileName: file.name,
          analysisId: result.analysis_id,
          summary: result.summary,
          fileInfo: result.file_info
        });

        setBatchProgress(prev => ({ ...prev, [file.name]: 100 }));
      }

      // Create batch analysis
      const batchResponse = await fetch('/api/batch-analysis', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          analyses: results.map(r => r.analysisId),
          fileNames: results.map(r => r.fileName)
        }),
      });

      if (!batchResponse.ok) {
        throw new Error('Failed to create batch analysis');
      }

      const batchResult = await batchResponse.json();

      if (onBatchAnalysisComplete) {
        onBatchAnalysisComplete(batchResult.batch_id, results);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Batch upload failed');
    } finally {
      setIsUploading(false);
      setIsLoading(false);
    }
  };

  const clearSelectedFiles = () => {
    setSelectedFiles([]);
    setBatchMode(false);
    setBatchProgress({});
    setError(null);
  };

  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    if (newFiles.length === 0) {
      setBatchMode(false);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  if (isUploading) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Analyzing Log File
            </h3>
            <p className="text-gray-600 mb-4">
              Processing your log file and generating insights...
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-500 mt-2">{uploadProgress}% complete</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Enterprise Log Analysis
        </h2>
        <p className="text-lg text-gray-600 mb-2">
          Upload your log files for intelligent analysis and insights
        </p>
        <p className="text-sm text-gray-500">
          Supports enterprise structured logs, JSON, Apache, Nginx, syslog, and more
        </p>
      </div>

      <div
        className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${isDragging
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="space-y-4">
          <div className="text-6xl">📄</div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Drop your log files here
            </h3>
            <p className="text-gray-600 mb-2">
              or click to browse and select files (single or multiple)
            </p>
            <p className="text-xs text-gray-500 mb-4">
              Maximum file size: 500MB • Maximum files: 50 • Supported formats: .log, .txt, .json, .csv
            </p>
            <button
              onClick={openFileDialog}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
            >
              Choose File
            </button>
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".log,.txt,.json,.csv"
          multiple
        />
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <div className="flex">
            <div className="text-red-400 text-xl">⚠️</div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">Upload Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              {error.includes('too large') && (
                <div className="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded">
                  💡 <strong>Tip:</strong> For very large log files, consider:
                  <ul className="mt-1 ml-4 list-disc">
                    <li>Splitting the file into smaller chunks</li>
                    <li>Compressing the file before upload</li>
                    <li>Using log rotation to manage file sizes</li>
                  </ul>
                </div>
              )}
              {error.includes('Too many files') && (
                <div className="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded">
                  💡 <strong>Tip:</strong> Select up to 50 files at a time for batch processing. You can run multiple batches if needed.
                </div>
              )}
            </div>
            <button
              onClick={() => setError(null)}
              className="text-red-400 hover:text-red-600 ml-2"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Batch Mode File Selection */}
      {batchMode && selectedFiles.length > 0 && (
        <div className="mt-6 bg-white rounded-lg border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Selected Files ({selectedFiles.length})
            </h3>
            <button
              onClick={clearSelectedFiles}
              className="text-gray-500 hover:text-gray-700 text-sm"
            >
              Clear All
            </button>
          </div>

          <div className="space-y-3 mb-6">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-600">📄</div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{file.name}</div>
                    <div className="text-xs text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {batchProgress[file.name] !== undefined && (
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${batchProgress[file.name]}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">
                        {batchProgress[file.name]}%
                      </span>
                    </div>
                  )}

                  {!isUploading && (
                    <button
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700 text-sm"
                    >
                      Remove
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleBatchUpload}
              disabled={isUploading || selectedFiles.length === 0}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
            >
              {isUploading ? 'Processing Files...' : `Analyze ${selectedFiles.length} Files`}
            </button>

            <button
              onClick={openFileDialog}
              disabled={isUploading}
              className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors"
            >
              Add More Files
            </button>
          </div>
        </div>
      )}

      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-blue-600 text-2xl mb-3">🏢</div>
          <h3 className="font-semibold text-gray-900 mb-2">Enterprise Structured</h3>
          <p className="text-sm text-gray-600">
            Advanced parsing for enterprise log formats with bracketed fields and structured data
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-green-600 text-2xl mb-3">📊</div>
          <h3 className="font-semibold text-gray-900 mb-2">Performance Analytics</h3>
          <p className="text-sm text-gray-600">
            Response time analysis, customer insights, and application performance monitoring
          </p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="text-purple-600 text-2xl mb-3">🔍</div>
          <h3 className="font-semibold text-gray-900 mb-2">Smart Detection</h3>
          <p className="text-sm text-gray-600">
            Automatic format detection, anomaly identification, and intelligent pattern recognition
          </p>
        </div>
      </div>

      {/* Batch Processing Features */}
      <div className="mt-6 bg-blue-50 rounded-lg p-6 border border-blue-200">
        <h4 className="text-md font-medium text-blue-900 mb-3">🚀 New: Batch Processing</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Upload multiple log files simultaneously for batch analysis</li>
          <li>• Comparative analysis across different log files</li>
          <li>• Route-specific performance metrics and insights</li>
          <li>• Aggregated reporting with side-by-side comparisons</li>
          <li>• Enhanced enterprise log parsing with route extraction</li>
        </ul>
      </div>
    </div>
  );
}
