'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

interface FileComparisonTableProps {
  batchResults: any[];
  detailed?: boolean;
}

export default function FileComparisonTable({ batchResults, detailed = false }: FileComparisonTableProps) {
  const [sortBy, setSortBy] = useState<string>('file_name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const router = useRouter();

  if (!batchResults || batchResults.length === 0) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">File Comparison</h3>
        <p className="text-gray-500">No files to compare</p>
      </div>
    );
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getSortValue = (result: any, column: string) => {
    switch (column) {
      case 'file_name':
        return result.file_name || '';
      case 'total_entries':
        return result.analysis_results.summary?.total_entries || 0;
      case 'error_count':
        return result.analysis_results.summary?.error_count || 0;
      case 'error_rate':
        return result.analysis_results.summary?.error_rate || 0;
      case 'health_status':
        return result.analysis_results.summary?.health_status || 'unknown';
      case 'total_routes':
        return result.analysis_results.route_analysis?.summary?.total_routes || 0;
      case 'total_requests':
        return result.analysis_results.route_analysis?.summary?.total_requests || 0;
      case 'avg_response_time':
        return result.analysis_results.route_analysis?.summary?.avg_response_time || 0;
      case 'file_size':
        return result.file_info?.size || 0;
      default:
        return '';
    }
  };

  const sortedResults = [...batchResults].sort((a, b) => {
    const aValue = getSortValue(a, sortBy);
    const bValue = getSortValue(b, sortBy);
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    return sortOrder === 'asc' 
      ? (aValue as number) - (bValue as number)
      : (bValue as number) - (aValue as number);
  });

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) {
      return <span className="text-gray-400">↕️</span>;
    }
    return <span>{sortOrder === 'asc' ? '↑' : '↓'}</span>;
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const viewIndividualAnalysis = (analysisId: string) => {
    router.push(`/analysis/${analysisId}`);
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">File Analysis Comparison</h3>
        <div className="text-sm text-gray-500">
          {batchResults.length} files analyzed
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('file_name')}
              >
                File Name <SortIcon column="file_name" />
              </th>
              
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('health_status')}
              >
                Health <SortIcon column="health_status" />
              </th>
              
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('total_entries')}
              >
                Entries <SortIcon column="total_entries" />
              </th>
              
              <th 
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('error_rate')}
              >
                Error Rate <SortIcon column="error_rate" />
              </th>

              {detailed && (
                <>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('total_routes')}
                  >
                    Routes <SortIcon column="total_routes" />
                  </th>
                  
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('total_requests')}
                  >
                    Requests <SortIcon column="total_requests" />
                  </th>
                  
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('avg_response_time')}
                  >
                    Avg Response <SortIcon column="avg_response_time" />
                  </th>
                  
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('file_size')}
                  >
                    File Size <SortIcon column="file_size" />
                  </th>
                </>
              )}
              
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Actions
              </th>
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedResults.map((result, index) => {
              const summary = result.analysis_results.summary;
              const routeAnalysis = result.analysis_results.route_analysis;
              
              return (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center">
                      <div className="text-blue-600 mr-2">📄</div>
                      <div>
                        <div className="font-medium">{result.file_name}</div>
                        <div className="text-xs text-gray-500">
                          {result.file_info?.format || 'Unknown format'}
                        </div>
                      </div>
                    </div>
                  </td>
                  
                  <td className="px-4 py-4 text-sm">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getHealthStatusColor(summary?.health_status || 'unknown')}`}>
                      {summary?.health_status || 'Unknown'}
                    </span>
                  </td>
                  
                  <td className="px-4 py-4 text-sm text-gray-900">
                    {summary?.total_entries?.toLocaleString() || 0}
                  </td>
                  
                  <td className="px-4 py-4 text-sm">
                    <div className="flex items-center">
                      <span className={`font-medium ${
                        (summary?.error_rate || 0) > 10 ? 'text-red-600' :
                        (summary?.error_rate || 0) > 5 ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {summary?.error_rate?.toFixed(1) || 0}%
                      </span>
                      <span className="ml-1 text-xs text-gray-500">
                        ({summary?.error_count || 0})
                      </span>
                    </div>
                  </td>

                  {detailed && (
                    <>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {routeAnalysis?.summary?.total_routes || 0}
                      </td>
                      
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {routeAnalysis?.summary?.total_requests?.toLocaleString() || 0}
                      </td>
                      
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {routeAnalysis?.summary?.avg_response_time > 0 
                          ? `${routeAnalysis.summary.avg_response_time.toFixed(0)}ms`
                          : 'N/A'
                        }
                      </td>
                      
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {result.file_info?.size ? formatFileSize(result.file_info.size) : 'N/A'}
                      </td>
                    </>
                  )}
                  
                  <td className="px-4 py-4 text-sm">
                    <button
                      onClick={() => viewIndividualAnalysis(result.analysis_id)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Summary Row */}
      <div className="mt-4 p-4 bg-gray-50 rounded border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-gray-900">
              {batchResults.reduce((sum, result) => sum + (result.analysis_results.summary?.total_entries || 0), 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">Total Entries</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-red-600">
              {batchResults.reduce((sum, result) => sum + (result.analysis_results.summary?.error_count || 0), 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">Total Errors</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-purple-600">
              {batchResults.reduce((sum, result) => sum + (result.analysis_results.route_analysis?.summary?.total_routes || 0), 0)}
            </div>
            <div className="text-sm text-gray-500">Total Routes</div>
          </div>
          
          <div>
            <div className="text-lg font-bold text-green-600">
              {batchResults.reduce((sum, result) => sum + (result.analysis_results.route_analysis?.summary?.total_requests || 0), 0).toLocaleString()}
            </div>
            <div className="text-sm text-gray-500">Total Requests</div>
          </div>
        </div>
      </div>
    </div>
  );
}
