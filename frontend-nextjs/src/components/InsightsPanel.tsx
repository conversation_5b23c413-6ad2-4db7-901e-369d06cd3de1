'use client';

import React from 'react';

interface InsightsPanelProps {
  data: any;
}

export default function InsightsPanel({ data }: InsightsPanelProps) {
  if (!data) {
    return null;
  }

  // Extract insights from various analysis results
  const insights: string[] = [];
  const recommendations: string[] = [];

  // Summary insights
  const summary = data.summary?.summary;
  if (summary) {
    if (summary.health_status === 'critical') {
      insights.push(`🚨 System health is CRITICAL with ${summary.error_rate}% error rate`);
      recommendations.push('Immediate action required: Investigate and resolve critical errors');
    } else if (summary.health_status === 'warning') {
      insights.push(`⚠️ System health needs attention with ${summary.error_rate}% error rate`);
      recommendations.push('Monitor error trends closely and implement error reduction strategies');
    } else if (summary.health_status === 'healthy') {
      insights.push(`✅ System appears healthy with low error rate (${summary.error_rate}%)`);
    }

    if (summary.total_entries > 100000) {
      insights.push(`📊 High volume system with ${summary.total_entries.toLocaleString()} log entries`);
    } else if (summary.total_entries < 100) {
      insights.push(`📉 Low activity detected with only ${summary.total_entries} log entries`);
    }
  }

  // Error analysis insights
  const errorAnalysis = data.errorAnalysis?.error_analysis;
  if (errorAnalysis && errorAnalysis.total_errors > 0) {
    const topErrorType = Object.entries(errorAnalysis.error_types || {})
      .sort(([,a], [,b]) => (b as number) - (a as number))[0];
    if (topErrorType) {
      insights.push(`🔍 Most common error type: ${topErrorType[0]} (${topErrorType[1]} occurrences)`);
    }
  }

  // Performance insights
  const perfAnalysis = data.performanceAnalysis?.performance_analysis;
  if (perfAnalysis && perfAnalysis.total_requests > 0) {
    const avgTime = perfAnalysis.avg_response_time;
    const slowCount = perfAnalysis.slow_request_count;

    if (avgTime > 2000) {
      insights.push(`🐌 High average response time: ${avgTime.toFixed(0)}ms`);
      recommendations.push('Consider performance optimization to reduce response times');
    } else if (avgTime < 500) {
      insights.push(`⚡ Excellent performance: Average response time is ${avgTime.toFixed(0)}ms`);
    }

    if (slowCount > 0) {
      insights.push(`⏱️ Found ${slowCount} slow requests (>5 seconds) that need investigation`);
      if (slowCount > perfAnalysis.total_requests * 0.05) {
        recommendations.push('Investigate and optimize slow requests (>5% of requests are slow)');
      }
    }
  }

  // Customer insights
  const customerAnalysis = data.customerAnalysis?.customer_analysis;
  if (customerAnalysis && customerAnalysis.total_unique_customers > 0) {
    insights.push(`👥 Serving ${customerAnalysis.total_unique_customers} unique customers`);

    if (customerAnalysis.error_prone_customers?.length > 0) {
      const topErrorCustomer = customerAnalysis.error_prone_customers[0];
      insights.push(`🚨 Customer ${topErrorCustomer.customer_id} has ${topErrorCustomer.error_count} errors`);
      recommendations.push('Provide additional support to customers experiencing frequent errors');
    }
  }

  // Application insights
  const appAnalysis = data.applicationAnalysis?.application_analysis;
  if (appAnalysis && appAnalysis.total_applications > 1) {
    insights.push(`📱 Monitoring ${appAnalysis.total_applications} different applications`);

    const apps = appAnalysis.application_summary || [];
    if (apps.length > 0) {
      const highestErrorApp = apps.reduce((max: any, app: any) => 
        app.error_rate > max.error_rate ? app : max
      );
      if (highestErrorApp.error_rate > 5) {
        insights.push(`⚠️ Application '${highestErrorApp.app_name}' has high error rate: ${highestErrorApp.error_rate}%`);
        if (highestErrorApp.error_rate > 10) {
          recommendations.push(`Investigate high error rate in application '${highestErrorApp.app_name}'`);
        }
      }
    }
  }

  // Time analysis insights
  const timeAnalysis = data.timeAnalysis?.time_analysis;
  if (timeAnalysis) {
    const peakHour = timeAnalysis.peak_hour;
    if (peakHour !== null) {
      insights.push(`📈 Peak activity occurs at ${peakHour.toString().padStart(2, '0')}:00`);
    }

    const totalDays = timeAnalysis.total_days;
    if (totalDays > 1) {
      insights.push(`📅 Data spans ${totalDays} days`);
    }
  }

  // Pattern insights
  const patternAnalysis = data.patternAnalysis?.pattern_analysis;
  if (patternAnalysis) {
    const commonMessages = patternAnalysis.common_messages || [];
    if (commonMessages.length > 0) {
      insights.push(`🔍 Found ${commonMessages.length} recurring message patterns`);
    }

    const ipAnalysis = patternAnalysis.ip_analysis;
    if (ipAnalysis && ipAnalysis.total_unique_ips > 0) {
      insights.push(`🌐 ${ipAnalysis.total_unique_ips} unique IP addresses detected`);
    }
  }

  // General recommendations
  if (summary && summary.total_entries > 10000) {
    recommendations.push('Consider implementing log rotation and archival for large log files');
  }

  // If no specific insights, add general ones
  if (insights.length === 0) {
    insights.push('📊 Analysis completed successfully');
    insights.push('🔍 Review the detailed charts and tables for more insights');
  }

  if (recommendations.length === 0) {
    recommendations.push('Continue monitoring system health and performance');
    recommendations.push('Set up alerts for error rate thresholds');
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Insights */}
      <div className="bg-white rounded-lg p-6 border">
        <div className="flex items-center mb-4">
          <div className="text-blue-600 text-xl mr-3">💡</div>
          <h3 className="text-lg font-semibold text-gray-900">Key Insights</h3>
        </div>
        
        <div className="space-y-3">
          {insights.slice(0, 8).map((insight, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3"></div>
              <p className="text-sm text-gray-700">{insight}</p>
            </div>
          ))}
        </div>

        {insights.length > 8 && (
          <div className="mt-4 text-sm text-gray-500">
            And {insights.length - 8} more insights available in detailed views...
          </div>
        )}
      </div>

      {/* Recommendations */}
      <div className="bg-white rounded-lg p-6 border">
        <div className="flex items-center mb-4">
          <div className="text-green-600 text-xl mr-3">🎯</div>
          <h3 className="text-lg font-semibold text-gray-900">Recommendations</h3>
        </div>
        
        <div className="space-y-3">
          {recommendations.slice(0, 6).map((recommendation, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 w-2 h-2 bg-green-400 rounded-full mt-2 mr-3"></div>
              <p className="text-sm text-gray-700">{recommendation}</p>
            </div>
          ))}
        </div>

        {recommendations.length > 6 && (
          <div className="mt-4 text-sm text-gray-500">
            And {recommendations.length - 6} more recommendations...
          </div>
        )}
      </div>
    </div>
  );
}
