'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface PatternAnalysisChartProps {
  data: any;
  detailed?: boolean;
}

export default function PatternAnalysisChart({ data, detailed = false }: PatternAnalysisChartProps) {
  if (!data || !data.pattern_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Pattern Analysis</h3>
        <p className="text-gray-500">No pattern data available</p>
      </div>
    );
  }

  const patternAnalysis = data.pattern_analysis;

  // Common messages chart
  const commonMessages = patternAnalysis.common_messages || [];
  const messageChartData = {
    labels: commonMessages.slice(0, 10).map((msg: any, index: number) => 
      `Message ${index + 1}`
    ),
    datasets: [
      {
        label: 'Occurrences',
        data: commonMessages.slice(0, 10).map((msg: any) => msg.count),
        backgroundColor: 'rgba(139, 92, 246, 0.6)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const messageChartOptions = {
    responsive: true,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Most Common Log Messages',
      },
      tooltip: {
        callbacks: {
          afterLabel: function(context: any) {
            const messageIndex = context.dataIndex;
            const message = commonMessages[messageIndex]?.message || '';
            return message.length > 100 ? message.substring(0, 100) + '...' : message;
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // IP analysis
  const ipAnalysis = patternAnalysis.ip_analysis || {};
  const topIps = ipAnalysis.top_ips || [];

  // Status patterns
  const statusPatterns = patternAnalysis.status_patterns || {};
  const statusDistribution = statusPatterns.status_distribution || {};
  const statusCategories = statusPatterns.status_categories || {};

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Pattern Analysis</h3>
        <div className="text-sm text-gray-500">
          {commonMessages.length} patterns identified
        </div>
      </div>

      {/* Pattern Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-purple-600 text-sm font-medium">Common Messages</div>
          <div className="text-purple-900 text-lg font-bold">
            {commonMessages.length}
          </div>
          <div className="text-purple-600 text-xs">
            Recurring patterns found
          </div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Unique IPs</div>
          <div className="text-blue-900 text-lg font-bold">
            {ipAnalysis.total_unique_ips || 0}
          </div>
          <div className="text-blue-600 text-xs">
            Different IP addresses
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Status Codes</div>
          <div className="text-green-900 text-lg font-bold">
            {Object.keys(statusDistribution).length}
          </div>
          <div className="text-green-600 text-xs">
            Different response codes
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="space-y-6">
        {/* Common Messages Chart */}
        {commonMessages.length > 0 && (
          <div>
            <Bar data={messageChartData} options={messageChartOptions} />
          </div>
        )}

        {/* Detailed Tables */}
        {detailed && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top IPs */}
            {topIps.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Top IP Addresses</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          IP Address
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Requests
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {topIps.slice(0, 10).map((ip: any, index: number) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm font-mono text-gray-900">
                            {ip.ip}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {ip.count.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Status Code Categories */}
            {Object.keys(statusCategories).length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">HTTP Status Categories</h4>
                <div className="space-y-2">
                  {Object.entries(statusCategories).map(([category, count]) => (
                    <div key={category} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center">
                        <span className={`w-3 h-3 rounded-full mr-3 ${
                          category === '2xx' ? 'bg-green-400' :
                          category === '3xx' ? 'bg-blue-400' :
                          category === '4xx' ? 'bg-yellow-400' :
                          category === '5xx' ? 'bg-red-400' : 'bg-gray-400'
                        }`}></span>
                        <span className="text-sm font-medium">{category} Status Codes</span>
                      </div>
                      <span className="text-sm text-gray-600">{count as number} requests</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Common Messages Details */}
        {detailed && commonMessages.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Message Pattern Details</h4>
            <div className="space-y-3">
              {commonMessages.slice(0, 5).map((msg: any, index: number) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-purple-600">
                      Pattern #{index + 1}
                    </span>
                    <div className="text-sm text-gray-600">
                      <span className="font-medium">{msg.count}</span> occurrences 
                      <span className="ml-2">({msg.percentage}%)</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-700 font-mono bg-white p-3 rounded border">
                    {msg.message}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Pattern Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Pattern Insights</h4>
            <div className="space-y-2 text-sm">
              {commonMessages.length > 0 && (
                <div className="flex items-center">
                  <span className="text-purple-600 mr-2">🔍</span>
                  <span>Found {commonMessages.length} recurring message patterns</span>
                </div>
              )}
              
              {ipAnalysis.total_unique_ips > 0 && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">🌐</span>
                  <span>{ipAnalysis.total_unique_ips} unique IP addresses detected</span>
                </div>
              )}
              
              {Object.keys(statusDistribution).length > 0 && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-2">📊</span>
                  <span>{Object.keys(statusDistribution).length} different HTTP status codes</span>
                </div>
              )}

              {/* Success rate insight */}
              {statusCategories['2xx'] && statusCategories['4xx'] && (
                <div className="flex items-center">
                  <span className="text-yellow-600 mr-2">✅</span>
                  <span>
                    Success rate: {(
                      (statusCategories['2xx'] / 
                      (statusCategories['2xx'] + statusCategories['4xx'] + (statusCategories['5xx'] || 0))) * 100
                    ).toFixed(1)}%
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
