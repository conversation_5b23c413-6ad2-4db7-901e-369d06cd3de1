'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface RouteAnalysisChartProps {
  data: any;
  detailed?: boolean;
}

export default function RouteAnalysisChart({ data, detailed = false }: RouteAnalysisChartProps) {
  if (!data || !data.route_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Route Analysis</h3>
        <p className="text-gray-500">No route data available (requires enterprise structured logs with method and originUrl fields)</p>
      </div>
    );
  }

  const routeAnalysis = data.route_analysis;
  const routes = routeAnalysis.routes || {};
  const summary = routeAnalysis.summary || {};
  const topRoutes = routeAnalysis.top_routes || [];
  const slowRoutes = routeAnalysis.slow_routes || [];
  const errorProneRoutes = routeAnalysis.error_prone_routes || [];

  // Top routes by request volume
  const topRoutesData = {
    labels: topRoutes.slice(0, 10).map((route: any) => 
      route.route.length > 30 ? route.route.substring(0, 30) + '...' : route.route
    ),
    datasets: [
      {
        label: 'Request Count',
        data: topRoutes.slice(0, 10).map((route: any) => route.total_requests),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const topRoutesOptions = {
    responsive: true,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Top Routes by Request Volume',
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Response time by route
  const responseTimeData = {
    labels: slowRoutes.slice(0, 10).map((route: any) => 
      route.route.length > 30 ? route.route.substring(0, 30) + '...' : route.route
    ),
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: slowRoutes.slice(0, 10).map((route: any) => route.avg_response_time),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
      },
    ],
  };

  const responseTimeOptions = {
    responsive: true,
    indexAxis: 'y' as const,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Slowest Routes by Response Time',
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Route Analysis</h3>
        <div className="text-sm text-gray-500">
          {summary.total_routes || 0} routes analyzed
        </div>
      </div>

      {/* Route Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Routes</div>
          <div className="text-blue-900 text-lg font-bold">
            {summary.total_routes || 0}
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Total Requests</div>
          <div className="text-green-900 text-lg font-bold">
            {summary.total_requests?.toLocaleString() || 0}
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="text-yellow-600 text-sm font-medium">Avg Response</div>
          <div className="text-yellow-900 text-lg font-bold">
            {summary.avg_response_time?.toFixed(0) || 0}ms
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Avg Error Rate</div>
          <div className="text-red-900 text-lg font-bold">
            {summary.avg_error_rate?.toFixed(1) || 0}%
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-8">
        {/* Top Routes Chart */}
        {topRoutes.length > 0 && (
          <div>
            <Bar data={topRoutesData} options={topRoutesOptions} />
          </div>
        )}

        {/* Response Time Chart */}
        {detailed && slowRoutes.length > 0 && (
          <div>
            <Bar data={responseTimeData} options={responseTimeOptions} />
          </div>
        )}

        {/* Route Details Tables */}
        {detailed && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Routes Table */}
            {topRoutes.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Most Active Routes</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Route
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Requests
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Avg Time
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Error Rate
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {topRoutes.slice(0, 10).map((route: any, index: number) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm font-mono text-gray-900">
                            {route.route.length > 40 ? route.route.substring(0, 40) + '...' : route.route}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {route.total_requests.toLocaleString()}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {route.avg_response_time > 0 ? `${route.avg_response_time.toFixed(0)}ms` : 'N/A'}
                          </td>
                          <td className="px-4 py-2 text-sm">
                            <span className={`font-medium ${
                              route.error_rate > 10 ? 'text-red-600' :
                              route.error_rate > 5 ? 'text-yellow-600' :
                              'text-green-600'
                            }`}>
                              {route.error_rate.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Error-Prone Routes Table */}
            {errorProneRoutes.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Routes with Errors</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Route
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Error Rate
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Errors
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Total Req
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {errorProneRoutes.slice(0, 10).map((route: any, index: number) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm font-mono text-gray-900">
                            {route.route.length > 40 ? route.route.substring(0, 40) + '...' : route.route}
                          </td>
                          <td className="px-4 py-2 text-sm text-red-600 font-medium">
                            {route.error_rate.toFixed(1)}%
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {route.error_count}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {route.total_requests.toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Route Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Route Insights</h4>
            <div className="space-y-2 text-sm">
              {summary.total_routes > 0 && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">🛣️</span>
                  <span>Analyzed {summary.total_routes} unique API routes</span>
                </div>
              )}
              
              {summary.total_requests > 0 && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-2">📊</span>
                  <span>{summary.total_requests.toLocaleString()} total API requests processed</span>
                </div>
              )}
              
              {summary.avg_response_time > 0 && (
                <div className="flex items-center">
                  <span className={`mr-2 ${summary.avg_response_time > 2000 ? 'text-red-600' : summary.avg_response_time > 1000 ? 'text-yellow-600' : 'text-green-600'}`}>
                    {summary.avg_response_time > 2000 ? '🐌' : summary.avg_response_time > 1000 ? '⚠️' : '⚡'}
                  </span>
                  <span>Average response time: {summary.avg_response_time.toFixed(0)}ms</span>
                </div>
              )}

              {errorProneRoutes.length > 0 && (
                <div className="flex items-center">
                  <span className="text-red-600 mr-2">🚨</span>
                  <span>{errorProneRoutes.length} routes have error rates above 0%</span>
                </div>
              )}

              {slowRoutes.length > 0 && slowRoutes[0].avg_response_time > 5000 && (
                <div className="flex items-center">
                  <span className="text-orange-600 mr-2">⏱️</span>
                  <span>Some routes have very slow response times (&gt;5s)</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
