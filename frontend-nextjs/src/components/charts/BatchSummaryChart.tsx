'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface BatchSummaryChartProps {
  data: any;
}

export default function BatchSummaryChart({ data }: BatchSummaryChartProps) {
  if (!data || !data.batch_results) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Batch Summary</h3>
        <p className="text-gray-500">No batch data available</p>
      </div>
    );
  }

  const batchResults = data.batch_results;
  const summary = data.comparative_analysis?.summary_comparison;

  // File comparison chart data
  const fileComparisonData = {
    labels: batchResults.map((result: any) => result.file_name),
    datasets: [
      {
        label: 'Total Entries',
        data: batchResults.map((result: any) => result.analysis_results.summary?.total_entries || 0),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Error Count',
        data: batchResults.map((result: any) => result.analysis_results.summary?.error_count || 0),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
      }
    ],
  };

  const fileComparisonOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Log Entries and Errors by File',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Health status distribution
  const healthStatusData = {
    labels: Object.keys(summary?.health_status_distribution || {}),
    datasets: [
      {
        data: Object.values(summary?.health_status_distribution || {}),
        backgroundColor: [
          '#22c55e', // healthy - green
          '#f59e0b', // warning - yellow
          '#ef4444', // critical - red
          '#6b7280', // unknown - gray
        ],
        borderWidth: 1,
      },
    ],
  };

  const healthStatusOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      title: {
        display: true,
        text: 'Health Status Distribution',
      },
    },
  };

  // Response time comparison
  const responseTimeData = {
    labels: batchResults.map((result: any) => result.file_name),
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: batchResults.map((result: any) => 
          result.analysis_results.route_analysis?.summary?.avg_response_time || 0
        ),
        backgroundColor: 'rgba(34, 197, 94, 0.6)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
      },
    ],
  };

  const responseTimeOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Average Response Time by File',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
    },
  };

  // Route count comparison
  const routeCountData = {
    labels: batchResults.map((result: any) => result.file_name),
    datasets: [
      {
        label: 'Route Count',
        data: batchResults.map((result: any) => 
          result.analysis_results.route_analysis?.summary?.total_routes || 0
        ),
        backgroundColor: 'rgba(139, 92, 246, 0.6)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const routeCountOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'API Routes Count by File',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Batch Analysis Summary</h3>
        <div className="text-sm text-gray-500">
          {batchResults.length} files analyzed
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* File Comparison */}
        <div>
          <Bar data={fileComparisonData} options={fileComparisonOptions} />
        </div>

        {/* Health Status Distribution */}
        {summary?.health_status_distribution && Object.keys(summary.health_status_distribution).length > 0 && (
          <div>
            <Doughnut data={healthStatusData} options={healthStatusOptions} />
          </div>
        )}

        {/* Response Time Comparison */}
        {batchResults.some((result: any) => result.analysis_results.route_analysis?.summary?.avg_response_time > 0) && (
          <div>
            <Bar data={responseTimeData} options={responseTimeOptions} />
          </div>
        )}

        {/* Route Count Comparison */}
        {batchResults.some((result: any) => result.analysis_results.route_analysis?.summary?.total_routes > 0) && (
          <div>
            <Bar data={routeCountData} options={routeCountOptions} />
          </div>
        )}
      </div>

      {/* Summary Statistics */}
      <div className="mt-8 bg-gray-50 rounded-lg p-4">
        <h4 className="text-md font-medium text-gray-900 mb-3">Aggregate Statistics</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {summary?.total_entries?.toLocaleString() || 0}
            </div>
            <div className="text-sm text-gray-500">Total Log Entries</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {summary?.total_errors?.toLocaleString() || 0}
            </div>
            <div className="text-sm text-gray-500">Total Errors</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {summary?.total_requests?.toLocaleString() || 0}
            </div>
            <div className="text-sm text-gray-500">API Requests</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {summary?.total_routes || 0}
            </div>
            <div className="text-sm text-gray-500">Unique Routes</div>
          </div>
        </div>
      </div>

      {/* File Performance Summary */}
      <div className="mt-6">
        <h4 className="text-md font-medium text-gray-900 mb-3">File Performance Summary</h4>
        <div className="space-y-2">
          {batchResults.map((result: any, index: number) => {
            const summary = result.analysis_results.summary;
            const routeAnalysis = result.analysis_results.route_analysis;
            
            return (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    summary?.health_status === 'healthy' ? 'bg-green-400' :
                    summary?.health_status === 'warning' ? 'bg-yellow-400' :
                    summary?.health_status === 'critical' ? 'bg-red-400' : 'bg-gray-400'
                  }`}></div>
                  <span className="text-sm font-medium text-gray-900">{result.file_name}</span>
                </div>
                
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <span>{summary?.total_entries?.toLocaleString() || 0} entries</span>
                  <span>{summary?.error_rate?.toFixed(1) || 0}% errors</span>
                  {routeAnalysis?.summary?.total_routes > 0 && (
                    <span>{routeAnalysis.summary.total_routes} routes</span>
                  )}
                  {routeAnalysis?.summary?.avg_response_time > 0 && (
                    <span>{routeAnalysis.summary.avg_response_time.toFixed(0)}ms avg</span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
