'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface ApplicationChartProps {
  data: any;
  detailed?: boolean;
}

export default function ApplicationChart({ data, detailed = false }: ApplicationChartProps) {
  if (!data || !data.application_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Analysis</h3>
        <p className="text-gray-500">No application data available (requires enterprise structured logs)</p>
      </div>
    );
  }

  const appAnalysis = data.application_analysis;
  const applicationSummary = appAnalysis.application_summary || [];
  const totalApplications = appAnalysis.total_applications || 0;

  // Application activity chart
  const activityChartData = {
    labels: applicationSummary.slice(0, 10).map((app: any) => app.app_name),
    datasets: [
      {
        label: 'Activity Count',
        data: applicationSummary.slice(0, 10).map((app: any) => app.activity_count),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const activityChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Application Activity',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Error rate distribution
  const errorRateData = {
    labels: applicationSummary.map((app: any) => app.app_name),
    datasets: [
      {
        data: applicationSummary.map((app: any) => app.error_rate),
        backgroundColor: [
          '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
          '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9'
        ],
        borderWidth: 1,
      },
    ],
  };

  const errorRateOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      title: {
        display: true,
        text: 'Error Rate by Application',
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.parsed}% error rate`;
          }
        }
      }
    },
  };

  // Performance chart
  const performanceChartData = {
    labels: applicationSummary
      .filter((app: any) => app.avg_response_time > 0)
      .map((app: any) => app.app_name),
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: applicationSummary
          .filter((app: any) => app.avg_response_time > 0)
          .map((app: any) => app.avg_response_time),
        backgroundColor: 'rgba(34, 197, 94, 0.6)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
      },
    ],
  };

  const performanceChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Average Response Time by Application',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Application Analysis</h3>
        <div className="text-sm text-gray-500">
          {totalApplications} applications monitored
        </div>
      </div>

      {/* Application Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Apps</div>
          <div className="text-blue-900 text-lg font-bold">
            {totalApplications}
          </div>
          <div className="text-blue-600 text-xs">
            Applications monitored
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Most Active</div>
          <div className="text-green-900 text-lg font-bold">
            {applicationSummary[0]?.app_name || 'N/A'}
          </div>
          <div className="text-green-600 text-xs">
            Highest activity app
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Highest Error Rate</div>
          <div className="text-red-900 text-lg font-bold">
            {Math.max(...applicationSummary.map((app: any) => app.error_rate)).toFixed(1)}%
          </div>
          <div className="text-red-600 text-xs">
            Maximum error rate
          </div>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-purple-600 text-sm font-medium">Avg Modules</div>
          <div className="text-purple-900 text-lg font-bold">
            {applicationSummary.length > 0 
              ? Math.round(applicationSummary.reduce((sum: number, app: any) => sum + app.module_count, 0) / applicationSummary.length)
              : 0
            }
          </div>
          <div className="text-purple-600 text-xs">
            Modules per application
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Application Activity */}
        {applicationSummary.length > 0 && (
          <div>
            <Bar data={activityChartData} options={activityChartOptions} />
          </div>
        )}

        {/* Error Rate and Performance Charts */}
        {detailed && applicationSummary.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Error Rate Distribution */}
            <div>
              <Doughnut data={errorRateData} options={errorRateOptions} />
            </div>

            {/* Performance Chart */}
            {applicationSummary.some((app: any) => app.avg_response_time > 0) && (
              <div>
                <Bar data={performanceChartData} options={performanceChartOptions} />
              </div>
            )}
          </div>
        )}

        {/* Application Details Table */}
        {detailed && applicationSummary.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Application Details</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Application
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Activity
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Errors
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Error Rate
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Avg Response
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Modules
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {applicationSummary.map((app: any, index: number) => (
                    <tr key={index}>
                      <td className="px-4 py-2 text-sm font-medium text-gray-900">
                        {app.app_name}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {app.activity_count.toLocaleString()}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {app.error_count}
                      </td>
                      <td className="px-4 py-2 text-sm">
                        <span className={`font-medium ${
                          app.error_rate > 10 ? 'text-red-600' :
                          app.error_rate > 5 ? 'text-yellow-600' :
                          'text-green-600'
                        }`}>
                          {app.error_rate.toFixed(1)}%
                        </span>
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {app.avg_response_time > 0 ? `${app.avg_response_time.toFixed(0)}ms` : 'N/A'}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-900">
                        {app.module_count}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Application Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Application Insights</h4>
            <div className="space-y-2 text-sm">
              {totalApplications > 0 && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">📱</span>
                  <span>Monitoring {totalApplications} applications</span>
                </div>
              )}
              
              {applicationSummary.length > 0 && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-2">📈</span>
                  <span>
                    Most active: {applicationSummary[0]?.app_name} 
                    ({applicationSummary[0]?.activity_count.toLocaleString()} activities)
                  </span>
                </div>
              )}
              
              {/* High error rate apps */}
              {applicationSummary.some((app: any) => app.error_rate > 10) && (
                <div className="flex items-center">
                  <span className="text-red-600 mr-2">⚠️</span>
                  <span>
                    {applicationSummary.filter((app: any) => app.error_rate > 10).length} 
                    {' '}applications have high error rates (&gt;10%)
                  </span>
                </div>
              )}

              {/* Performance insights */}
              {applicationSummary.some((app: any) => app.avg_response_time > 2000) && (
                <div className="flex items-center">
                  <span className="text-orange-600 mr-2">🐌</span>
                  <span>
                    Some applications have slow response times (&gt;2s)
                  </span>
                </div>
              )}

              {/* Module complexity */}
              {applicationSummary.some((app: any) => app.module_count > 5) && (
                <div className="flex items-center">
                  <span className="text-purple-600 mr-2">🔧</span>
                  <span>
                    Complex applications with multiple modules detected
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
