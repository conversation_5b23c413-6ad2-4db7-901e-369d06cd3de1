'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface TimelineChartProps {
  data: any;
  detailed?: boolean;
}

export default function TimelineChart({ data, detailed = false }: TimelineChartProps) {
  if (!data || !data.time_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline Analysis</h3>
        <p className="text-gray-500">No timeline data available</p>
      </div>
    );
  }

  const timeAnalysis = data.time_analysis;

  // Hourly distribution chart
  const hourlyData = timeAnalysis.hourly_distribution || {};
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const hourlyCounts = hours.map(hour => hourlyData[hour] || 0);

  const hourlyChartData = {
    labels: hours.map(hour => `${hour.toString().padStart(2, '0')}:00`),
    datasets: [
      {
        label: 'Log Entries',
        data: hourlyCounts,
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const hourlyChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Activity by Hour of Day',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Daily distribution chart
  const dailyData = timeAnalysis.daily_distribution || {};
  const dailyLabels = Object.keys(dailyData).sort();
  const dailyCounts = dailyLabels.map(date => dailyData[date]);

  const dailyChartData = {
    labels: dailyLabels.map(date => new Date(date).toLocaleDateString()),
    datasets: [
      {
        label: 'Daily Activity',
        data: dailyCounts,
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 2,
        fill: false,
      },
    ],
  };

  const dailyChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Activity Over Time',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  const peakHour = timeAnalysis.peak_hour;
  const totalDays = timeAnalysis.total_days || 0;
  const avgEntriesPerHour = timeAnalysis.avg_entries_per_hour || 0;

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Timeline Analysis</h3>
        <div className="text-sm text-gray-500">
          {totalDays} days analyzed
        </div>
      </div>

      {/* Timeline Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Peak Hour</div>
          <div className="text-blue-900 text-lg font-bold">
            {peakHour !== null ? `${peakHour.toString().padStart(2, '0')}:00` : 'N/A'}
          </div>
          <div className="text-blue-600 text-xs">
            Highest activity period
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Avg/Hour</div>
          <div className="text-green-900 text-lg font-bold">
            {Math.round(avgEntriesPerHour).toLocaleString()}
          </div>
          <div className="text-green-600 text-xs">
            Average entries per hour
          </div>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-purple-600 text-sm font-medium">Time Span</div>
          <div className="text-purple-900 text-lg font-bold">
            {totalDays} {totalDays === 1 ? 'day' : 'days'}
          </div>
          <div className="text-purple-600 text-xs">
            Analysis period
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Hourly Distribution */}
        <div>
          <Bar data={hourlyChartData} options={hourlyChartOptions} />
        </div>

        {/* Daily Distribution (only if detailed and multiple days) */}
        {detailed && dailyLabels.length > 1 && (
          <div>
            <Line data={dailyChartData} options={dailyChartOptions} />
          </div>
        )}

        {/* Activity Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Activity Insights</h4>
            <div className="space-y-2 text-sm">
              {peakHour !== null && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">📈</span>
                  <span>Peak activity occurs at {peakHour.toString().padStart(2, '0')}:00</span>
                </div>
              )}
              
              {avgEntriesPerHour > 0 && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-2">⚡</span>
                  <span>Average of {Math.round(avgEntriesPerHour)} entries per hour</span>
                </div>
              )}
              
              {totalDays > 1 && (
                <div className="flex items-center">
                  <span className="text-purple-600 mr-2">📅</span>
                  <span>Data spans {totalDays} days</span>
                </div>
              )}

              {/* Activity level assessment */}
              {avgEntriesPerHour > 1000 && (
                <div className="flex items-center">
                  <span className="text-orange-600 mr-2">🔥</span>
                  <span>High-volume system with significant activity</span>
                </div>
              )}
              
              {avgEntriesPerHour < 10 && avgEntriesPerHour > 0 && (
                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">🔍</span>
                  <span>Low-volume system with minimal activity</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
