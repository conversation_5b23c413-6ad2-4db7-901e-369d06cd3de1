'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface CustomerChartProps {
  data: any;
  detailed?: boolean;
}

export default function CustomerChart({ data, detailed = false }: CustomerChartProps) {
  if (!data || !data.customer_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Analysis</h3>
        <p className="text-gray-500">No customer data available (requires enterprise structured logs)</p>
      </div>
    );
  }

  const customerAnalysis = data.customer_analysis;
  const topCustomers = customerAnalysis.top_customers || [];
  const errorProneCustomers = customerAnalysis.error_prone_customers || [];
  const totalUniqueCustomers = customerAnalysis.total_unique_customers || 0;

  // Top customers activity chart
  const activityChartData = {
    labels: topCustomers.slice(0, 10).map((customer: any) => 
      customer.customer_id.length > 10 
        ? customer.customer_id.substring(0, 10) + '...'
        : customer.customer_id
    ),
    datasets: [
      {
        label: 'Activity Count',
        data: topCustomers.slice(0, 10).map((customer: any) => customer.activity_count),
        backgroundColor: 'rgba(34, 197, 94, 0.6)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
      },
    ],
  };

  const activityChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Top 10 Most Active Customers',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Error-prone customers chart
  const errorChartData = {
    labels: errorProneCustomers.slice(0, 10).map((customer: any) => 
      customer.customer_id.length > 10 
        ? customer.customer_id.substring(0, 10) + '...'
        : customer.customer_id
    ),
    datasets: [
      {
        label: 'Error Count',
        data: errorProneCustomers.slice(0, 10).map((customer: any) => customer.error_count),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
      },
    ],
  };

  const errorChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Customers with Most Errors',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Customer Analysis</h3>
        <div className="text-sm text-gray-500">
          {totalUniqueCustomers} unique customers
        </div>
      </div>

      {/* Customer Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Customers</div>
          <div className="text-blue-900 text-lg font-bold">
            {totalUniqueCustomers.toLocaleString()}
          </div>
          <div className="text-blue-600 text-xs">
            Unique customer IDs
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Active Customers</div>
          <div className="text-green-900 text-lg font-bold">
            {topCustomers.length}
          </div>
          <div className="text-green-600 text-xs">
            Customers with activity
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Error-Prone</div>
          <div className="text-red-900 text-lg font-bold">
            {errorProneCustomers.length}
          </div>
          <div className="text-red-600 text-xs">
            Customers with errors
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Top Active Customers */}
        {topCustomers.length > 0 && (
          <div>
            <Bar data={activityChartData} options={activityChartOptions} />
          </div>
        )}

        {/* Error-Prone Customers */}
        {detailed && errorProneCustomers.length > 0 && (
          <div>
            <Bar data={errorChartData} options={errorChartOptions} />
          </div>
        )}

        {/* Detailed Tables */}
        {detailed && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Customers Table */}
            {topCustomers.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Most Active Customers</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Customer ID
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Activity
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Errors
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Apps
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {topCustomers.slice(0, 10).map((customer: any, index: number) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm font-mono text-gray-900">
                            {customer.customer_id.length > 15 
                              ? customer.customer_id.substring(0, 15) + '...'
                              : customer.customer_id
                            }
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {customer.activity_count.toLocaleString()}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {customer.error_count}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-900">
                            {customer.apps_used.length}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Error-Prone Customers Table */}
            {errorProneCustomers.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Customers with Errors</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Customer ID
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Error Count
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {errorProneCustomers.slice(0, 10).map((customer: any, index: number) => (
                        <tr key={index}>
                          <td className="px-4 py-2 text-sm font-mono text-gray-900">
                            {customer.customer_id.length > 20 
                              ? customer.customer_id.substring(0, 20) + '...'
                              : customer.customer_id
                            }
                          </td>
                          <td className="px-4 py-2 text-sm text-red-600 font-medium">
                            {customer.error_count}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Customer Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Customer Insights</h4>
            <div className="space-y-2 text-sm">
              {totalUniqueCustomers > 0 && (
                <div className="flex items-center">
                  <span className="text-blue-600 mr-2">👥</span>
                  <span>Serving {totalUniqueCustomers} unique customers</span>
                </div>
              )}
              
              {topCustomers.length > 0 && (
                <div className="flex items-center">
                  <span className="text-green-600 mr-2">📈</span>
                  <span>
                    Most active customer: {topCustomers[0]?.customer_id} 
                    ({topCustomers[0]?.activity_count} activities)
                  </span>
                </div>
              )}
              
              {errorProneCustomers.length > 0 && (
                <div className="flex items-center">
                  <span className="text-red-600 mr-2">⚠️</span>
                  <span>
                    {errorProneCustomers.length} customers experiencing errors
                  </span>
                </div>
              )}

              {/* Multi-app usage */}
              {topCustomers.some((c: any) => c.apps_used.length > 1) && (
                <div className="flex items-center">
                  <span className="text-purple-600 mr-2">📱</span>
                  <span>
                    Some customers use multiple applications
                  </span>
                </div>
              )}

              {/* Platform diversity */}
              {topCustomers.some((c: any) => c.platforms_used && c.platforms_used.length > 1) && (
                <div className="flex items-center">
                  <span className="text-orange-600 mr-2">🌐</span>
                  <span>
                    Multi-platform customer access detected
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
