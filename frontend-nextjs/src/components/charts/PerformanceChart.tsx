'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface PerformanceChartProps {
  data: any;
  detailed?: boolean;
}

export default function PerformanceChart({ data, detailed = false }: PerformanceChartProps) {
  if (!data || !data.performance_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Analysis</h3>
        <p className="text-gray-500">No performance data available (requires enterprise structured logs)</p>
      </div>
    );
  }

  const perfAnalysis = data.performance_analysis;

  // App performance chart
  const appPerformance = perfAnalysis.performance_by_app || {};
  const appNames = Object.keys(appPerformance);
  const appResponseTimes = appNames.map(app => appPerformance[app].avg_response_time);

  const appChartData = {
    labels: appNames,
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: appResponseTimes,
        backgroundColor: 'rgba(34, 197, 94, 0.6)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 1,
      },
    ],
  };

  const appChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Average Response Time by Application',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
    },
  };

  // Module performance chart
  const modulePerformance = perfAnalysis.performance_by_module || {};
  const moduleNames = Object.keys(modulePerformance);
  const moduleResponseTimes = moduleNames.map(module => modulePerformance[module].avg_response_time);

  const moduleChartData = {
    labels: moduleNames,
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: moduleResponseTimes,
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const moduleChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Average Response Time by Module',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
    },
  };

  const totalRequests = perfAnalysis.total_requests || 0;
  const avgResponseTime = perfAnalysis.avg_response_time || 0;
  const p95ResponseTime = perfAnalysis.p95_response_time || 0;
  const p99ResponseTime = perfAnalysis.p99_response_time || 0;
  const slowRequestCount = perfAnalysis.slow_request_count || 0;
  const slowRequests = perfAnalysis.slow_requests || [];

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Performance Analysis</h3>
        <div className="text-sm text-gray-500">
          {totalRequests.toLocaleString()} requests analyzed
        </div>
      </div>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Avg Response</div>
          <div className="text-green-900 text-lg font-bold">
            {avgResponseTime.toFixed(0)}ms
          </div>
          <div className="text-green-600 text-xs">
            Average response time
          </div>
        </div>
        
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">95th Percentile</div>
          <div className="text-blue-900 text-lg font-bold">
            {p95ResponseTime.toFixed(0)}ms
          </div>
          <div className="text-blue-600 text-xs">
            95% of requests faster than
          </div>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-purple-600 text-sm font-medium">99th Percentile</div>
          <div className="text-purple-900 text-lg font-bold">
            {p99ResponseTime.toFixed(0)}ms
          </div>
          <div className="text-purple-600 text-xs">
            99% of requests faster than
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Slow Requests</div>
          <div className="text-red-900 text-lg font-bold">
            {slowRequestCount}
          </div>
          <div className="text-red-600 text-xs">
            Requests &gt; 5 seconds
          </div>
        </div>
      </div>

      {/* Performance Status */}
      <div className="mb-6 p-4 rounded-lg">
        {avgResponseTime < 500 ? (
          <div className="bg-green-50 border border-green-200">
            <div className="flex items-center p-4">
              <div className="text-green-400 text-xl mr-3">⚡</div>
              <div>
                <div className="text-green-800 font-medium">Excellent Performance</div>
                <div className="text-green-600 text-sm">
                  Average response time is under 500ms
                </div>
              </div>
            </div>
          </div>
        ) : avgResponseTime < 2000 ? (
          <div className="bg-yellow-50 border border-yellow-200">
            <div className="flex items-center p-4">
              <div className="text-yellow-400 text-xl mr-3">⚠️</div>
              <div>
                <div className="text-yellow-800 font-medium">Good Performance</div>
                <div className="text-yellow-600 text-sm">
                  Average response time is acceptable but could be optimized
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200">
            <div className="flex items-center p-4">
              <div className="text-red-400 text-xl mr-3">🐌</div>
              <div>
                <div className="text-red-800 font-medium">Performance Issues Detected</div>
                <div className="text-red-600 text-sm">
                  High response times may impact user experience
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Application Performance */}
        {appNames.length > 0 && (
          <div>
            <Bar data={appChartData} options={appChartOptions} />
          </div>
        )}

        {/* Module Performance */}
        {detailed && moduleNames.length > 0 && (
          <div>
            <Bar data={moduleChartData} options={moduleChartOptions} />
          </div>
        )}

        {/* Performance Tables */}
        {detailed && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Application Performance Table */}
            {appNames.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Application Performance</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Application
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Avg Time
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Requests
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                          Slow
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {appNames.map((app) => {
                        const appData = appPerformance[app];
                        return (
                          <tr key={app}>
                            <td className="px-4 py-2 text-sm font-medium text-gray-900">
                              {app}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {appData.avg_response_time.toFixed(0)}ms
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {appData.request_count.toLocaleString()}
                            </td>
                            <td className="px-4 py-2 text-sm text-gray-900">
                              {appData.slow_request_count}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Slowest Requests */}
            {slowRequests.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Slowest Requests</h4>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {slowRequests.slice(0, 10).map((request: any, index: number) => (
                    <div key={index} className="p-3 bg-red-50 rounded border">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-red-600">
                          {request.response_time.toFixed(0)}ms
                        </span>
                        <span className="text-xs text-gray-500">
                          {request.app_name}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600">
                        {request.url && (
                          <div className="font-mono">{request.url}</div>
                        )}
                        {request.module && (
                          <div>Module: {request.module}</div>
                        )}
                        {request.request_id && (
                          <div>ID: {request.request_id}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
