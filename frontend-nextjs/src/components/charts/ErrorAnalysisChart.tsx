'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface ErrorAnalysisChartProps {
  data: any;
  detailed?: boolean;
}

export default function ErrorAnalysisChart({ data, detailed = false }: ErrorAnalysisChartProps) {
  if (!data || !data.error_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Error Analysis</h3>
        <p className="text-gray-500">No error data available</p>
      </div>
    );
  }

  const errorAnalysis = data.error_analysis;

  // Error types chart
  const errorTypes = errorAnalysis.error_types || {};
  const typeChartData = {
    labels: Object.keys(errorTypes),
    datasets: [
      {
        data: Object.values(errorTypes),
        backgroundColor: [
          '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
          '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9'
        ],
        borderWidth: 1,
      },
    ],
  };

  const typeChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      title: {
        display: true,
        text: 'Error Types Distribution',
      },
    },
  };

  // Error trends chart
  const errorTrends = errorAnalysis.error_trends || [];
  const trendChartData = {
    labels: errorTrends.map((trend: any) => 
      new Date(trend.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    ),
    datasets: [
      {
        label: 'Error Count',
        data: errorTrends.map((trend: any) => trend.error_count),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 2,
      },
    ],
  };

  const trendChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Error Trends Over Time',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Error Analysis</h3>
        <div className="text-sm text-gray-500">
          {errorAnalysis.total_errors} total errors
        </div>
      </div>

      {/* Error Summary */}
      <div className="mb-6 p-4 bg-red-50 rounded-lg">
        <div className="flex items-center">
          <div className="text-red-400 text-xl mr-3">🚨</div>
          <div>
            <div className="text-red-800 font-medium">
              {errorAnalysis.total_errors} errors detected
            </div>
            <div className="text-red-600 text-sm">
              {Object.keys(errorTypes).length} different error types identified
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Error Types Distribution */}
        {Object.keys(errorTypes).length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <Doughnut data={typeChartData} options={typeChartOptions} />
            </div>
            
            <div className="space-y-3">
              <h4 className="text-md font-medium text-gray-900">Error Breakdown</h4>
              {Object.entries(errorTypes)
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium capitalize">{type.replace('_', ' ')}</span>
                    <span className="text-sm text-gray-600">{count as number} errors</span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Error Trends */}
        {detailed && errorTrends.length > 0 && (
          <div>
            <Bar data={trendChartData} options={trendChartOptions} />
          </div>
        )}

        {/* Top Error Messages */}
        {detailed && errorAnalysis.top_error_messages && errorAnalysis.top_error_messages.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Most Common Error Messages</h4>
            <div className="space-y-2">
              {errorAnalysis.top_error_messages.slice(0, 10).map((error: any, index: number) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-red-600">
                      {error.count} occurrences
                    </span>
                    <span className="text-xs text-gray-500">
                      #{index + 1}
                    </span>
                  </div>
                  <div className="text-sm text-gray-700 font-mono bg-white p-2 rounded border">
                    {error.message.length > 200 
                      ? error.message.substring(0, 200) + '...'
                      : error.message
                    }
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
