'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface RouteComparisonChartProps {
  data: any;
  fileResults: any[];
  detailed?: boolean;
}

export default function RouteComparisonChart({ data, fileResults, detailed = false }: RouteComparisonChartProps) {
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Route Comparison</h3>
        <p className="text-gray-500">No route data available for comparison</p>
      </div>
    );
  }

  // Get top routes by total requests across all files
  const routeEntries = Object.entries(data);
  const topRoutes = routeEntries
    .map(([route, fileData]: [string, any]) => ({
      route,
      totalRequests: fileData.reduce((sum: number, file: any) => sum + (file.total_requests || 0), 0),
      avgResponseTime: fileData.reduce((sum: number, file: any) => sum + (file.avg_response_time || 0), 0) / fileData.length,
      avgErrorRate: fileData.reduce((sum: number, file: any) => sum + (file.error_rate || 0), 0) / fileData.length
    }))
    .sort((a, b) => b.totalRequests - a.totalRequests)
    .slice(0, detailed ? 20 : 10);

  // Response time comparison chart
  const responseTimeData = {
    labels: topRoutes.map(r => r.route.length > 30 ? r.route.substring(0, 30) + '...' : r.route),
    datasets: fileResults.map((file, index) => ({
      label: file.fileName || `File ${index + 1}`,
      data: topRoutes.map(route => {
        const routeData = data[route.route];
        const fileData = routeData ? routeData[index] : null;
        return fileData ? fileData.avg_response_time || 0 : 0;
      }),
      backgroundColor: `hsla(${index * 60}, 70%, 50%, 0.6)`,
      borderColor: `hsla(${index * 60}, 70%, 50%, 1)`,
      borderWidth: 1,
    }))
  };

  const responseTimeOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Response Time Comparison by Route',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)'
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
  };

  // Request volume comparison chart
  const requestVolumeData = {
    labels: topRoutes.map(r => r.route.length > 30 ? r.route.substring(0, 30) + '...' : r.route),
    datasets: fileResults.map((file, index) => ({
      label: file.fileName || `File ${index + 1}`,
      data: topRoutes.map(route => {
        const routeData = data[route.route];
        const fileData = routeData ? routeData[index] : null;
        return fileData ? fileData.total_requests || 0 : 0;
      }),
      backgroundColor: `hsla(${index * 60 + 120}, 70%, 50%, 0.6)`,
      borderColor: `hsla(${index * 60 + 120}, 70%, 50%, 1)`,
      borderWidth: 1,
    }))
  };

  const requestVolumeOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Request Volume Comparison by Route',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Number of Requests'
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
  };

  // Error rate comparison chart
  const errorRateData = {
    labels: topRoutes.map(r => r.route.length > 30 ? r.route.substring(0, 30) + '...' : r.route),
    datasets: fileResults.map((file, index) => ({
      label: file.fileName || `File ${index + 1}`,
      data: topRoutes.map(route => {
        const routeData = data[route.route];
        const fileData = routeData ? routeData[index] : null;
        return fileData ? fileData.error_rate || 0 : 0;
      }),
      backgroundColor: `hsla(${index * 60 + 240}, 70%, 50%, 0.6)`,
      borderColor: `hsla(${index * 60 + 240}, 70%, 50%, 1)`,
      borderWidth: 1,
    }))
  };

  const errorRateOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Error Rate Comparison by Route',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Error Rate (%)'
        }
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45
        }
      }
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Route Performance Comparison</h3>
        <div className="text-sm text-gray-500">
          {topRoutes.length} routes compared across {fileResults.length} files
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Routes</div>
          <div className="text-blue-900 text-lg font-bold">
            {Object.keys(data).length}
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Avg Requests/Route</div>
          <div className="text-green-900 text-lg font-bold">
            {Math.round(topRoutes.reduce((sum, r) => sum + r.totalRequests, 0) / topRoutes.length) || 0}
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="text-yellow-600 text-sm font-medium">Avg Response Time</div>
          <div className="text-yellow-900 text-lg font-bold">
            {Math.round(topRoutes.reduce((sum, r) => sum + r.avgResponseTime, 0) / topRoutes.length) || 0}ms
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Avg Error Rate</div>
          <div className="text-red-900 text-lg font-bold">
            {(topRoutes.reduce((sum, r) => sum + r.avgErrorRate, 0) / topRoutes.length).toFixed(1) || 0}%
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-8">
        {/* Response Time Comparison */}
        <div>
          <Bar data={responseTimeData} options={responseTimeOptions} />
        </div>

        {/* Request Volume Comparison */}
        {detailed && (
          <div>
            <Bar data={requestVolumeData} options={requestVolumeOptions} />
          </div>
        )}

        {/* Error Rate Comparison */}
        {detailed && (
          <div>
            <Bar data={errorRateData} options={errorRateOptions} />
          </div>
        )}

        {/* Route Details Table */}
        {detailed && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Route Comparison Details</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                      Route
                    </th>
                    {fileResults.map((file, index) => (
                      <th key={index} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        {file.fileName || `File ${index + 1}`}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {topRoutes.slice(0, 15).map((route, routeIndex) => (
                    <tr key={routeIndex}>
                      <td className="px-4 py-2 text-sm font-mono text-gray-900">
                        {route.route.length > 50 ? route.route.substring(0, 50) + '...' : route.route}
                      </td>
                      {fileResults.map((file, fileIndex) => {
                        const routeData = data[route.route];
                        const fileData = routeData ? routeData[fileIndex] : null;
                        
                        return (
                          <td key={fileIndex} className="px-4 py-2 text-sm text-gray-900">
                            {fileData && fileData.exists ? (
                              <div>
                                <div className="font-medium">
                                  {fileData.total_requests.toLocaleString()} req
                                </div>
                                <div className="text-xs text-gray-500">
                                  {fileData.avg_response_time.toFixed(0)}ms avg
                                </div>
                                {fileData.error_rate > 0 && (
                                  <div className="text-xs text-red-600">
                                    {fileData.error_rate.toFixed(1)}% errors
                                  </div>
                                )}
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
