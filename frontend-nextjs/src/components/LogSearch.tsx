'use client';

import { useState, useEffect } from 'react';

interface LogSearchProps {
  analysisId: string;
}

interface SearchResult {
  line_number: number;
  raw_line: string;
  timestamp: string | null;
  level: string | null;
  message: string | null;
  fields: any;
}

export default function LogSearch({ analysisId }: LogSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [levelFilter, setLevelFilter] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const resultsPerPage = 50;

  const searchLogs = async (page = 1, append = false) => {
    if (!searchQuery.trim() && !levelFilter) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        q: searchQuery,
        limit: resultsPerPage.toString(),
        offset: ((page - 1) * resultsPerPage).toString(),
      });

      if (levelFilter) {
        params.append('level', levelFilter);
      }

      const response = await fetch(`/api/analysis/${analysisId}/search?${params}`);
      const data = await response.json();

      if (append) {
        setResults(prev => [...prev, ...data.results]);
      } else {
        setResults(data.results);
      }

      setTotalResults(data.total_results);
      setHasMore(data.has_more);
      setCurrentPage(page);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    searchLogs(1, false);
  };

  const loadMore = () => {
    searchLogs(currentPage + 1, true);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setLevelFilter('');
    setResults([]);
    setTotalResults(0);
    setCurrentPage(1);
    setHasMore(false);
  };

  const formatTimestamp = (timestamp: string | null) => {
    if (!timestamp) return 'N/A';
    try {
      return new Date(timestamp).toLocaleString();
    } catch {
      return timestamp;
    }
  };

  const getLevelColor = (level: string | null) => {
    switch (level?.toUpperCase()) {
      case 'ERROR': return 'text-red-600 bg-red-50';
      case 'WARN': 
      case 'WARNING': return 'text-yellow-600 bg-yellow-50';
      case 'INFO': return 'text-blue-600 bg-blue-50';
      case 'DEBUG': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Controls */}
      <div className="bg-white rounded-lg p-6 border">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Search Log Entries</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Query
            </label>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Enter search terms..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Log Level
            </label>
            <select
              value={levelFilter}
              onChange={(e) => setLevelFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Levels</option>
              <option value="ERROR">Error</option>
              <option value="WARN">Warning</option>
              <option value="INFO">Info</option>
              <option value="DEBUG">Debug</option>
            </select>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={handleSearch}
            disabled={loading || (!searchQuery.trim() && !levelFilter)}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
          
          <button
            onClick={clearSearch}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            Clear
          </button>
        </div>

        {totalResults > 0 && (
          <div className="mt-4 text-sm text-gray-600">
            Found {totalResults.toLocaleString()} results
          </div>
        )}
      </div>

      {/* Search Results */}
      {results.length > 0 && (
        <div className="bg-white rounded-lg border">
          <div className="p-4 border-b">
            <h4 className="text-md font-medium text-gray-900">Search Results</h4>
          </div>
          
          <div className="divide-y divide-gray-200">
            {results.map((result, index) => (
              <div key={index} className="p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500 font-mono">
                      Line {result.line_number}
                    </span>
                    
                    {result.level && (
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(result.level)}`}>
                        {result.level}
                      </span>
                    )}
                    
                    {result.timestamp && (
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(result.timestamp)}
                      </span>
                    )}
                  </div>
                </div>

                <div className="text-sm text-gray-900 font-mono bg-gray-50 p-3 rounded border overflow-x-auto">
                  {result.raw_line}
                </div>

                {result.message && result.message !== result.raw_line && (
                  <div className="mt-2 text-sm text-gray-700">
                    <strong>Parsed Message:</strong> {result.message}
                  </div>
                )}

                {result.fields && Object.keys(result.fields).length > 0 && (
                  <div className="mt-2">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                        View parsed fields ({Object.keys(result.fields).length})
                      </summary>
                      <div className="mt-2 bg-gray-50 p-2 rounded text-xs font-mono">
                        <pre>{JSON.stringify(result.fields, null, 2)}</pre>
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>

          {hasMore && (
            <div className="p-4 border-t text-center">
              <button
                onClick={loadMore}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors"
              >
                {loading ? 'Loading...' : 'Load More Results'}
              </button>
            </div>
          )}
        </div>
      )}

      {/* No Results */}
      {!loading && results.length === 0 && (searchQuery.trim() || levelFilter) && (
        <div className="bg-white rounded-lg p-8 text-center border">
          <div className="text-gray-400 text-4xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
          <p className="text-gray-600">
            Try adjusting your search terms or filters to find matching log entries.
          </p>
        </div>
      )}

      {/* Search Tips */}
      {results.length === 0 && !searchQuery.trim() && !levelFilter && (
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <h4 className="text-md font-medium text-blue-900 mb-3">Search Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Enter keywords to search in log messages and raw content</li>
            <li>• Use the level filter to find specific types of log entries</li>
            <li>• Search is case-insensitive and matches partial words</li>
            <li>• Combine text search with level filters for precise results</li>
            <li>• Results are paginated - use "Load More" to see additional entries</li>
          </ul>
        </div>
      )}
    </div>
  );
}
