(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5246:(e,s,t)=>{Promise.resolve().then(t.bind(t,8300))},8300:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(5155),l=t(2115),r=t(5428);function n(e){let{onAnalysisComplete:s}=e,[t,n]=(0,l.useState)(!1),[i,d]=(0,l.useState)(0),[o,c]=(0,l.useState)(!1),[m,x]=(0,l.useState)(null),h=(0,l.useRef)(null),{setIsLoading:u}=(0,r.S)(),g=async e=>{c(!0),u(!0),x(null),d(0);let t=new FormData;t.append("file",e);try{let e=await fetch("/api/upload",{method:"POST",body:t});if(!e.ok){let s=await e.json();throw Error(s.error||"Upload failed")}let a=await e.json();d(100),setTimeout(()=>{s(a.analysis_id),c(!1),u(!1)},1e3)}catch(e){x(e instanceof Error?e.message:"Upload failed"),c(!1),u(!1),d(0)}};return o?(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Analyzing Log File"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Processing your log file and generating insights..."}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(i,"%")}})}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:[i,"% complete"]})]})})}):(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Enterprise Log Analysis"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-2",children:"Upload your log files for intelligent analysis and insights"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Supports enterprise structured logs, JSON, Apache, Nginx, syslog, and more"})]}),(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-12 text-center transition-colors ".concat(t?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"),onDragOver:e=>{e.preventDefault(),n(!0)},onDragLeave:e=>{e.preventDefault(),n(!1)},onDrop:e=>{e.preventDefault(),n(!1);let s=Array.from(e.dataTransfer.files);s.length>0&&g(s[0])},children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-6xl",children:"\uD83D\uDCC4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Drop your log file here"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"or click to browse and select a file"}),(0,a.jsx)("button",{onClick:()=>{var e;null==(e=h.current)||e.click()},className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors",children:"Choose File"})]})]}),(0,a.jsx)("input",{ref:h,type:"file",onChange:e=>{let s=e.target.files;s&&s.length>0&&g(s[0])},className:"hidden",accept:".log,.txt,.json,.csv"})]}),m&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"text-red-400",children:"⚠️"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Upload Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:m})]})]})}),(0,a.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-blue-600 text-2xl mb-3",children:"\uD83C\uDFE2"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Enterprise Structured"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Advanced parsing for enterprise log formats with bracketed fields and structured data"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-green-600 text-2xl mb-3",children:"\uD83D\uDCCA"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Performance Analytics"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Response time analysis, customer insights, and application performance monitoring"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"text-purple-600 text-2xl mb-3",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Smart Detection"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Automatic format detection, anomaly identification, and intelligent pattern recognition"})]})]})]})}var i=t(406);function d(){let[e,s]=(0,l.useState)(null);return(0,a.jsx)(a.Fragment,{children:e?(0,a.jsx)(i.A,{analysisId:e}):(0,a.jsx)(n,{onAnalysisComplete:s})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[647,579,406,441,684,358],()=>s(5246)),_N_E=e.O()}]);