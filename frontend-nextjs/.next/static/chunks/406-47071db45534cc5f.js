"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[406],{406:(e,s,t)=>{t.d(s,{A:()=>g});var a=t(5155),r=t(2115),l=t(5428),i=t(2502),n=t(4065);function d(e){let{data:s,detailed:t=!1}=e;if(!s||!s.error_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Error Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No error data available"})]});let r=s.error_analysis,l=r.error_types||{},i={labels:Object.keys(l),datasets:[{data:Object.values(l),backgroundColor:["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9"],borderWidth:1}]},d=r.error_trends||[],c={labels:d.map(e=>new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})),datasets:[{label:"Error Count",data:d.map(e=>e.error_count),backgroundColor:"rgba(239, 68, 68, 0.6)",borderColor:"rgba(239, 68, 68, 1)",borderWidth:2}]};return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Error Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[r.total_errors," total errors"]})]}),(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-red-400 text-xl mr-3",children:"\uD83D\uDEA8"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-red-800 font-medium",children:[r.total_errors," errors detected"]}),(0,a.jsxs)("div",{className:"text-red-600 text-sm",children:[Object.keys(l).length," different error types identified"]})]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[Object.keys(l).length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(n.nu,{data:i,options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Error Types Distribution"}}}})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Error Breakdown"}),Object.entries(l).sort((e,s)=>{let[,t]=e,[,a]=s;return a-t}).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:s.replace("_"," ")}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[t," errors"]})]},s)})]})]}),t&&d.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:c,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Error Trends Over Time"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),t&&r.top_error_messages&&r.top_error_messages.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Most Common Error Messages"}),(0,a.jsx)("div",{className:"space-y-2",children:r.top_error_messages.slice(0,10).map((e,s)=>(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-red-600",children:[e.count," occurrences"]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["#",s+1]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-700 font-mono bg-white p-2 rounded border",children:e.message.length>200?e.message.substring(0,200)+"...":e.message})]},s))})]})]})]})}function c(e){let{data:s,detailed:t=!1}=e;if(!s||!s.time_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Timeline Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No timeline data available"})]});let r=s.time_analysis,l=r.hourly_distribution||{},i=Array.from({length:24},(e,s)=>s),d=i.map(e=>l[e]||0),c={labels:i.map(e=>"".concat(e.toString().padStart(2,"0"),":00")),datasets:[{label:"Log Entries",data:d,backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},o=r.daily_distribution||{},m=Object.keys(o).sort(),x=m.map(e=>o[e]),h={labels:m.map(e=>new Date(e).toLocaleDateString()),datasets:[{label:"Daily Activity",data:x,backgroundColor:"rgba(16, 185, 129, 0.6)",borderColor:"rgba(16, 185, 129, 1)",borderWidth:2,fill:!1}]},u=r.peak_hour,p=r.total_days||0,g=r.avg_entries_per_hour||0;return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Timeline Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[p," days analyzed"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Peak Hour"}),(0,a.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:null!==u?"".concat(u.toString().padStart(2,"0"),":00"):"N/A"}),(0,a.jsx)("div",{className:"text-blue-600 text-xs",children:"Highest activity period"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Avg/Hour"}),(0,a.jsx)("div",{className:"text-green-900 text-lg font-bold",children:Math.round(g).toLocaleString()}),(0,a.jsx)("div",{className:"text-green-600 text-xs",children:"Average entries per hour"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Time Span"}),(0,a.jsxs)("div",{className:"text-purple-900 text-lg font-bold",children:[p," ",1===p?"day":"days"]}),(0,a.jsx)("div",{className:"text-purple-600 text-xs",children:"Analysis period"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:c,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Activity by Hour of Day"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),t&&m.length>1&&(0,a.jsx)("div",{children:(0,a.jsx)(n.N1,{data:h,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Activity Over Time"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),t&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Activity Insights"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[null!==u&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDCC8"}),(0,a.jsxs)("span",{children:["Peak activity occurs at ",u.toString().padStart(2,"0"),":00"]})]}),g>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-600 mr-2",children:"⚡"}),(0,a.jsxs)("span",{children:["Average of ",Math.round(g)," entries per hour"]})]}),p>1&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDCC5"}),(0,a.jsxs)("span",{children:["Data spans ",p," days"]})]}),g>1e3&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83D\uDD25"}),(0,a.jsx)("span",{children:"High-volume system with significant activity"})]}),g<10&&g>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-gray-600 mr-2",children:"\uD83D\uDD0D"}),(0,a.jsx)("span",{children:"Low-volume system with minimal activity"})]})]})]})]})]})}function o(e){let{data:s,detailed:t=!1}=e;if(!s||!s.pattern_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Pattern Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No pattern data available"})]});let r=s.pattern_analysis,l=r.common_messages||[],i={labels:l.slice(0,10).map((e,s)=>"Message ".concat(s+1)),datasets:[{label:"Occurrences",data:l.slice(0,10).map(e=>e.count),backgroundColor:"rgba(139, 92, 246, 0.6)",borderColor:"rgba(139, 92, 246, 1)",borderWidth:1}]},d=r.ip_analysis||{},c=d.top_ips||[],o=r.status_patterns||{},m=o.status_distribution||{},x=o.status_categories||{};return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Pattern Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[l.length," patterns identified"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Common Messages"}),(0,a.jsx)("div",{className:"text-purple-900 text-lg font-bold",children:l.length}),(0,a.jsx)("div",{className:"text-purple-600 text-xs",children:"Recurring patterns found"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Unique IPs"}),(0,a.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:d.total_unique_ips||0}),(0,a.jsx)("div",{className:"text-blue-600 text-xs",children:"Different IP addresses"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Status Codes"}),(0,a.jsx)("div",{className:"text-green-900 text-lg font-bold",children:Object.keys(m).length}),(0,a.jsx)("div",{className:"text-green-600 text-xs",children:"Different response codes"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[l.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:i,options:{responsive:!0,indexAxis:"y",plugins:{legend:{display:!1},title:{display:!0,text:"Most Common Log Messages"},tooltip:{callbacks:{afterLabel:function(e){var s;let t=(null==(s=l[e.dataIndex])?void 0:s.message)||"";return t.length>100?t.substring(0,100)+"...":t}}}},scales:{x:{beginAtZero:!0,ticks:{precision:0}}}}})}),t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[c.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Top IP Addresses"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"IP Address"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Requests"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.slice(0,10).map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.ip}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.count.toLocaleString()})]},s))})]})})]}),Object.keys(x).length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"HTTP Status Categories"}),(0,a.jsx)("div",{className:"space-y-2",children:Object.entries(x).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"w-3 h-3 rounded-full mr-3 ".concat("2xx"===s?"bg-green-400":"3xx"===s?"bg-blue-400":"4xx"===s?"bg-yellow-400":"5xx"===s?"bg-red-400":"bg-gray-400")}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[s," Status Codes"]})]}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[t," requests"]})]},s)})})]})]}),t&&l.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Message Pattern Details"}),(0,a.jsx)("div",{className:"space-y-3",children:l.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-purple-600",children:["Pattern #",s+1]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium",children:e.count})," occurrences",(0,a.jsxs)("span",{className:"ml-2",children:["(",e.percentage,"%)"]})]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-700 font-mono bg-white p-3 rounded border",children:e.message})]},s))})]}),t&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Pattern Insights"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[l.length>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDD0D"}),(0,a.jsxs)("span",{children:["Found ",l.length," recurring message patterns"]})]}),d.total_unique_ips>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83C\uDF10"}),(0,a.jsxs)("span",{children:[d.total_unique_ips," unique IP addresses detected"]})]}),Object.keys(m).length>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCCA"}),(0,a.jsxs)("span",{children:[Object.keys(m).length," different HTTP status codes"]})]}),x["2xx"]&&x["4xx"]&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-yellow-600 mr-2",children:"✅"}),(0,a.jsxs)("span",{children:["Success rate: ",(x["2xx"]/(x["2xx"]+x["4xx"]+(x["5xx"]||0))*100).toFixed(1),"%"]})]})]})]})]})]})}function m(e){let{data:s,detailed:t=!1}=e;if(!s||!s.performance_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No performance data available (requires enterprise structured logs)"})]});let r=s.performance_analysis,l=r.performance_by_app||{},i=Object.keys(l),d=i.map(e=>l[e].avg_response_time),c=r.performance_by_module||{},o=Object.keys(c),m=o.map(e=>c[e].avg_response_time),x=r.total_requests||0,h=r.avg_response_time||0,u=r.p95_response_time||0,p=r.p99_response_time||0,g=r.slow_request_count||0,j=r.slow_requests||[];return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Performance Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[x.toLocaleString()," requests analyzed"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Avg Response"}),(0,a.jsxs)("div",{className:"text-green-900 text-lg font-bold",children:[h.toFixed(0),"ms"]}),(0,a.jsx)("div",{className:"text-green-600 text-xs",children:"Average response time"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"95th Percentile"}),(0,a.jsxs)("div",{className:"text-blue-900 text-lg font-bold",children:[u.toFixed(0),"ms"]}),(0,a.jsx)("div",{className:"text-blue-600 text-xs",children:"95% of requests faster than"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"99th Percentile"}),(0,a.jsxs)("div",{className:"text-purple-900 text-lg font-bold",children:[p.toFixed(0),"ms"]}),(0,a.jsx)("div",{className:"text-purple-600 text-xs",children:"99% of requests faster than"})]}),(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Slow Requests"}),(0,a.jsx)("div",{className:"text-red-900 text-lg font-bold",children:g}),(0,a.jsx)("div",{className:"text-red-600 text-xs",children:"Requests > 5 seconds"})]})]}),(0,a.jsx)("div",{className:"mb-6 p-4 rounded-lg",children:h<500?(0,a.jsx)("div",{className:"bg-green-50 border border-green-200",children:(0,a.jsxs)("div",{className:"flex items-center p-4",children:[(0,a.jsx)("div",{className:"text-green-400 text-xl mr-3",children:"⚡"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-green-800 font-medium",children:"Excellent Performance"}),(0,a.jsx)("div",{className:"text-green-600 text-sm",children:"Average response time is under 500ms"})]})]})}):h<2e3?(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200",children:(0,a.jsxs)("div",{className:"flex items-center p-4",children:[(0,a.jsx)("div",{className:"text-yellow-400 text-xl mr-3",children:"⚠️"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-yellow-800 font-medium",children:"Good Performance"}),(0,a.jsx)("div",{className:"text-yellow-600 text-sm",children:"Average response time is acceptable but could be optimized"})]})]})}):(0,a.jsx)("div",{className:"bg-red-50 border border-red-200",children:(0,a.jsxs)("div",{className:"flex items-center p-4",children:[(0,a.jsx)("div",{className:"text-red-400 text-xl mr-3",children:"\uD83D\uDC0C"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-red-800 font-medium",children:"Performance Issues Detected"}),(0,a.jsx)("div",{className:"text-red-600 text-sm",children:"High response times may impact user experience"})]})]})})}),(0,a.jsxs)("div",{className:"space-y-6",children:[i.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:{labels:i,datasets:[{label:"Avg Response Time (ms)",data:d,backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Application"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})}),t&&o.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:{labels:o,datasets:[{label:"Avg Response Time (ms)",data:m,backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Module"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})}),t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[i.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Performance"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Application"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Time"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Requests"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Slow"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>{let s=l[e];return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm font-medium text-gray-900",children:e}),(0,a.jsxs)("td",{className:"px-4 py-2 text-sm text-gray-900",children:[s.avg_response_time.toFixed(0),"ms"]}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:s.request_count.toLocaleString()}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:s.slow_request_count})]},e)})})]})})]}),j.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Slowest Requests"}),(0,a.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:j.slice(0,10).map((e,s)=>(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-red-600",children:[e.response_time.toFixed(0),"ms"]}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.app_name})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:[e.url&&(0,a.jsx)("div",{className:"font-mono",children:e.url}),e.module&&(0,a.jsxs)("div",{children:["Module: ",e.module]}),e.request_id&&(0,a.jsxs)("div",{children:["ID: ",e.request_id]})]})]},s))})]})]})]})]})}function x(e){var s,t;let{data:r,detailed:l=!1}=e;if(!r||!r.customer_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Customer Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No customer data available (requires enterprise structured logs)"})]});let i=r.customer_analysis,d=i.top_customers||[],c=i.error_prone_customers||[],o=i.total_unique_customers||0,m={labels:d.slice(0,10).map(e=>e.customer_id.length>10?e.customer_id.substring(0,10)+"...":e.customer_id),datasets:[{label:"Activity Count",data:d.slice(0,10).map(e=>e.activity_count),backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]},x={labels:c.slice(0,10).map(e=>e.customer_id.length>10?e.customer_id.substring(0,10)+"...":e.customer_id),datasets:[{label:"Error Count",data:c.slice(0,10).map(e=>e.error_count),backgroundColor:"rgba(239, 68, 68, 0.6)",borderColor:"rgba(239, 68, 68, 1)",borderWidth:1}]};return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Customer Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[o," unique customers"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Customers"}),(0,a.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:o.toLocaleString()}),(0,a.jsx)("div",{className:"text-blue-600 text-xs",children:"Unique customer IDs"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Active Customers"}),(0,a.jsx)("div",{className:"text-green-900 text-lg font-bold",children:d.length}),(0,a.jsx)("div",{className:"text-green-600 text-xs",children:"Customers with activity"})]}),(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Error-Prone"}),(0,a.jsx)("div",{className:"text-red-900 text-lg font-bold",children:c.length}),(0,a.jsx)("div",{className:"text-red-600 text-xs",children:"Customers with errors"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[d.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:m,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Top 10 Most Active Customers"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),l&&c.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:x,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Customers with Most Errors"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),l&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[d.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Most Active Customers"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Customer ID"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Activity"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Errors"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Apps"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.slice(0,10).map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.customer_id.length>15?e.customer_id.substring(0,15)+"...":e.customer_id}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.activity_count.toLocaleString()}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.error_count}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.apps_used.length})]},s))})]})})]}),c.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Customers with Errors"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Customer ID"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Error Count"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.slice(0,10).map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.customer_id.length>20?e.customer_id.substring(0,20)+"...":e.customer_id}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-red-600 font-medium",children:e.error_count})]},s))})]})})]})]}),l&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Customer Insights"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[o>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDC65"}),(0,a.jsxs)("span",{children:["Serving ",o," unique customers"]})]}),d.length>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCC8"}),(0,a.jsxs)("span",{children:["Most active customer: ",null==(s=d[0])?void 0:s.customer_id,"(",null==(t=d[0])?void 0:t.activity_count," activities)"]})]}),c.length>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-red-600 mr-2",children:"⚠️"}),(0,a.jsxs)("span",{children:[c.length," customers experiencing errors"]})]}),d.some(e=>e.apps_used.length>1)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDCF1"}),(0,a.jsx)("span",{children:"Some customers use multiple applications"})]}),d.some(e=>e.platforms_used&&e.platforms_used.length>1)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83C\uDF10"}),(0,a.jsx)("span",{children:"Multi-platform customer access detected"})]})]})]})]})]})}function h(e){var s,t,r;let{data:l,detailed:i=!1}=e;if(!l||!l.application_analysis)return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Application Analysis"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No application data available (requires enterprise structured logs)"})]});let d=l.application_analysis,c=d.application_summary||[],o=d.total_applications||0,m={labels:c.slice(0,10).map(e=>e.app_name),datasets:[{label:"Activity Count",data:c.slice(0,10).map(e=>e.activity_count),backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},x={labels:c.map(e=>e.app_name),datasets:[{data:c.map(e=>e.error_rate),backgroundColor:["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9"],borderWidth:1}]},h={labels:c.filter(e=>e.avg_response_time>0).map(e=>e.app_name),datasets:[{label:"Avg Response Time (ms)",data:c.filter(e=>e.avg_response_time>0).map(e=>e.avg_response_time),backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]};return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Application Analysis"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[o," applications monitored"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Apps"}),(0,a.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:o}),(0,a.jsx)("div",{className:"text-blue-600 text-xs",children:"Applications monitored"})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Most Active"}),(0,a.jsx)("div",{className:"text-green-900 text-lg font-bold",children:(null==(s=c[0])?void 0:s.app_name)||"N/A"}),(0,a.jsx)("div",{className:"text-green-600 text-xs",children:"Highest activity app"})]}),(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Highest Error Rate"}),(0,a.jsxs)("div",{className:"text-red-900 text-lg font-bold",children:[Math.max(...c.map(e=>e.error_rate)).toFixed(1),"%"]}),(0,a.jsx)("div",{className:"text-red-600 text-xs",children:"Maximum error rate"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Avg Modules"}),(0,a.jsx)("div",{className:"text-purple-900 text-lg font-bold",children:c.length>0?Math.round(c.reduce((e,s)=>e+s.module_count,0)/c.length):0}),(0,a.jsx)("div",{className:"text-purple-600 text-xs",children:"Modules per application"})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[c.length>0&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:m,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Application Activity"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),i&&c.length>0&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)(n.nu,{data:x,options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Error Rate by Application"},tooltip:{callbacks:{label:function(e){return"".concat(e.label,": ").concat(e.parsed,"% error rate")}}}}}})}),c.some(e=>e.avg_response_time>0)&&(0,a.jsx)("div",{children:(0,a.jsx)(n.yP,{data:h,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Application"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})})]}),i&&c.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Details"}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Application"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Activity"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Errors"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Error Rate"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Response"}),(0,a.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Modules"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map((e,s)=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-4 py-2 text-sm font-medium text-gray-900",children:e.app_name}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.activity_count.toLocaleString()}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.error_count}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,a.jsxs)("span",{className:"font-medium ".concat(e.error_rate>10?"text-red-600":e.error_rate>5?"text-yellow-600":"text-green-600"),children:[e.error_rate.toFixed(1),"%"]})}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.avg_response_time>0?"".concat(e.avg_response_time.toFixed(0),"ms"):"N/A"}),(0,a.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.module_count})]},s))})]})})]}),i&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Insights"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[o>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDCF1"}),(0,a.jsxs)("span",{children:["Monitoring ",o," applications"]})]}),c.length>0&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCC8"}),(0,a.jsxs)("span",{children:["Most active: ",null==(t=c[0])?void 0:t.app_name,"(",null==(r=c[0])?void 0:r.activity_count.toLocaleString()," activities)"]})]}),c.some(e=>e.error_rate>10)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-red-600 mr-2",children:"⚠️"}),(0,a.jsxs)("span",{children:[c.filter(e=>e.error_rate>10).length," ","applications have high error rates (>10%)"]})]}),c.some(e=>e.avg_response_time>2e3)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83D\uDC0C"}),(0,a.jsx)("span",{children:"Some applications have slow response times (>2s)"})]}),c.some(e=>e.module_count>5)&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDD27"}),(0,a.jsx)("span",{children:"Complex applications with multiple modules detected"})]})]})]})]})]})}function u(e){let{analysisId:s}=e,[t,l]=(0,r.useState)(""),[i,n]=(0,r.useState)(""),[d,c]=(0,r.useState)([]),[o,m]=(0,r.useState)(!1),[x,h]=(0,r.useState)(0),[u,p]=(0,r.useState)(1),[g,j]=(0,r.useState)(!1),y=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.trim()||i){m(!0);try{let r=new URLSearchParams({q:t,limit:"50",offset:((e-1)*50).toString()});i&&r.append("level",i);let l=await fetch("/api/analysis/".concat(s,"/search?").concat(r)),n=await l.json();a?c(e=>[...e,...n.results]):c(n.results),h(n.total_results),j(n.has_more),p(e)}catch(e){console.error("Search failed:",e)}finally{m(!1)}}},v=()=>{p(1),y(1,!1)},b=e=>{if(!e)return"N/A";try{return new Date(e).toLocaleString()}catch(s){return e}},N=e=>{switch(null==e?void 0:e.toUpperCase()){case"ERROR":return"text-red-600 bg-red-50";case"WARN":case"WARNING":return"text-yellow-600 bg-yellow-50";case"INFO":return"text-blue-600 bg-blue-50";default:return"text-gray-600 bg-gray-50"}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Search Log Entries"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Query"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>l(e.target.value),placeholder:"Enter search terms...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&v()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Log Level"}),(0,a.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"All Levels"}),(0,a.jsx)("option",{value:"ERROR",children:"Error"}),(0,a.jsx)("option",{value:"WARN",children:"Warning"}),(0,a.jsx)("option",{value:"INFO",children:"Info"}),(0,a.jsx)("option",{value:"DEBUG",children:"Debug"})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:v,disabled:o||!t.trim()&&!i,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors",children:o?"Searching...":"Search"}),(0,a.jsx)("button",{onClick:()=>{l(""),n(""),c([]),h(0),p(1),j(!1)},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors",children:"Clear"})]}),x>0&&(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Found ",x.toLocaleString()," results"]})]}),d.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Search Results"})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:d.map((e,s)=>(0,a.jsxs)("div",{className:"p-4 hover:bg-gray-50",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-2",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500 font-mono",children:["Line ",e.line_number]}),e.level&&(0,a.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(N(e.level)),children:e.level}),e.timestamp&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:b(e.timestamp)})]})}),(0,a.jsx)("div",{className:"text-sm text-gray-900 font-mono bg-gray-50 p-3 rounded border overflow-x-auto",children:e.raw_line}),e.message&&e.message!==e.raw_line&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-gray-700",children:[(0,a.jsx)("strong",{children:"Parsed Message:"})," ",e.message]}),e.fields&&Object.keys(e.fields).length>0&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("details",{className:"text-sm",children:[(0,a.jsxs)("summary",{className:"cursor-pointer text-gray-600 hover:text-gray-800",children:["View parsed fields (",Object.keys(e.fields).length,")"]}),(0,a.jsx)("div",{className:"mt-2 bg-gray-50 p-2 rounded text-xs font-mono",children:(0,a.jsx)("pre",{children:JSON.stringify(e.fields,null,2)})})]})})]},s))}),g&&(0,a.jsx)("div",{className:"p-4 border-t text-center",children:(0,a.jsx)("button",{onClick:()=>{y(u+1,!0)},disabled:o,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors",children:o?"Loading...":"Load More Results"})})]}),!o&&0===d.length&&(t.trim()||i)&&(0,a.jsxs)("div",{className:"bg-white rounded-lg p-8 text-center border",children:[(0,a.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Results Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms or filters to find matching log entries."})]}),0===d.length&&!t.trim()&&!i&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 border border-blue-200",children:[(0,a.jsx)("h4",{className:"text-md font-medium text-blue-900 mb-3",children:"Search Tips"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsx)("li",{children:"• Enter keywords to search in log messages and raw content"}),(0,a.jsx)("li",{children:"• Use the level filter to find specific types of log entries"}),(0,a.jsx)("li",{children:"• Search is case-insensitive and matches partial words"}),(0,a.jsx)("li",{children:"• Combine text search with level filters for precise results"}),(0,a.jsx)("li",{children:'• Results are paginated - use "Load More" to see additional entries'})]})]})]})}function p(e){var s,t,r,l,i,n,d,c;let{data:o}=e;if(!o)return null;let m=[],x=[],h=null==(s=o.summary)?void 0:s.summary;h&&("critical"===h.health_status?(m.push("\uD83D\uDEA8 System health is CRITICAL with ".concat(h.error_rate,"% error rate")),x.push("Immediate action required: Investigate and resolve critical errors")):"warning"===h.health_status?(m.push("⚠️ System health needs attention with ".concat(h.error_rate,"% error rate")),x.push("Monitor error trends closely and implement error reduction strategies")):"healthy"===h.health_status&&m.push("✅ System appears healthy with low error rate (".concat(h.error_rate,"%)")),h.total_entries>1e5?m.push("\uD83D\uDCCA High volume system with ".concat(h.total_entries.toLocaleString()," log entries")):h.total_entries<100&&m.push("\uD83D\uDCC9 Low activity detected with only ".concat(h.total_entries," log entries")));let u=null==(t=o.errorAnalysis)?void 0:t.error_analysis;if(u&&u.total_errors>0){let e=Object.entries(u.error_types||{}).sort((e,s)=>{let[,t]=e,[,a]=s;return a-t})[0];e&&m.push("\uD83D\uDD0D Most common error type: ".concat(e[0]," (").concat(e[1]," occurrences)"))}let p=null==(r=o.performanceAnalysis)?void 0:r.performance_analysis;if(p&&p.total_requests>0){let e=p.avg_response_time,s=p.slow_request_count;e>2e3?(m.push("\uD83D\uDC0C High average response time: ".concat(e.toFixed(0),"ms")),x.push("Consider performance optimization to reduce response times")):e<500&&m.push("⚡ Excellent performance: Average response time is ".concat(e.toFixed(0),"ms")),s>0&&(m.push("⏱️ Found ".concat(s," slow requests (>5 seconds) that need investigation")),s>.05*p.total_requests&&x.push("Investigate and optimize slow requests (>5% of requests are slow)"))}let g=null==(l=o.customerAnalysis)?void 0:l.customer_analysis;if(g&&g.total_unique_customers>0&&(m.push("\uD83D\uDC65 Serving ".concat(g.total_unique_customers," unique customers")),(null==(c=g.error_prone_customers)?void 0:c.length)>0)){let e=g.error_prone_customers[0];m.push("\uD83D\uDEA8 Customer ".concat(e.customer_id," has ").concat(e.error_count," errors")),x.push("Provide additional support to customers experiencing frequent errors")}let j=null==(i=o.applicationAnalysis)?void 0:i.application_analysis;if(j&&j.total_applications>1){m.push("\uD83D\uDCF1 Monitoring ".concat(j.total_applications," different applications"));let e=j.application_summary||[];if(e.length>0){let s=e.reduce((e,s)=>s.error_rate>e.error_rate?s:e);s.error_rate>5&&(m.push("⚠️ Application '".concat(s.app_name,"' has high error rate: ").concat(s.error_rate,"%")),s.error_rate>10&&x.push("Investigate high error rate in application '".concat(s.app_name,"'")))}}let y=null==(n=o.timeAnalysis)?void 0:n.time_analysis;if(y){let e=y.peak_hour;null!==e&&m.push("\uD83D\uDCC8 Peak activity occurs at ".concat(e.toString().padStart(2,"0"),":00"));let s=y.total_days;s>1&&m.push("\uD83D\uDCC5 Data spans ".concat(s," days"))}let v=null==(d=o.patternAnalysis)?void 0:d.pattern_analysis;if(v){let e=v.common_messages||[];e.length>0&&m.push("\uD83D\uDD0D Found ".concat(e.length," recurring message patterns"));let s=v.ip_analysis;s&&s.total_unique_ips>0&&m.push("\uD83C\uDF10 ".concat(s.total_unique_ips," unique IP addresses detected"))}return h&&h.total_entries>1e4&&x.push("Consider implementing log rotation and archival for large log files"),0===m.length&&(m.push("\uD83D\uDCCA Analysis completed successfully"),m.push("\uD83D\uDD0D Review the detailed charts and tables for more insights")),0===x.length&&(x.push("Continue monitoring system health and performance"),x.push("Set up alerts for error rate thresholds")),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-xl mr-3",children:"\uD83D\uDCA1"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Key Insights"})]}),(0,a.jsx)("div",{className:"space-y-3",children:m.slice(0,8).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:e})]},s))}),m.length>8&&(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["And ",m.length-8," more insights available in detailed views..."]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-xl mr-3",children:"\uD83C\uDFAF"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recommendations"})]}),(0,a.jsx)("div",{className:"space-y-3",children:x.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-2 h-2 bg-green-400 rounded-full mt-2 mr-3"}),(0,a.jsx)("p",{className:"text-sm text-gray-700",children:e})]},s))}),x.length>6&&(0,a.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["And ",x.length-6," more recommendations..."]})]})]})}function g(e){var s,t,i,n,g,j,y,v;let{analysisId:b}=e,[N,f]=(0,r.useState)("overview"),[_,w]=(0,r.useState)(null),[D,A]=(0,r.useState)(!0),[C,S]=(0,r.useState)(null),{setAnalysisData:k}=(0,l.S)();(0,r.useEffect)(()=>{P()},[b]);let P=async()=>{try{A(!0),S(null);let[e,s,t,a]=await Promise.all([fetch("/api/analysis/".concat(b,"/summary")).then(e=>e.json()),fetch("/api/analysis/".concat(b,"/errors")).then(e=>e.json()),fetch("/api/analysis/".concat(b,"/timeline")).then(e=>e.json()),fetch("/api/analysis/".concat(b,"/patterns")).then(e=>e.json())]),r={},l={},i={};try{let e=await fetch("/api/analysis/".concat(b,"/performance"));e.ok&&(r=await e.json())}catch(e){console.log("Performance analysis not available")}try{let e=await fetch("/api/analysis/".concat(b,"/customers"));e.ok&&(l=await e.json())}catch(e){console.log("Customer analysis not available")}try{let e=await fetch("/api/analysis/".concat(b,"/applications"));e.ok&&(i=await e.json())}catch(e){console.log("Application analysis not available")}let n={summary:e,errorAnalysis:s,timeAnalysis:t,patternAnalysis:a,performanceAnalysis:r,customerAnalysis:l,applicationAnalysis:i};w(n),k(n)}catch(e){S(e instanceof Error?e.message:"Failed to load analysis data")}finally{A(!1)}};return D?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading analysis data..."})]})}):C?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"text-red-400",children:"⚠️"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Analysis"}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:C})]})]})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Analysis Results"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["Analysis ID: ",b]}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Refresh"})]})]}),(null==_?void 0:_.summary)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Entries"}),(0,a.jsx)("div",{className:"text-blue-900 text-2xl font-bold",children:(null==(t=_.summary.summary)||null==(s=t.total_entries)?void 0:s.toLocaleString())||0})]}),(0,a.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Error Rate"}),(0,a.jsxs)("div",{className:"text-red-900 text-2xl font-bold",children:[(null==(n=_.summary.summary)||null==(i=n.error_rate)?void 0:i.toFixed(1))||0,"%"]})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Health Status"}),(0,a.jsx)("div",{className:"text-green-900 text-lg font-bold capitalize",children:(null==(g=_.summary.summary)?void 0:g.health_status)||"Unknown"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Parse Success"}),(0,a.jsxs)("div",{className:"text-purple-900 text-2xl font-bold",children:[(null==(y=_.summary.summary)||null==(j=y.parse_success_rate)?void 0:j.toFixed(1))||0,"%"]})]})]})]}),(0,a.jsx)(p,{data:_}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"flex space-x-8 px-6",children:[{id:"overview",label:"Overview",icon:"\uD83D\uDCCA"},{id:"errors",label:"Error Analysis",icon:"\uD83D\uDEA8"},{id:"performance",label:"Performance",icon:"⚡"},{id:"customers",label:"Customers",icon:"\uD83D\uDC65"},{id:"applications",label:"Applications",icon:"\uD83D\uDCF1"},{id:"patterns",label:"Patterns",icon:"\uD83D\uDD0D"},{id:"timeline",label:"Timeline",icon:"\uD83D\uDCC8"},{id:"search",label:"Search Logs",icon:"\uD83D\uDD0E"}].map(e=>(0,a.jsxs)("button",{onClick:()=>f(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(N===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})}),(0,a.jsxs)("div",{className:"p-6",children:["overview"===N&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsx)(d,{data:_.errorAnalysis}),(0,a.jsx)(o,{data:_.patternAnalysis})]}),(null==(v=_.performanceAnalysis)?void 0:v.performance_analysis)&&(0,a.jsx)(m,{data:_.performanceAnalysis}),(0,a.jsx)(c,{data:_.timeAnalysis})]}),"errors"===N&&(0,a.jsx)(d,{data:_.errorAnalysis,detailed:!0}),"performance"===N&&(0,a.jsx)(m,{data:_.performanceAnalysis,detailed:!0}),"customers"===N&&(0,a.jsx)(x,{data:_.customerAnalysis,detailed:!0}),"applications"===N&&(0,a.jsx)(h,{data:_.applicationAnalysis,detailed:!0}),"patterns"===N&&(0,a.jsx)(o,{data:_.patternAnalysis,detailed:!0}),"timeline"===N&&(0,a.jsx)(c,{data:_.timeAnalysis,detailed:!0}),"search"===N&&(0,a.jsx)(u,{analysisId:b})]})]})]})}i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$,i.Bs),i.t1.register(i.PP,i.kc,i.E8,i.No,i.FN,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.No,i.FN,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$,i.Bs)},5428:(e,s,t)=>{t.d(s,{AnalysisProvider:()=>i,S:()=>n});var a=t(5155),r=t(2115);let l=(0,r.createContext)(void 0);function i(e){let{children:s}=e,[t,i]=(0,r.useState)(null),[n,d]=(0,r.useState)(null),[c,o]=(0,r.useState)(!1);return(0,a.jsx)(l.Provider,{value:{currentAnalysis:t,setCurrentAnalysis:i,analysisData:n,setAnalysisData:d,isLoading:c,setIsLoading:o},children:s})}function n(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useAnalysis must be used within an AnalysisProvider");return e}}}]);