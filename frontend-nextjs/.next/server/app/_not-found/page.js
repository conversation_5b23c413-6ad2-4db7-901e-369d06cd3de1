(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1891:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var n=t(687),s=t(7856);function o(){let{currentAnalysis:e,setCurrentAnalysis:r}=(0,s.S)();return(0,n.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,n.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"\uD83D\uDD0D Log Analyzer"}),(0,n.jsx)("span",{className:"text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded",children:"Enterprise Edition"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,n.jsx)("button",{onClick:()=>{r(null)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"New Analysis"}),(0,n.jsx)("div",{className:"text-sm text-gray-500",children:(0,n.jsxs)("span",{className:"inline-flex items-center",children:[(0,n.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full mr-2"}),"Backend Connected"]})})]})]})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3917:(e,r,t)=>{Promise.resolve().then(t.bind(t,4597)),Promise.resolve().then(t.bind(t,7266))},4396:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l});var n=t(7413),s=t(1001),o=t.n(s);t(1135);var i=t(7266),a=t(4597);let l={title:"Log Analyzer - Enterprise Log Analysis Tool",description:"Advanced log analysis tool with enterprise structured log parsing capabilities",keywords:["log analysis","enterprise logs","monitoring","analytics"]};function d({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${o().variable} font-sans antialiased`,children:(0,n.jsx)(i.AnalysisProvider,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,n.jsx)(a.default,{}),(0,n.jsx)("main",{className:"container mx-auto px-4 py-8",children:e})]})})})})}},4597:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/components/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/components/Header.tsx","default")},7266:(e,r,t)=>{"use strict";t.d(r,{AnalysisProvider:()=>s});var n=t(2907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call AnalysisProvider() from the server but AnalysisProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/context/AnalysisContext.tsx","AnalysisProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAnalysis() from the server but useAnalysis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/context/AnalysisContext.tsx","useAnalysis")},7856:(e,r,t)=>{"use strict";t.d(r,{AnalysisProvider:()=>i,S:()=>a});var n=t(687),s=t(3210);let o=(0,s.createContext)(void 0);function i({children:e}){let[r,t]=(0,s.useState)(null),[i,a]=(0,s.useState)(null),[l,d]=(0,s.useState)(!1);return(0,n.jsx)(o.Provider,{value:{currentAnalysis:r,setCurrentAnalysis:t,analysisData:i,setAnalysisData:a,isLoading:l,setIsLoading:d},children:e})}function a(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAnalysis must be used within an AnalysisProvider");return e}},7948:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},8793:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=t(5239),s=t(8088),o=t(8170),i=t.n(o),a=t(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:t,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9181:(e,r,t)=>{Promise.resolve().then(t.bind(t,1891)),Promise.resolve().then(t.bind(t,7856))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,553],()=>t(8793));module.exports=n})();