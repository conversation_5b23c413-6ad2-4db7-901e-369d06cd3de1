(()=>{var e={};e.id=223,e.ids=[223],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},847:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(687),n=t(5773);t(3210);var a=t(7856),o=t(6111);function i({analysisId:e}){let{setCurrentAnalysis:r}=(0,a.S)(),t=(0,n.useRouter)();return e?(0,s.jsx)(o.A,{analysisId:e}):(t.push("/"),null)}function l(){let e=(0,n.useParams)().analysisId;return(0,s.jsx)(i,{analysisId:e})}},969:(e,r,t)=>{Promise.resolve().then(t.bind(t,3657))},2107:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>d});var s=t(5239),n=t(8088),a=t(8170),o=t.n(a),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["analysis",{children:["[analysisId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3657)),"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/analysis/[analysisId]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/analysis/[analysisId]/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/analysis/[analysisId]/page",pathname:"/analysis/[analysisId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3657:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/analysis/[analysisId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/app/analysis/[analysisId]/page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},7417:(e,r,t)=>{Promise.resolve().then(t.bind(t,847))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,553,638,530],()=>t(2107));module.exports=s})();