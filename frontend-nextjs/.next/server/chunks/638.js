exports.id=638,exports.ids=[638],exports.modules={554:(t,e)=>{"use strict";function i(t){return t.endsWith("/route")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(t,e)=>{"use strict";function i(t){let e=5381;for(let i=0;i<t.length;i++)e=(e<<5)+e+t.charCodeAt(i)|0;return e>>>0}function s(t){return i(t).toString(36).slice(0,5)}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{djb2Hash:function(){return i},hexHash:function(){return s}})},1437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{INTERCEPTION_ROUTE_MARKERS:function(){return r},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return n}});let s=i(4722),r=["(..)(..)","(.)","(..)","(...)"];function n(t){return void 0!==t.split("/").find(t=>r.find(e=>t.startsWith(e)))}function a(t){let e,i,n;for(let s of t.split("/"))if(i=r.find(t=>s.startsWith(t))){[e,n]=t.split(i,2);break}if(!e||!i||!n)throw Object.defineProperty(Error("Invalid interception route: "+t+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(e=(0,s.normalizeAppPath)(e),i){case"(.)":n="/"===e?"/"+n:e+"/"+n;break;case"(..)":if("/"===e)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});n=e.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let a=e.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+t+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});n=a.slice(0,-2).concat(n).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:e,interceptedRoute:n}}},1658:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{fillMetadataSegment:function(){return u},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return f}});let s=i(8304),r=function(t){return t&&t.__esModule?t:{default:t}}(i(8671)),n=i(6341),a=i(7253),o=i(660),l=i(4722),h=i(2958),c=i(5499);function d(t){let e=r.default.dirname(t);if(t.endsWith("/sitemap"))return"";let i="";return e.split("/").some(t=>(0,c.isGroupSegment)(t)||(0,c.isParallelRouteSegment)(t))&&(i=(0,o.djb2Hash)(e).toString(36).slice(0,6)),i}function u(t,e,i){let s=(0,l.normalizeAppPath)(t),o=(0,a.getNamedRouteRegex)(s,{prefixRouteKeys:!1}),c=(0,n.interpolateDynamicPath)(s,e,o),{name:u,ext:f}=r.default.parse(i),p=d(r.default.posix.join(t,u)),g=p?`-${p}`:"";return(0,h.normalizePathSep)(r.default.join(c,`${u}${g}${f}`))}function f(t){if(!(0,s.isMetadataPage)(t))return t;let e=t,i="";if("/robots"===t?e+=".txt":"/manifest"===t?e+=".webmanifest":i=d(t),!e.endsWith("/route")){let{dir:t,name:s,ext:n}=r.default.parse(e);e=r.default.posix.join(t,`${s}${i?`-${i}`:""}${n}`,"route")}return e}function p(t,e){let i=t.endsWith("/route"),s=i?t.slice(0,-6):t,r=s.endsWith("/sitemap")?".xml":"";return(e?`${s}/[__metadata_id__]`:`${s}${r}`)+(i?"/route":"")}},2437:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getPathMatch",{enumerable:!0,get:function(){return r}});let s=i(5362);function r(t,e){let i=[],r=(0,s.pathToRegexp)(t,i,{delimiter:"/",sensitive:"boolean"==typeof(null==e?void 0:e.sensitive)&&e.sensitive,strict:null==e?void 0:e.strict}),n=(0,s.regexpToFunction)((null==e?void 0:e.regexModifier)?new RegExp(e.regexModifier(r.source),r.flags):r,i);return(t,s)=>{if("string"!=typeof t)return!1;let r=n(t);if(!r)return!1;if(null==e?void 0:e.removeUnnamedParams)for(let t of i)"number"==typeof t.name&&delete r.params[t.name];return{...s,...r.params}}}},2785:(t,e)=>{"use strict";function i(t){let e={};for(let[i,s]of t.entries()){let t=e[i];void 0===t?e[i]=s:Array.isArray(t)?t.push(s):e[i]=[t,s]}return e}function s(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,s(t));else e.set(i,s(r));return e}function n(t){for(var e=arguments.length,i=Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,s]of e.entries())t.append(i,s)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return n},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},2958:(t,e)=>{"use strict";function i(t){return t.replace(/\\/g,"/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3293:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"escapeStringRegexp",{enumerable:!0,get:function(){return r}});let i=/[|\\{}()[\]^$+*?.-]/,s=/[|\\{}()[\]^$+*?.-]/g;function r(t){return i.test(t)?t.replace(s,"\\$&"):t}},3324:(t,e,i)=>{"use strict";let s;function r(t){return t+.5|0}i.d(e,{Bs:()=>sF,A6:()=>iy,E8:()=>sZ,PP:()=>rM,t1:()=>sC,ju:()=>iM,s$:()=>rr,ZT:()=>iw,No:()=>s$,kc:()=>rP,FN:()=>sY,hE:()=>ra,m_:()=>rx});let n=(t,e,i)=>Math.max(Math.min(t,i),e);function a(t){return n(r(2.55*t),0,255)}function o(t){return n(r(255*t),0,255)}function l(t){return n(r(t/2.55)/100,0,1)}function h(t){return n(r(100*t),0,100)}let c={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},d=[..."0123456789ABCDEF"],u=t=>d[15&t],f=t=>d[(240&t)>>4]+d[15&t],p=t=>(240&t)>>4==(15&t),g=t=>p(t.r)&&p(t.g)&&p(t.b)&&p(t.a),m=(t,e)=>t<255?e(t):"",b=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function x(t,e,i){let s=e*Math.min(i,1-i),r=(e,r=(e+t/30)%12)=>i-s*Math.max(Math.min(r-3,9-r,1),-1);return[r(0),r(8),r(4)]}function _(t,e,i){let s=(s,r=(s+t/60)%6)=>i-i*e*Math.max(Math.min(r,4-r,1),0);return[s(5),s(3),s(1)]}function y(t,e,i){let s,r=x(t,1,.5);for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)r[s]*=1-e-i,r[s]+=e;return r}function v(t){let e,i,s,r=t.r/255,n=t.g/255,a=t.b/255,o=Math.max(r,n,a),l=Math.min(r,n,a),h=(o+l)/2;o!==l&&(s=o-l,i=h>.5?s/(2-o-l):s/(o+l),e=60*(e=r===o?(n-a)/s+6*(n<a):n===o?(a-r)/s+2:(r-n)/s+4)+.5);return[0|e,i||0,h]}function M(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(o)}function w(t){return(t%360+360)%360}let k={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},P={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},E=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,S=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,O=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function R(t,e,i){if(t){let s=v(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),t.r=(s=M(x,s,void 0,void 0))[0],t.g=s[1],t.b=s[2]}}function A(t,e){return t?Object.assign(e||{},t):t}function T(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=o(t[3]))):(e=A(t,{r:0,g:0,b:0,a:1})).a=o(e.a),e}class C{constructor(t){let e;if(t instanceof C)return t;let i=typeof t;"object"===i?e=T(t):"string"===i&&(e=function(t){var e,i=t.length;return"#"===t[0]&&(4===i||5===i?e={r:255&17*c[t[1]],g:255&17*c[t[2]],b:255&17*c[t[3]],a:5===i?17*c[t[4]]:255}:(7===i||9===i)&&(e={r:c[t[1]]<<4|c[t[2]],g:c[t[3]]<<4|c[t[4]],b:c[t[5]]<<4|c[t[6]],a:9===i?c[t[7]]<<4|c[t[8]]:255})),e}(t)||function(t){s||((s=function(){let t,e,i,s,r,n={},a=Object.keys(P),o=Object.keys(k);for(t=0;t<a.length;t++){for(e=0,s=r=a[t];e<o.length;e++)i=o[e],r=r.replace(i,k[i]);i=parseInt(P[s],16),n[r]=[i>>16&255,i>>8&255,255&i]}return n}()).transparent=[0,0,0,0]);let e=s[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,i,s,r=E.exec(t),o=255;if(r){if(r[7]!==e){let t=+r[7];o=r[8]?a(t):n(255*t,0,255)}return e=+r[1],i=+r[3],s=+r[5],e=255&(r[2]?a(e):n(e,0,255)),{r:e,g:i=255&(r[4]?a(i):n(i,0,255)),b:s=255&(r[6]?a(s):n(s,0,255)),a:o}}}(t):function(t){let e,i=b.exec(t),s=255;if(!i)return;i[5]!==e&&(s=i[6]?a(+i[5]):o(+i[5]));let r=w(+i[2]),n=i[3]/100,l=i[4]/100;return{r:(e="hwb"===i[1]?M(y,r,n,l):"hsv"===i[1]?M(_,r,n,l):M(x,r,n,l))[0],g:e[1],b:e[2],a:s}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=A(this._rgb);return t&&(t.a=l(t.a)),t}set rgb(t){this._rgb=T(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${l(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;return this._valid?(e=g(t=this._rgb)?u:f,t?"#"+e(t.r)+e(t.g)+e(t.b)+m(t.a,e):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=v(t),i=e[0],s=h(e[1]),r=h(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${r}%, ${l(t.a)})`:`hsl(${i}, ${s}%, ${r}%)`}(this._rgb):void 0}mix(t,e){if(t){let i,s=this.rgb,r=t.rgb,n=e===i?.5:e,a=2*n-1,o=s.a-r.a,l=((a*o==-1?a:(a+o)/(1+a*o))+1)/2;i=1-l,s.r=255&l*s.r+i*r.r+.5,s.g=255&l*s.g+i*r.g+.5,s.b=255&l*s.b+i*r.b+.5,s.a=n*s.a+(1-n)*r.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){let s=O(l(t.r)),r=O(l(t.g)),n=O(l(t.b));return{r:o(S(s+i*(O(l(e.r))-s))),g:o(S(r+i*(O(l(e.g))-r))),b:o(S(n+i*(O(l(e.b))-n))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new C(this.rgb)}alpha(t){return this._rgb.a=o(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=r(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return R(this._rgb,2,t),this}darken(t){return R(this._rgb,2,-t),this}saturate(t){return R(this._rgb,1,t),this}desaturate(t){return R(this._rgb,1,-t),this}rotate(t){var e,i;return e=this._rgb,(i=v(e))[0]=w(i[0]+t),e.r=(i=M(x,i,void 0,void 0))[0],e.g=i[1],e.b=i[2],this}}function D(){}let L=(()=>{let t=0;return()=>t++})();function I(t){return null==t}function F(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function z(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function N(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function j(t,e){return N(t)?t:e}function V(t,e){return void 0===t?e:t}let B=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,W=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function H(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function $(t,e,i,s){let r,n,a;if(F(t))if(n=t.length,s)for(r=n-1;r>=0;r--)e.call(i,t[r],r);else for(r=0;r<n;r++)e.call(i,t[r],r);else if(z(t))for(r=0,n=(a=Object.keys(t)).length;r<n;r++)e.call(i,t[a[r]],a[r])}function U(t,e){let i,s,r,n;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(r=t[i],n=e[i],r.datasetIndex!==n.datasetIndex||r.index!==n.index)return!1;return!0}function Y(t){if(F(t))return t.map(Y);if(z(t)){let e=Object.create(null),i=Object.keys(t),s=i.length,r=0;for(;r<s;++r)e[i[r]]=Y(t[i[r]]);return e}return t}function X(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function q(t,e,i,s){if(!X(t))return;let r=e[t],n=i[t];z(r)&&z(n)?K(r,n,s):e[t]=Y(n)}function K(t,e,i){let s,r=F(e)?e:[e],n=r.length;if(!z(t))return t;let a=(i=i||{}).merger||q;for(let e=0;e<n;++e){if(!z(s=r[e]))continue;let n=Object.keys(s);for(let e=0,r=n.length;e<r;++e)a(n[e],t,s,i)}return t}function G(t,e){return K(t,e,{merger:Q})}function Q(t,e,i){if(!X(t))return;let s=e[t],r=i[t];z(s)&&z(r)?G(s,r):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Y(r))}let Z={"":t=>t,x:t=>t.x,y:t=>t.y};function J(t,e){return(Z[e]||(Z[e]=function(t){let e=function(t){let e=t.split("."),i=[],s="";for(let t of e)(s+=t).endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(let i of e){if(""===i)break;t=t&&t[i]}return t}}(e)))(t)}function tt(t){return t.charAt(0).toUpperCase()+t.slice(1)}let te=t=>void 0!==t,ti=t=>"function"==typeof t,ts=(t,e)=>{if(t.size!==e.size)return!1;for(let i of t)if(!e.has(i))return!1;return!0},tr=Math.PI,tn=2*tr,ta=tn+tr,to=Number.POSITIVE_INFINITY,tl=tr/180,th=tr/2,tc=tr/4,td=2*tr/3,tu=Math.log10,tf=Math.sign;function tp(t,e,i){return Math.abs(t-e)<i}function tg(t){let e=Math.round(t),i=Math.pow(10,Math.floor(tu(t=tp(t,e,t/1e3)?e:t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function tm(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tb(t,e,i){let s,r,n;for(s=0,r=t.length;s<r;s++)isNaN(n=t[s][i])||(e.min=Math.min(e.min,n),e.max=Math.max(e.max,n))}function tx(t){return tr/180*t}function t_(t){if(!N(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function ty(t,e){let i=e.x-t.x,s=e.y-t.y,r=Math.sqrt(i*i+s*s),n=Math.atan2(s,i);return n<-.5*tr&&(n+=tn),{angle:n,distance:r}}function tv(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tM(t,e){return(t-e+ta)%tn-tr}function tw(t){return(t%tn+tn)%tn}function tk(t,e,i,s){let r=tw(t),n=tw(e),a=tw(i),o=tw(n-r),l=tw(a-r),h=tw(r-n),c=tw(r-a);return r===n||r===a||s&&n===a||o>l&&h<c}function tP(t,e,i){return Math.max(e,Math.min(i,t))}function tE(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function tS(t,e,i){let s;i=i||(i=>t[i]<e);let r=t.length-1,n=0;for(;r-n>1;)i(s=n+r>>1)?n=s:r=s;return{lo:n,hi:r}}let tO=(t,e,i,s)=>tS(t,i,s?s=>{let r=t[s][e];return r<i||r===i&&t[s+1][e]===i}:s=>t[s][e]<i),tR=(t,e,i)=>tS(t,i,s=>t[s][e]>=i),tA=["push","pop","shift","splice","unshift"];function tT(t,e){let i=t._chartjs;if(!i)return;let s=i.listeners,r=s.indexOf(e);-1!==r&&s.splice(r,1),s.length>0||(tA.forEach(e=>{delete t[e]}),delete t._chartjs)}function tC(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tD="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tL(t,e){let i=[],s=!1;return function(...r){i=r,s||(s=!0,tD.call(window,()=>{s=!1,t.apply(e,i)}))}}let tI=t=>"start"===t?"left":"end"===t?"right":"center",tF=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2,tz=(t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e;function tN(t,e,i){let s=e.length,r=0,n=s;if(t._sorted){let{iScale:a,vScale:o,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=a.axis,{min:d,max:u,minDefined:f,maxDefined:p}=a.getUserBounds();if(f){if(r=Math.min(tO(l,c,d).lo,i?s:tO(e,c,a.getPixelForValue(d)).lo),h){let t=l.slice(0,r+1).reverse().findIndex(t=>!I(t[o.axis]));r-=Math.max(0,t)}r=tP(r,0,s-1)}if(p){let t=Math.max(tO(l,a.axis,u,!0).hi+1,i?0:tO(e,c,a.getPixelForValue(u),!0).hi+1);if(h){let e=l.slice(t-1).findIndex(t=>!I(t[o.axis]));t+=Math.max(0,e)}n=tP(t,r,s)-r}else n=s-r}return{start:r,count:n}}function tj(t){let{xScale:e,yScale:i,_scaleRanges:s}=t,r={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=r,!0;let n=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,r),n}let tV=t=>0===t||1===t,tB=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*tn/i)),tW=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*tn/i)+1,tH={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*th)+1,easeOutSine:t=>Math.sin(t*th),easeInOutSine:t=>-.5*(Math.cos(tr*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tV(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tV(t)?t:tB(t,.075,.3),easeOutElastic:t=>tV(t)?t:tW(t,.075,.3),easeInOutElastic:t=>tV(t)?t:t<.5?.5*tB(2*t,.1125,.45):.5+.5*tW(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tH.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tH.easeInBounce(2*t):.5*tH.easeOutBounce(2*t-1)+.5};function t$(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tU(t){return t$(t)?t:new C(t)}function tY(t){return t$(t)?t:new C(t).saturate(.5).darken(.1).hexString()}let tX=["x","y","borderWidth","radius","tension"],tq=["color","borderColor","backgroundColor"],tK=new Map;function tG(t,e,i){return(function(t,e){let i=t+JSON.stringify(e=e||{}),s=tK.get(i);return s||(s=new Intl.NumberFormat(t,e),tK.set(i,s)),s})(e,i).format(t)}let tQ={values:t=>F(t)?t:""+t,numeric(t,e,i){let s;if(0===t)return"0";let r=this.chart.options.locale,n=t;if(i.length>1){var a,o;let e,r=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(r<1e-4||r>1e15)&&(s="scientific"),a=t,Math.abs(e=(o=i).length>3?o[2].value-o[1].value:o[1].value-o[0].value)>=1&&a!==Math.floor(a)&&(e=a-Math.floor(a)),n=e}let l=tu(Math.abs(n)),h=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),c={notation:s,minimumFractionDigits:h,maximumFractionDigits:h};return Object.assign(c,this.options.ticks.format),tG(t,r,c)},logarithmic(t,e,i){return 0===t?"0":[1,2,3,5,10,15].includes(i[e].significand||t/Math.pow(10,Math.floor(tu(t))))||e>.8*i.length?tQ.numeric.call(this,t,e,i):""}};var tZ={formatters:tQ};let tJ=Object.create(null),t0=Object.create(null);function t1(t,e){if(!e)return t;let i=e.split(".");for(let e=0,s=i.length;e<s;++e){let s=i[e];t=t[s]||(t[s]=Object.create(null))}return t}function t2(t,e,i){return"string"==typeof e?K(t1(t,e),i):K(t1(t,""),e)}class t5{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>tY(e.backgroundColor),this.hoverBorderColor=(t,e)=>tY(e.borderColor),this.hoverColor=(t,e)=>tY(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return t2(this,t,e)}get(t){return t1(this,t)}describe(t,e){return t2(t0,t,e)}override(t,e){return t2(tJ,t,e)}route(t,e,i,s){let r=t1(this,t),n=t1(this,i),a="_"+e;Object.defineProperties(r,{[a]:{value:r[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[a],e=n[s];return z(t)?Object.assign({},e,t):V(t,e)},set(t){this[a]=t}}})}apply(t){t.forEach(t=>t(this))}}var t3=new t5({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:tq},numbers:{type:"number",properties:tX}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:tZ.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function t8(t,e,i,s,r){let n=e[r];return n||(n=e[r]=t.measureText(r).width,i.push(r)),n>s&&(s=n),s}function t6(t,e,i){let s=t.currentDevicePixelRatio,r=0!==i?Math.max(i/2,.5):0;return Math.round((e-r)*s)/s+r}function t4(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function t7(t,e,i,s){t9(t,e,i,s,null)}function t9(t,e,i,s,r){let n,a,o,l,h,c,d,u,f=e.pointStyle,p=e.rotation,g=e.radius,m=(p||0)*tl;if(f&&"object"==typeof f&&("[object HTMLImageElement]"===(n=f.toString())||"[object HTMLCanvasElement]"===n)){t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),t.restore();return}if(!isNaN(g)&&!(g<=0)){switch(t.beginPath(),f){default:r?t.ellipse(i,s,r/2,g,0,0,tn):t.arc(i,s,g,0,tn),t.closePath();break;case"triangle":c=r?r/2:g,t.moveTo(i+Math.sin(m)*c,s-Math.cos(m)*g),m+=td,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*g),m+=td,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*g),t.closePath();break;case"rectRounded":h=.516*g,a=Math.cos(m+tc)*(l=g-h),d=Math.cos(m+tc)*(r?r/2-h:l),o=Math.sin(m+tc)*l,u=Math.sin(m+tc)*(r?r/2-h:l),t.arc(i-d,s-o,h,m-tr,m-th),t.arc(i+u,s-a,h,m-th,m),t.arc(i+d,s+o,h,m,m+th),t.arc(i-u,s+a,h,m+th,m+tr),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,c=r?r/2:l,t.rect(i-c,s-l,2*c,2*l);break}m+=tc;case"rectRot":d=Math.cos(m)*(r?r/2:g),a=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-d,s-o),t.lineTo(i+u,s-a),t.lineTo(i+d,s+o),t.lineTo(i-u,s+a),t.closePath();break;case"crossRot":m+=tc;case"cross":d=Math.cos(m)*(r?r/2:g),a=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-a),t.lineTo(i-u,s+a);break;case"star":d=Math.cos(m)*(r?r/2:g),a=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-a),t.lineTo(i-u,s+a),m+=tc,d=Math.cos(m)*(r?r/2:g),a=Math.cos(m)*g,o=Math.sin(m)*g,u=Math.sin(m)*(r?r/2:g),t.moveTo(i-d,s-o),t.lineTo(i+d,s+o),t.moveTo(i+u,s-a),t.lineTo(i-u,s+a);break;case"line":a=r?r/2:Math.cos(m)*g,o=Math.sin(m)*g,t.moveTo(i-a,s-o),t.lineTo(i+a,s+o);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(r?r/2:g),s+Math.sin(m)*g);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function et(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function ee(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ei(t){t.restore()}function es(t,e,i,s,r){if(!e)return t.lineTo(i.x,i.y);if("middle"===r){let s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===r!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function er(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function en(t,e,i,s,r,n={}){let a,o,l=F(e)?e:[e],h=n.strokeWidth>0&&""!==n.strokeColor;for(t.save(),t.font=r.string,n.translation&&t.translate(n.translation[0],n.translation[1]),I(n.rotation)||t.rotate(n.rotation),n.color&&(t.fillStyle=n.color),n.textAlign&&(t.textAlign=n.textAlign),n.textBaseline&&(t.textBaseline=n.textBaseline),a=0;a<l.length;++a)o=l[a],n.backdrop&&function(t,e){let i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}(t,n.backdrop),h&&(n.strokeColor&&(t.strokeStyle=n.strokeColor),I(n.strokeWidth)||(t.lineWidth=n.strokeWidth),t.strokeText(o,i,s,n.maxWidth)),t.fillText(o,i,s,n.maxWidth),function(t,e,i,s,r){if(r.strikethrough||r.underline){let n=t.measureText(s),a=e-n.actualBoundingBoxLeft,o=e+n.actualBoundingBoxRight,l=i-n.actualBoundingBoxAscent,h=i+n.actualBoundingBoxDescent,c=r.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=r.decorationWidth||2,t.moveTo(a,c),t.lineTo(o,c),t.stroke()}}(t,i,s,o,n),s+=Number(r.lineHeight);t.restore()}function ea(t,e){let{x:i,y:s,w:r,h:n,radius:a}=e;t.arc(i+a.topLeft,s+a.topLeft,a.topLeft,1.5*tr,tr,!0),t.lineTo(i,s+n-a.bottomLeft),t.arc(i+a.bottomLeft,s+n-a.bottomLeft,a.bottomLeft,tr,th,!0),t.lineTo(i+r-a.bottomRight,s+n),t.arc(i+r-a.bottomRight,s+n-a.bottomRight,a.bottomRight,th,0,!0),t.lineTo(i+r,s+a.topRight),t.arc(i+r-a.topRight,s+a.topRight,a.topRight,0,-th,!0),t.lineTo(i+a.topLeft,s)}let eo=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,el=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,eh=t=>+t||0;function ec(t,e){let i={},s=z(e),r=s?Object.keys(e):e,n=z(t)?s?i=>V(t[i],t[e[i]]):e=>t[e]:()=>t;for(let t of r)i[t]=eh(n(t));return i}function ed(t){return ec(t,{top:"y",right:"x",bottom:"y",left:"x"})}function eu(t){return ec(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ef(t){let e=ed(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ep(t,e){t=t||{},e=e||t3.font;let i=V(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=V(t.style,e.style);s&&!(""+s).match(el)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let r={family:V(t.family,e.family),lineHeight:function(t,e){let i=(""+t).match(eo);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}(V(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:V(t.weight,e.weight),string:""};return r.string=!r||I(r.size)||I(r.family)?null:(r.style?r.style+" ":"")+(r.weight?r.weight+" ":"")+r.size+"px "+r.family,r}function eg(t,e,i,s){let r,n,a,o=!0;for(r=0,n=t.length;r<n;++r)if(void 0!==(a=t[r])&&(void 0!==e&&"function"==typeof a&&(a=a(e),o=!1),void 0!==i&&F(a)&&(a=a[i%a.length],o=!1),void 0!==a))return s&&!o&&(s.cacheable=!1),a}function em(t,e){return Object.assign(Object.create(t),e)}function eb(t,e=[""],i,s,r=()=>t[0]){let n=i||t;return void 0===s&&(s=eE("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:n,_fallback:s,_getTarget:r,override:i=>eb([i,...t],e,n,s)},{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>eM(i,s,()=>(function(t,e,i,s){let r;for(let n of e)if(void 0!==(r=eE(ey(n,t),i)))return ev(t,r)?ek(i,s,t,r):r})(s,e,t,i)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eS(t).includes(e),ownKeys:t=>eS(t),set(t,e,i){let s=t._storage||(t._storage=r());return t[e]=s[e]=i,delete t._keys,!0}})}function ex(t,e,i,s){return new Proxy({_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:e_(t,s),setContext:e=>ex(t,e,i,s),override:r=>ex(t.override(r),e,i,s)},{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>eM(t,e,()=>(function(t,e,i){let{_proxy:s,_context:r,_subProxy:n,_descriptors:a}=t,o=s[e];return ti(o)&&a.isScriptable(e)&&(o=function(t,e,i,s){let{_proxy:r,_context:n,_subProxy:a,_stack:o}=i;if(o.has(t))throw Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let l=e(n,a||s);return o.delete(t),ev(t,l)&&(l=ek(r._scopes,r,t,l)),l}(e,o,t,i)),F(o)&&o.length&&(o=function(t,e,i,s){let{_proxy:r,_context:n,_subProxy:a,_descriptors:o}=i;if(void 0!==n.index&&s(t))return e[n.index%e.length];if(z(e[0])){let i=e,s=r._scopes.filter(t=>t!==i);for(let l of(e=[],i)){let i=ek(s,r,t,l);e.push(ex(i,n,a&&a[t],o))}}return e}(e,o,t,a.isIndexable)),ev(e,o)&&(o=ex(o,r,n&&n[e],a)),o})(t,e,i)),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function e_(t,e={scriptable:!0,indexable:!0}){let{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:r=e.allKeys}=t;return{allKeys:r,scriptable:i,indexable:s,isScriptable:ti(i)?i:()=>i,isIndexable:ti(s)?s:()=>s}}let ey=(t,e)=>t?t+tt(e):e,ev=(t,e)=>z(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eM(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let s=i();return t[e]=s,s}let ew=(t,e)=>!0===t?e:"string"==typeof t?J(e,t):void 0;function ek(t,e,i,s){var r;let n=e._rootScopes,a=(r=e._fallback,ti(r)?r(i,s):r),o=[...t,...n],l=new Set;l.add(s);let h=eP(l,o,i,a||i,s);return null!==h&&(void 0===a||a===i||null!==(h=eP(l,o,a,h,s)))&&eb(Array.from(l),[""],n,a,()=>(function(t,e,i){let s=t._getTarget();e in s||(s[e]={});let r=s[e];return F(r)&&z(i)?i:r||{}})(e,i,s))}function eP(t,e,i,s,r){for(;i;)i=function(t,e,i,s,r){for(let a of e){let e=ew(i,a);if(e){var n;t.add(e);let a=(n=e._fallback,ti(n)?n(i,r):n);if(void 0!==a&&a!==i&&a!==s)return a}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}(t,e,i,s,r);return i}function eE(t,e){for(let i of e){if(!i)continue;let e=i[t];if(void 0!==e)return e}}function eS(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let i of t)for(let t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eO(t,e,i,s){let r,n,a,{iScale:o}=t,{key:l="r"}=this._parsing,h=Array(s);for(r=0;r<s;++r)a=e[n=r+i],h[r]={r:o.parse(J(a,l),n)};return h}let eR=Number.EPSILON||1e-14,eA=(t,e)=>e<t.length&&!t[e].skip&&t[e],eT=t=>"x"===t?"y":"x";function eC(t,e,i){return Math.max(Math.min(t,i),e)}function eD(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eL(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eI(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}let eF=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),ez=["top","right","bottom","left"];function eN(t,e,i){let s={};i=i?"-"+i:"";for(let r=0;r<4;r++){let n=ez[r];s[n]=parseFloat(t[e+"-"+n+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}let ej=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function eV(t,e){if("native"in t)return t;let{canvas:i,currentDevicePixelRatio:s}=e,r=eF(i),n="border-box"===r.boxSizing,a=eN(r,"padding"),o=eN(r,"border","width"),{x:l,y:h,box:c}=function(t,e){let i,s,r=t.touches,n=r&&r.length?r[0]:t,{offsetX:a,offsetY:o}=n,l=!1;if(ej(a,o,t.target))i=a,s=o;else{let t=e.getBoundingClientRect();i=n.clientX-t.left,s=n.clientY-t.top,l=!0}return{x:i,y:s,box:l}}(t,i),d=a.left+(c&&o.left),u=a.top+(c&&o.top),{width:f,height:p}=e;return n&&(f-=a.width+o.width,p-=a.height+o.height),{x:Math.round((l-d)/f*i.width/s),y:Math.round((h-u)/p*i.height/s)}}let eB=t=>Math.round(10*t)/10;function eW(t,e,i){let s=e||1,r=Math.floor(t.height*s),n=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let a=t.canvas;return a.style&&(i||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||a.height!==r||a.width!==n)&&(t.currentDevicePixelRatio=s,a.height=r,a.width=n,t.ctx.setTransform(s,0,0,s,0,0),!0)}let eH=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eD()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function e$(t,e){let i=eF(t).getPropertyValue(e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function eU(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function eY(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function eX(t,e,i,s){let r={x:t.cp2x,y:t.cp2y},n={x:e.cp1x,y:e.cp1y},a=eU(t,r,i),o=eU(r,n,i),l=eU(n,e,i),h=eU(a,o,i),c=eU(o,l,i);return eU(h,c,i)}function eq(t,e,i){var s;return t?(s=i,{x:t=>e+e+s-t,setWidth(t){s=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function eK(t,e){let i,s;("ltr"===e||"rtl"===e)&&(s=[(i=t.canvas.style).getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function eG(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function eQ(t){return"angle"===t?{between:tk,compare:tM,normalize:tw}:{between:tE,compare:(t,e)=>t-e,normalize:t=>t}}function eZ({start:t,end:e,count:i,loop:s,style:r}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:r}}function eJ(t,e,i){let s,r,n;if(!i)return[t];let{property:a,start:o,end:l}=i,h=e.length,{compare:c,between:d,normalize:u}=eQ(a),{start:f,end:p,loop:g,style:m}=function(t,e,i){let s,{property:r,start:n,end:a}=i,{between:o,normalize:l}=eQ(r),h=e.length,{start:c,end:d,loop:u}=t;if(u){for(c+=h,d+=h,s=0;s<h&&o(l(e[c%h][r]),n,a);++s)c--,d--;c%=h,d%=h}return d<c&&(d+=h),{start:c,end:d,loop:u,style:t.style}}(t,e,i),b=[],x=!1,_=null,y=()=>d(o,n,s)&&0!==c(o,n),v=()=>0===c(l,s)||d(l,n,s),M=()=>x||y(),w=()=>!x||v();for(let t=f,i=f;t<=p;++t)(r=e[t%h]).skip||(s=u(r[a]))!==n&&(x=d(s,o,l),null===_&&M()&&(_=0===c(s,o)?t:i),null!==_&&w()&&(b.push(eZ({start:_,end:t,loop:g,count:h,style:m})),_=null),i=t,n=s);return null!==_&&b.push(eZ({start:_,end:p,loop:g,count:h,style:m})),b}function e0(t,e){let i=[],s=t.segments;for(let r=0;r<s.length;r++){let n=eJ(s[r],t.points,e);n.length&&i.push(...n)}return i}function e1(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){let r=t._chart.getContext(),n=e2(t.options),{_datasetIndex:a,options:{spanGaps:o}}=t,l=i.length,h=[],c=n,d=e[0].start,u=d;function f(t,e,s,r){let n=o?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=n;for(;i[e%l].skip;)e+=n;t%l!=e%l&&(h.push({start:t%l,end:e%l,loop:s,style:r}),c=r,d=e%l)}}for(let t of e){let e,n=i[(d=o?d:t.start)%l];for(u=d+1;u<=t.end;u++){let o=i[u%l];(function(t,e){if(!e)return!1;let i=[],s=function(t,e){return t$(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)})(e=e2(s.setContext(em(r,{type:"segment",p0:n,p1:o,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),c)&&f(d,u-1,t.loop,c),n=o,c=e}d<u-1&&f(d,u-1,t.loop,c)}return h}(t,e,i,s):e}function e2(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function e5(t,e,i){return t.options.clip?t[i]:e[i]}function e3(t,e){let i=e._clip;if(i.disabled)return!1;let s=function(t,e){let{xScale:i,yScale:s}=t;return i&&s?{left:e5(i,e,"left"),right:e5(i,e,"right"),top:e5(s,e,"top"),bottom:e5(s,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:s.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:s.right+(!0===i.right?0:i.right),top:!1===i.top?0:s.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:s.bottom+(!0===i.bottom?0:i.bottom)}}class e8{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){let r=e.listeners[s],n=e.duration;r.forEach(s=>s({chart:t,initial:e.initial,numSteps:n,currentStep:Math.min(i-e.start,n)}))}_refresh(){this._request||(this._running=!0,this._request=tD.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,s)=>{let r;if(!i.running||!i.items.length)return;let n=i.items,a=n.length-1,o=!1;for(;a>=0;--a)(r=n[a])._active?(r._total>i.duration&&(i.duration=r._total),r.tick(t),o=!0):(n[a]=n[n.length-1],n.pop());o&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){let e=this._charts,i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){let e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;let e=this._charts.get(t);return!!e&&!!e.running&&!!e.items.length}stop(t){let e=this._charts.get(t);if(!e||!e.items.length)return;let i=e.items,s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var e6=new e8;let e4="transparent",e7={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){let s=tU(t||e4),r=s.valid&&tU(e||e4);return r&&r.valid?r.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class e9{constructor(t,e,i,s){let r=e[i];s=eg([t.to,s,r,t.from]);let n=eg([t.from,r,s]);this._active=!0,this._fn=t.fn||e7[t.type||typeof n],this._easing=tH[t.easing]||tH.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=n,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);let s=this._target[this._prop],r=i-this._start,n=this._duration-r;this._start=i,this._duration=Math.floor(Math.max(n,t.duration)),this._total+=r,this._loop=!!t.loop,this._to=eg([t.to,e,s,t.from]),this._from=eg([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){let e,i=t-this._start,s=this._duration,r=this._prop,n=this._from,a=this._loop,o=this._to;if(this._active=n!==o&&(a||i<s),!this._active){this._target[r]=o,this._notify(!0);return}if(i<0){this._target[r]=n;return}e=i/s%2,e=a&&e>1?2-e:e,e=this._easing(Math.min(1,Math.max(0,e))),this._target[r]=this._fn(n,o,e)}wait(){let t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){let e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class it{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!z(t))return;let e=Object.keys(t3.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{let r=t[s];if(!z(r))return;let n={};for(let t of e)n[t]=r[t];(F(r.properties)&&r.properties||[s]).forEach(t=>{t!==s&&i.has(t)||i.set(t,n)})})}_animateOptions(t,e){let i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}(t,i);if(!s)return[];let r=this._createAnimations(s,i);return i.$shared&&(function(t,e){let i=[],s=Object.keys(e);for(let e=0;e<s.length;e++){let r=t[s[e]];r&&r.active()&&i.push(r.wait())}return Promise.all(i)})(t.options.$animations,i).then(()=>{t.options=i},()=>{}),r}_createAnimations(t,e){let i,s=this._properties,r=[],n=t.$animations||(t.$animations={}),a=Object.keys(e),o=Date.now();for(i=a.length-1;i>=0;--i){let l=a[i];if("$"===l.charAt(0))continue;if("options"===l){r.push(...this._animateOptions(t,e));continue}let h=e[l],c=n[l],d=s.get(l);if(c)if(d&&c.active()){c.update(d,h,o);continue}else c.cancel();if(!d||!d.duration){t[l]=h;continue}n[l]=c=new e9(d,t,l,h),r.push(c)}return r}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);let i=this._createAnimations(t,e);if(i.length)return e6.add(this._chart,i),!0}}function ie(t,e){let i=t&&t.options||{},s=i.reverse,r=void 0===i.min?e:0,n=void 0===i.max?e:0;return{start:s?n:r,end:s?r:n}}function ii(t,e){let i,s,r=[],n=t._getSortedDatasetMetas(e);for(i=0,s=n.length;i<s;++i)r.push(n[i].index);return r}function is(t,e,i,s={}){let r,n,a,o,l=t.keys,h="single"===s.mode;if(null===e)return;let c=!1;for(r=0,n=l.length;r<n;++r){if((a=+l[r])===i){if(c=!0,s.all)continue;break}N(o=t.values[a])&&(h||0===e||tf(e)===tf(o))&&(e+=o)}return c||s.all?e:0}function ir(t,e){let i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function ia(t,e,i,s){for(let r of e.getMatchingVisibleMetas(s).reverse()){let e=t[r.index];if(i&&e>0||!i&&e<0)return r.index}return null}function io(t,e){let i,{chart:s,_cachedMeta:r}=t,n=s._stacks||(s._stacks={}),{iScale:a,vScale:o,index:l}=r,h=a.axis,c=o.axis,d=`${a.id}.${o.id}.${r.stack||r.type}`,u=e.length;for(let t=0;t<u;++t){let s=e[t],{[h]:a,[c]:u}=s;(i=(s._stacks||(s._stacks={}))[c]=function(t,e,i){let s=t[e]||(t[e]={});return s[i]||(s[i]={})}(n,d,a))[l]=u,i._top=ia(i,o,!0,r.type),i._bottom=ia(i,o,!1,r.type),(i._visualValues||(i._visualValues={}))[l]=u}}function il(t,e){let i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function ih(t,e){let i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s)for(let r of e=e||t._parsed){let t=r._stacks;if(!t||void 0===t[s]||void 0===t[s][i])return;delete t[s][i],void 0!==t[s]._visualValues&&void 0!==t[s]._visualValues[i]&&delete t[s]._visualValues[i]}}let ic=t=>"reset"===t||"none"===t,id=(t,e)=>e?t:Object.assign({},t),iu=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:ii(i,!0),values:null};class ip{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ir(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&ih(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,r=e.xAxisID=V(i.xAxisID,il(t,"x")),n=e.yAxisID=V(i.yAxisID,il(t,"y")),a=e.rAxisID=V(i.rAxisID,il(t,"r")),o=e.indexAxis,l=e.iAxisID=s(o,r,n,a),h=e.vAxisID=s(o,n,r,a);e.xScale=this.getScaleForId(r),e.yScale=this.getScaleForId(n),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&tT(this._data,this),t._stacked&&ih(t)}_dataCheck(){let t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(z(e)){let t=this._cachedMeta;this._data=function(t,e){let i,s,r,{iScale:n,vScale:a}=e,o="x"===n.axis?"x":"y",l="x"===a.axis?"x":"y",h=Object.keys(t),c=Array(h.length);for(i=0,s=h.length;i<s;++i)r=h[i],c[i]={[o]:r,[l]:t[r]};return c}(e,t)}else if(i!==e){if(i){tT(i,this);let t=this._cachedMeta;ih(t),t._parsed=[]}e&&Object.isExtensible(e)&&function(t,e){if(t._chartjs)return t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tA.forEach(e=>{let i="_onData"+tt(e),s=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let r=s.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[i]&&t[i](...e)}),r}})})}(e,this),this._syncList=[],this._data=e}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let e=this._cachedMeta,i=this.getDataset(),s=!1;this._dataCheck();let r=e._stacked;e._stacked=ir(e.vScale,e),e.stack!==i.stack&&(s=!0,ih(e),e.stack=i.stack),this._resyncElements(t),(s||r!==e._stacked)&&(io(this,e._parsed),e._stacked=ir(e.vScale,e))}configure(){let t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){let i,s,r,{_cachedMeta:n,_data:a}=this,{iScale:o,_stacked:l}=n,h=o.axis,c=0===t&&e===a.length||n._sorted,d=t>0&&n._parsed[t-1];if(!1===this._parsing)n._parsed=a,n._sorted=!0,r=a;else{r=F(a[t])?this.parseArrayData(n,a,t,e):z(a[t])?this.parseObjectData(n,a,t,e):this.parsePrimitiveData(n,a,t,e);let o=()=>null===s[h]||d&&s[h]<d[h];for(i=0;i<e;++i)n._parsed[i+t]=s=r[i],c&&(o()&&(c=!1),d=s);n._sorted=c}l&&io(this,r)}parsePrimitiveData(t,e,i,s){let r,n,{iScale:a,vScale:o}=t,l=a.axis,h=o.axis,c=a.getLabels(),d=a===o,u=Array(s);for(r=0;r<s;++r)n=r+i,u[r]={[l]:d||a.parse(c[n],n),[h]:o.parse(e[n],n)};return u}parseArrayData(t,e,i,s){let r,n,a,{xScale:o,yScale:l}=t,h=Array(s);for(r=0;r<s;++r)a=e[n=r+i],h[r]={x:o.parse(a[0],n),y:l.parse(a[1],n)};return h}parseObjectData(t,e,i,s){let r,n,a,{xScale:o,yScale:l}=t,{xAxisKey:h="x",yAxisKey:c="y"}=this._parsing,d=Array(s);for(r=0;r<s;++r)a=e[n=r+i],d[r]={x:o.parse(J(a,h),n),y:l.parse(J(a,c),n)};return d}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){let s=this.chart,r=this._cachedMeta,n=e[t.axis];return is({keys:ii(s,!0),values:e._stacks[t.axis]._visualValues},n,r.index,{mode:i})}updateRangeFromParsed(t,e,i,s){let r=i[e.axis],n=null===r?NaN:r,a=s&&i._stacks[e.axis];s&&a&&(s.values=a,n=is(s,r,this._cachedMeta.index)),t.min=Math.min(t.min,n),t.max=Math.max(t.max,n)}getMinMax(t,e){let i,s,r=this._cachedMeta,n=r._parsed,a=r._sorted&&t===r.iScale,o=n.length,l=this._getOtherScale(t),h=iu(e,r,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:u}=function(t){let{min:e,max:i,minDefined:s,maxDefined:r}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:r?i:Number.POSITIVE_INFINITY}}(l);function f(){let e=(s=n[i])[l.axis];return!N(s[t.axis])||d>e||u<e}for(i=0;i<o&&(f()||(this.updateRangeFromParsed(c,t,s,h),!a));++i);if(a){for(i=o-1;i>=0;--i)if(!f()){this.updateRangeFromParsed(c,t,s,h);break}}return c}getAllParsedValues(t){let e,i,s,r=this._cachedMeta._parsed,n=[];for(e=0,i=r.length;e<i;++e)N(s=r[e][t.axis])&&n.push(s);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let e=this._cachedMeta,i=e.iScale,s=e.vScale,r=this.getParsed(t);return{label:i?""+i.getLabelForValue(r[i.axis]):"",value:s?""+s.getLabelForValue(r[s.axis]):""}}_update(t){var e;let i,s,r,n,a=this._cachedMeta;this.update(t||"default"),z(e=V(this.options.clip,function(t,e,i){if(!1===i)return!1;let s=ie(t,i),r=ie(e,i);return{top:r.end,right:s.end,bottom:r.start,left:s.start}}(a.xScale,a.yScale,this.getMaxOverflow())))?(i=e.top,s=e.right,r=e.bottom,n=e.left):i=s=r=n=e,a._clip={top:i,right:s,bottom:r,left:n,disabled:!1===e}}update(t){}draw(){let t,e=this._ctx,i=this.chart,s=this._cachedMeta,r=s.data||[],n=i.chartArea,a=[],o=this._drawStart||0,l=this._drawCount||r.length-o,h=this.options.drawActiveElementsOnTop;for(s.dataset&&s.dataset.draw(e,n,o,l),t=o;t<o+l;++t){let i=r[t];i.hidden||(i.active&&h?a.push(i):i.draw(e,n))}for(t=0;t<a.length;++t)a[t].draw(e,n)}getStyle(t,e){let i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){var s,r;let n,a=this.getDataset();if(t>=0&&t<this._cachedMeta.data.length){let e=this._cachedMeta.data[t];(n=e.$context||(s=this.getContext(),e.$context=em(s,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"}))).parsed=this.getParsed(t),n.raw=a.data[t],n.index=n.dataIndex=t}else(n=this.$context||(this.$context=em(this.chart.getContext(),{active:!1,dataset:void 0,datasetIndex:r=this.index,index:r,mode:"default",type:"dataset"}))).dataset=a,n.index=n.datasetIndex=this.index;return n.active=!!e,n.mode=i,n}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){let s="active"===e,r=this._cachedDataOpts,n=t+"-"+e,a=r[n],o=this.enableOptionSharing&&te(i);if(a)return id(a,o);let l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],d=l.getOptionScopes(this.getDataset(),h),u=Object.keys(t3.elements[t]),f=l.resolveNamedOptions(d,u,()=>this.getContext(i,s,e),c);return f.$shared&&(f.$shared=o,r[n]=Object.freeze(id(f,o))),f}_resolveAnimations(t,e,i){let s,r=this.chart,n=this._cachedDataOpts,a=`animation-${e}`,o=n[a];if(o)return o;if(!1!==r.options.animation){let r=this.chart.config,n=r.datasetAnimationScopeKeys(this._type,e),a=r.getOptionScopes(this.getDataset(),n);s=r.createResolver(a,this.getContext(t,i,e))}let l=new it(r,s&&s.animations);return s&&s._cacheable&&(n[a]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||ic(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){let i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,r=this.getSharedOptions(i),n=this.includeOptions(e,r)||r!==s;return this.updateSharedOptions(r,e,i),{sharedOptions:r,includeOptions:n}}updateElement(t,e,i,s){ic(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!ic(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;let r=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(r)||r})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let e=this._data,i=this._cachedMeta.data;for(let[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];let s=i.length,r=e.length,n=Math.min(r,s);n&&this.parse(0,n),r>s?this._insertElements(s,r-s,t):r<s&&this._removeElements(r,s-r)}_insertElements(t,e,i=!0){let s,r=this._cachedMeta,n=r.data,a=t+e,o=t=>{for(t.length+=e,s=t.length-1;s>=a;s--)t[s]=t[s-e]};for(o(n),s=t;s<a;++s)n[s]=new this.dataElementType;this._parsing&&o(r._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){let i=this._cachedMeta;if(this._parsing){let s=i._parsed.splice(t,e);i._stacked&&ih(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);let i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function ig(t,e,i,s){return F(t)?!function(t,e,i,s){let r=i.parse(t[0],s),n=i.parse(t[1],s),a=Math.min(r,n),o=Math.max(r,n),l=a,h=o;Math.abs(a)>Math.abs(o)&&(l=o,h=a),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:r,end:n,min:a,max:o}}(t,e,i,s):e[i.axis]=i.parse(t,s),e}function im(t,e,i,s){let r,n,a,o,l=t.iScale,h=t.vScale,c=l.getLabels(),d=l===h,u=[];for(r=i,n=i+s;r<n;++r)o=e[r],(a={})[l.axis]=d||l.parse(c[r],r),u.push(ig(o,a,h,r));return u}function ib(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function ix(t,e,i,s){var r,n,a;return t=s?i_((r=t,n=e,a=i,t=r===n?a:r===a?n:r),i,e):i_(t,e,i)}function i_(t,e,i){return"start"===t?e:"end"===t?i:t}class iy extends ip{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,s){return im(t,e,i,s)}parseArrayData(t,e,i,s){return im(t,e,i,s)}parseObjectData(t,e,i,s){let r,n,a,o,{iScale:l,vScale:h}=t,{xAxisKey:c="x",yAxisKey:d="y"}=this._parsing,u="x"===l.axis?c:d,f="x"===h.axis?c:d,p=[];for(r=i,n=i+s;r<n;++r)o=e[r],(a={})[l.axis]=l.parse(J(o,u),r),p.push(ig(J(o,f),a,h,r));return p}updateRangeFromParsed(t,e,i,s){super.updateRangeFromParsed(t,e,i,s);let r=i._custom;r&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let{iScale:e,vScale:i}=this._cachedMeta,s=this.getParsed(t),r=s._custom,n=ib(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(s[i.axis]);return{label:""+e.getLabelForValue(s[e.axis]),value:n}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){let e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,s){let r="reset"===s,{index:n,_cachedMeta:{vScale:a}}=this,o=a.getBasePixel(),l=a.isHorizontal(),h=this._getRuler(),{sharedOptions:c,includeOptions:d}=this._getSharedOptions(e,s);for(let u=e;u<e+i;u++){let e=this.getParsed(u),i=r||I(e[a.axis])?{base:o,head:o}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),p=(e._stacks||{})[a.axis],g={horizontal:l,base:i.base,enableBorderRadius:!p||ib(e._custom)||n===p._top||n===p._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};d&&(g.options=c||this.resolveDataElementOptions(u,t[u].active?"active":s));let m=g.options||t[u].options;!function(t,e,i,s){let r,n,a,o,l,h=e.borderSkipped,c={};if(!h){t.borderSkipped=c;return}if(!0===h){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:d,end:u,reverse:f,top:p,bottom:g}=(t.horizontal?(r=t.base>t.x,n="left",a="right"):(r=t.base<t.y,n="bottom",a="top"),r?(o="end",l="start"):(o="start",l="end"),{start:n,end:a,reverse:r,top:o,bottom:l});"middle"===h&&i&&(t.enableBorderRadius=!0,(i._top||0)===s?h=p:(i._bottom||0)===s?h=g:(c[ix(g,d,u,f)]=!0,h=p)),c[ix(h,d,u,f)]=!0,t.borderSkipped=c}(g,m,p,n),function(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?.33*(1===i):e}(g,m,h.ratio),this.updateElement(t[u],u,g,s)}}_getStacks(t,e){let{iScale:i}=this._cachedMeta,s=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),r=i.options.stacked,n=[],a=this._cachedMeta.controller.getParsed(e),o=a&&a[i.axis],l=t=>{let e=t._parsed.find(t=>t[i.axis]===o),s=e&&e[t.vScale.axis];if(I(s)||isNaN(s))return!0};for(let i of s)if(!(void 0!==e&&l(i))&&((!1===r||-1===n.indexOf(i.stack)||void 0===r&&void 0===i.stack)&&n.push(i.stack),i.index===t))break;return n.length||n.push(void 0),n}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){let t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(i=>t[i].axis===e).shift()}_getAxis(){let t={},e=this.getFirstScaleIdForIndexAxis();for(let i of this.chart.data.datasets)t[V("x"===this.chart.options.indexAxis?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){let s=this._getStacks(t,i),r=void 0!==e?s.indexOf(e):-1;return -1===r?s.length-1:r}_getRuler(){let t,e,i=this.options,s=this._cachedMeta,r=s.iScale,n=[];for(t=0,e=s.data.length;t<e;++t)n.push(r.getPixelForValue(this.getParsed(t)[r.axis],t));let a=i.barThickness;return{min:a||function(t){let e,i,s,r,n=t.iScale,a=function(t,e){if(!t._cache.$bar){let i=t.getMatchingVisibleMetas(e),s=[];for(let e=0,r=i.length;e<r;e++)s=s.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=tC(s.sort((t,e)=>t-e))}return t._cache.$bar}(n,t.type),o=n._length,l=()=>{32767!==s&&-32768!==s&&(te(r)&&(o=Math.min(o,Math.abs(s-r)||o)),r=s)};for(e=0,i=a.length;e<i;++e)s=n.getPixelForValue(a[e]),l();for(e=0,r=void 0,i=n.ticks.length;e<i;++e)s=n.getPixelForTick(e),l();return o}(s),pixels:n,start:r._startPixel,end:r._endPixel,stackCount:this._getStackCount(),scale:r,grouped:i.grouped,ratio:a?1:i.categoryPercentage*i.barPercentage}}_calculateBarValuePixels(t){let e,i,{_cachedMeta:{vScale:s,_stacked:r,index:n},options:{base:a,minBarLength:o}}=this,l=a||0,h=this.getParsed(t),c=h._custom,d=ib(c),u=h[s.axis],f=0,p=r?this.applyStack(s,h,r):u;p!==u&&(f=p-u,p=u),d&&(u=c.barStart,p=c.barEnd-c.barStart,0!==u&&tf(u)!==tf(c.barEnd)&&(f=0),f+=u);let g=I(a)||d?f:a,m=s.getPixelForValue(g);if(Math.abs(i=(e=this.chart.getDataVisibility(t)?s.getPixelForValue(f+p):m)-m)<o){var b;i=(0!==(b=i)?tf(b):(s.isHorizontal()?1:-1)*(s.min>=l?1:-1))*o,u===l&&(m-=i/2);let t=s.getPixelForDecimal(0),a=s.getPixelForDecimal(1),c=Math.min(t,a);e=(m=Math.max(Math.min(m,Math.max(t,a)),c))+i,r&&!d&&(h._stacks[s.axis]._visualValues[n]=s.getValueForPixel(e)-s.getValueForPixel(m))}if(m===s.getPixelForValue(l)){let t=tf(i)*s.getLineWidthForValue(l)/2;m+=t,i-=t}return{size:i,base:m,head:e,center:e+i/2}}_calculateBarIndexPixels(t,e){let i,s,r=e.scale,n=this.options,a=n.skipNull,o=V(n.maxBarThickness,1/0),l=this._getAxisCount();if(e.grouped){let r=a?this._getStackCount(t):e.stackCount,h="flex"===n.barThickness?function(t,e,i,s){let r=e.pixels,n=r[t],a=t>0?r[t-1]:null,o=t<r.length-1?r[t+1]:null,l=i.categoryPercentage;null===a&&(a=n-(null===o?e.end-e.start:o-n)),null===o&&(o=n+n-a);let h=n-(n-Math.min(a,o))/2*l;return{chunk:Math.abs(o-a)/2*l/s,ratio:i.barPercentage,start:h}}(t,e,n,r*l):function(t,e,i,s){let r,n,a=i.barThickness;return I(a)?(r=e.min*i.categoryPercentage,n=i.barPercentage):(r=a*s,n=1),{chunk:r/s,ratio:n,start:e.pixels[t]-r/2}}(t,e,n,r*l),c="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,d=this._getAxis().indexOf(V(c,this.getFirstScaleIdForIndexAxis())),u=this._getStackIndex(this.index,this._cachedMeta.stack,a?t:void 0)+d;i=h.start+h.chunk*u+h.chunk/2,s=Math.min(o,h.chunk*h.ratio)}else i=r.getPixelForValue(this.getParsed(t)[r.axis],t),s=Math.min(o,e.min*e.ratio);return{base:i-s/2,head:i+s/2,center:i,size:s}}draw(){let t=this._cachedMeta,e=t.vScale,i=t.data,s=i.length,r=0;for(;r<s;++r)null===this.getParsed(r)[e.axis]||i[r].hidden||i[r].draw(this._ctx)}}class iv extends ip{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,s){let r=super.parsePrimitiveData(t,e,i,s);for(let t=0;t<r.length;t++)r[t]._custom=this.resolveDataElementOptions(t+i).radius;return r}parseArrayData(t,e,i,s){let r=super.parseArrayData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=V(s[2],this.resolveDataElementOptions(t+i).radius)}return r}parseObjectData(t,e,i,s){let r=super.parseObjectData(t,e,i,s);for(let t=0;t<r.length;t++){let s=e[i+t];r[t]._custom=V(s&&s.r&&+s.r,this.resolveDataElementOptions(t+i).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,n=this.getParsed(t),a=s.getLabelForValue(n.x),o=r.getLabelForValue(n.y),l=n._custom;return{label:i[t]||"",value:"("+a+", "+o+(l?", "+l:"")+")"}}update(t){let e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(e,s),h=n.axis,c=a.axis;for(let d=e;d<e+i;d++){let e=t[d],i=!r&&this.getParsed(d),u={},f=u[h]=r?n.getPixelForDecimal(.5):n.getPixelForValue(i[h]),p=u[c]=r?a.getBasePixel():a.getPixelForValue(i[c]);u.skip=isNaN(f)||isNaN(p),l&&(u.options=o||this.resolveDataElementOptions(d,e.active?"active":s),r&&(u.options.radius=0)),this.updateElement(e,d,u,s)}}resolveDataElementOptions(t,e){let i=this.getParsed(t),s=super.resolveDataElementOptions(t,e);s.$shared&&(s=Object.assign({},s,{$shared:!1}));let r=s.radius;return"active"!==e&&(s.radius=0),s.radius+=V(i&&i._custom,r),s}}class iM extends ip{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let n=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){let i=this.getDataset().data,s=this._cachedMeta;if(!1===this._parsing)s._parsed=i;else{let r,n,a=t=>+i[t];if(z(i[t])){let{key:t="value"}=this._parsing;a=e=>+J(i[e],t)}for(r=t,n=t+e;r<n;++r)s._parsed[r]=a(r)}}_getRotation(){return tx(this.options.rotation-90)}_getCircumference(){return tx(this.options.circumference)}_getRotationExtents(){let t=tn,e=-tn;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){let s=this.chart.getDatasetMeta(i).controller,r=s._getRotation(),n=s._getCircumference();t=Math.min(t,r),e=Math.max(e,r+n)}return{rotation:t,circumference:e-t}}update(t){let{chartArea:e}=this.chart,i=this._cachedMeta,s=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,n=Math.max((Math.min(e.width,e.height)-r)/2,0),a=Math.min(B(this.options.cutout,n),1),o=this._getRingWeight(this.index),{circumference:l,rotation:h}=this._getRotationExtents(),{ratioX:c,ratioY:d,offsetX:u,offsetY:f}=function(t,e,i){let s=1,r=1,n=0,a=0;if(e<tn){let o=t+e,l=Math.cos(t),h=Math.sin(t),c=Math.cos(o),d=Math.sin(o),u=(e,s,r)=>tk(e,t,o,!0)?1:Math.max(s,s*i,r,r*i),f=(e,s,r)=>tk(e,t,o,!0)?-1:Math.min(s,s*i,r,r*i),p=u(0,l,c),g=u(th,h,d),m=f(tr,l,c),b=f(tr+th,h,d);s=(p-m)/2,r=(g-b)/2,n=-(p+m)/2,a=-(g+b)/2}return{ratioX:s,ratioY:r,offsetX:n,offsetY:a}}(h,l,a),p=Math.max(Math.min((e.width-r)/c,(e.height-r)/d)/2,0),g=W(this.options.radius,p),m=Math.max(g*a,0),b=(g-m)/this._getVisibleDatasetWeightTotal();this.offsetX=u*g,this.offsetY=f*g,i.total=this.calculateTotal(),this.outerRadius=g-b*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-b*o,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){let i=this.options,s=this._cachedMeta,r=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===s._parsed[t]||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*r/tn)}updateElements(t,e,i,s){let r,n="reset"===s,a=this.chart,o=a.chartArea,l=a.options.animation,h=(o.left+o.right)/2,c=(o.top+o.bottom)/2,d=n&&l.animateScale,u=d?0:this.innerRadius,f=d?0:this.outerRadius,{sharedOptions:p,includeOptions:g}=this._getSharedOptions(e,s),m=this._getRotation();for(r=0;r<e;++r)m+=this._circumference(r,n);for(r=e;r<e+i;++r){let e=this._circumference(r,n),i=t[r],a={x:h+this.offsetX,y:c+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:f,innerRadius:u};g&&(a.options=p||this.resolveDataElementOptions(r,i.active?"active":s)),m+=e,this.updateElement(i,r,a,s)}}calculateTotal(){let t,e=this._cachedMeta,i=e.data,s=0;for(t=0;t<i.length;t++){let r=e._parsed[t];null!==r&&!isNaN(r)&&this.chart.getDataVisibility(t)&&!i[t].hidden&&(s+=Math.abs(r))}return s}calculateCircumference(t){let e=this._cachedMeta.total;return e>0&&!isNaN(t)?Math.abs(t)/e*tn:0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=tG(e._parsed[t],i.options.locale);return{label:s[t]||"",value:r}}getMaxBorderWidth(t){let e,i,s,r,n,a=0,o=this.chart;if(!t){for(e=0,i=o.data.datasets.length;e<i;++e)if(o.isDatasetVisible(e)){t=(s=o.getDatasetMeta(e)).data,r=s.controller;break}}if(!t)return 0;for(e=0,i=t.length;e<i;++e)"inner"!==(n=r.resolveDataElementOptions(e)).borderAlign&&(a=Math.max(a,n.borderWidth||0,n.hoverBorderWidth||0));return a}getMaxOffset(t){let e=0;for(let i=0,s=t.length;i<s;++i){let t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(V(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class iw extends ip{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let e=this._cachedMeta,{dataset:i,data:s=[],_dataset:r}=e,n=this.chart._animationsDisabled,{start:a,count:o}=tN(e,s,n);this._drawStart=a,this._drawCount=o,tj(e)&&(a=0,o=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!r._decimated,i.points=s;let l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!n,options:l},t),this.updateElements(s,a,o,t)}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:c}=this._getSharedOptions(e,s),d=n.axis,u=a.axis,{spanGaps:f,segment:p}=this.options,g=tm(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||r||"none"===s,b=e+i,x=t.length,_=e>0&&this.getParsed(e-1);for(let i=0;i<x;++i){let f=t[i],x=m?f:{};if(i<e||i>=b){x.skip=!0;continue}let y=this.getParsed(i),v=I(y[u]),M=x[d]=n.getPixelForValue(y[d],i),w=x[u]=r||v?a.getBasePixel():a.getPixelForValue(o?this.applyStack(a,y,o):y[u],i);x.skip=isNaN(M)||isNaN(w)||v,x.stop=i>0&&Math.abs(y[d]-_[d])>g,p&&(x.parsed=y,x.raw=l.data[i]),c&&(x.options=h||this.resolveDataElementOptions(i,f.active?"active":s)),m||this.updateElement(f,i,x,s),_=y}}getMaxOverflow(){let t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];return s.length?Math.max(i,s[0].size(this.resolveDataElementOptions(0)),s[s.length-1].size(this.resolveDataElementOptions(s.length-1)))/2:i}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}class ik extends ip{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let e=t.data;if(e.labels.length&&e.datasets.length){let{labels:{pointStyle:i,color:s}}=t.legend.options;return e.labels.map((e,r)=>{let n=t.getDatasetMeta(0).controller.getStyle(r);return{text:e,fillStyle:n.backgroundColor,strokeStyle:n.borderColor,fontColor:s,lineWidth:n.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let e=this._cachedMeta,i=this.chart,s=i.data.labels||[],r=tG(e._parsed[t].r,i.options.locale);return{label:s[t]||"",value:r}}parseObjectData(t,e,i,s){return eO.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){let t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{let s=this.getParsed(i).r;!isNaN(s)&&this.chart.getDataVisibility(i)&&(s<e.min&&(e.min=s),s>e.max&&(e.max=s))}),e}_updateRadius(){let t=this.chart,e=t.chartArea,i=t.options,s=Math.max(Math.min(e.right-e.left,e.bottom-e.top)/2,0),r=Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0),n=(s-r)/t.getVisibleDatasetCount();this.outerRadius=s-n*this.index,this.innerRadius=this.outerRadius-n}updateElements(t,e,i,s){let r,n="reset"===s,a=this.chart,o=a.options.animation,l=this._cachedMeta.rScale,h=l.xCenter,c=l.yCenter,d=l.getIndexAngle(0)-.5*tr,u=d,f=360/this.countVisibleElements();for(r=0;r<e;++r)u+=this._computeAngle(r,s,f);for(r=e;r<e+i;r++){let e=t[r],i=u,p=u+this._computeAngle(r,s,f),g=a.getDataVisibility(r)?l.getDistanceFromCenterForValue(this.getParsed(r).r):0;u=p,n&&(o.animateScale&&(g=0),o.animateRotate&&(i=p=d));let m={x:h,y:c,innerRadius:0,outerRadius:g,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(r,e.active?"active":s)};this.updateElement(e,r,m,s)}}countVisibleElements(){let t=this._cachedMeta,e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?tx(this.resolveDataElementOptions(t,e).angle||i):0}}class iP extends iM{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class iE extends ip{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,s){return eO.bind(this)(t,e,i,s)}update(t){let e=this._cachedMeta,i=e.dataset,s=e.data||[],r=e.iScale.getLabels();if(i.points=s,"resize"!==t){let e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);let n={_loop:!0,_fullLoop:r.length===s.length,options:e};this.updateElement(i,void 0,n,t)}this.updateElements(s,0,s.length,t)}updateElements(t,e,i,s){let r=this._cachedMeta.rScale,n="reset"===s;for(let a=e;a<e+i;a++){let e=t[a],i=this.resolveDataElementOptions(a,e.active?"active":s),o=r.getPointPositionForValue(a,this.getParsed(a).r),l=n?r.xCenter:o.x,h=n?r.yCenter:o.y,c={x:l,y:h,angle:o.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,a,c,s)}}}class iS extends ip{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:s,yScale:r}=e,n=this.getParsed(t),a=s.getLabelForValue(n.x),o=r.getLabelForValue(n.y);return{label:i[t]||"",value:"("+a+", "+o+")"}}update(t){let e=this._cachedMeta,{data:i=[]}=e,s=this.chart._animationsDisabled,{start:r,count:n}=tN(e,i,s);if(this._drawStart=r,this._drawCount=n,tj(e)&&(r=0,n=i.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:r,_dataset:n}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!n._decimated,r.points=i;let a=this.resolveDatasetElementOptions(t);a.segment=this.options.segment,this.updateElement(r,void 0,{animated:!s,options:a},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,r,n,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,s){let r="reset"===s,{iScale:n,vScale:a,_stacked:o,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,s),c=this.getSharedOptions(h),d=this.includeOptions(s,c),u=n.axis,f=a.axis,{spanGaps:p,segment:g}=this.options,m=tm(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||r||"none"===s,x=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){let e=t[h],i=this.getParsed(h),p=b?e:{},_=I(i[f]),y=p[u]=n.getPixelForValue(i[u],h),v=p[f]=r||_?a.getBasePixel():a.getPixelForValue(o?this.applyStack(a,i,o):i[f],h);p.skip=isNaN(y)||isNaN(v)||_,p.stop=h>0&&Math.abs(i[u]-x[u])>m,g&&(p.parsed=i,p.raw=l.data[h]),d&&(p.options=c||this.resolveDataElementOptions(h,e.active?"active":s)),b||this.updateElement(e,h,p,s),x=i}this.updateSharedOptions(c,s,h)}getMaxOverflow(){let t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}let i=t.dataset,s=i.options&&i.options.borderWidth||0;return e.length?Math.max(s,e[0].size(this.resolveDataElementOptions(0)),e[e.length-1].size(this.resolveDataElementOptions(e.length-1)))/2:s}}function iO(){throw Error("This method is not implemented: Check that a complete date adapter is provided.")}class iR{static override(t){Object.assign(iR.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return iO()}parse(){return iO()}format(){return iO()}add(){return iO()}diff(){return iO()}startOf(){return iO()}endOf(){return iO()}}var iA={_date:iR};function iT(t,e,i,s,r){let n=t.getSortedVisibleDatasetMetas(),a=i[e];for(let t=0,i=n.length;t<i;++t){let{index:i,data:o}=n[t],{lo:l,hi:h}=function(t,e,i,s){let{controller:r,data:n,_sorted:a}=t,o=r._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&"r"!==e&&a&&n.length){let a=o._reversePixels?tR:tO;if(s){if(r._sharedOptions){let t=n[0],s="function"==typeof t.getRange&&t.getRange(e);if(s){let t=a(n,e,i-s),r=a(n,e,i+s);return{lo:t.lo,hi:r.hi}}}}else{let s=a(n,e,i);if(l){let{vScale:e}=r._cachedMeta,{_parsed:i}=t,n=i.slice(0,s.lo+1).reverse().findIndex(t=>!I(t[e.axis]));s.lo-=Math.max(0,n);let a=i.slice(s.hi).findIndex(t=>!I(t[e.axis]));s.hi+=Math.max(0,a)}return s}}return{lo:0,hi:n.length-1}}(n[t],e,a,r);for(let t=l;t<=h;++t){let e=o[t];e.skip||s(e,i,t)}}}function iC(t,e,i,s,r){let n=[];return(r||t.isPointInArea(e))&&iT(t,i,e,function(i,a,o){(r||et(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&n.push({element:i,datasetIndex:a,index:o})},!0),n}function iD(t,e,i,s,r,n){let a;return n||t.isPointInArea(e)?"r"!==i||s?function(t,e,i,s,r,n){let a=[],o=function(t){let e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){return Math.sqrt(Math.pow(e?Math.abs(t.x-s.x):0,2)+Math.pow(i?Math.abs(t.y-s.y):0,2))}}(i),l=Number.POSITIVE_INFINITY;return iT(t,i,e,function(i,h,c){let d=i.inRange(e.x,e.y,r);if(s&&!d)return;let u=i.getCenterPoint(r);if(!(n||t.isPointInArea(u))&&!d)return;let f=o(e,u);f<l?(a=[{element:i,datasetIndex:h,index:c}],l=f):f===l&&a.push({element:i,datasetIndex:h,index:c})}),a}(t,e,i,s,r,n):(a=[],iT(t,i,e,function(t,i,s){let{startAngle:n,endAngle:o}=t.getProps(["startAngle","endAngle"],r),{angle:l}=ty(t,{x:e.x,y:e.y});tk(l,n,o)&&a.push({element:t,datasetIndex:i,index:s})}),a):[]}function iL(t,e,i,s,r){let n=[],a="x"===i?"inXRange":"inYRange",o=!1;return(iT(t,i,e,(t,s,l)=>{t[a]&&t[a](e[i],r)&&(n.push({element:t,datasetIndex:s,index:l}),o=o||t.inRange(e.x,e.y,r))}),s&&!o)?[]:n}var iI={modes:{index(t,e,i,s){let r=eV(e,t),n=i.axis||"x",a=i.includeInvisible||!1,o=i.intersect?iC(t,r,n,s,a):iD(t,r,n,!1,s,a),l=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{let e=o[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,s){let r=eV(e,t),n=i.axis||"xy",a=i.includeInvisible||!1,o=i.intersect?iC(t,r,n,s,a):iD(t,r,n,!1,s,a);if(o.length>0){let e=o[0].datasetIndex,i=t.getDatasetMeta(e).data;o=[];for(let t=0;t<i.length;++t)o.push({element:i[t],datasetIndex:e,index:t})}return o},point(t,e,i,s){let r=eV(e,t);return iC(t,r,i.axis||"xy",s,i.includeInvisible||!1)},nearest(t,e,i,s){let r=eV(e,t),n=i.axis||"xy",a=i.includeInvisible||!1;return iD(t,r,n,i.intersect,s,a)},x(t,e,i,s){let r=eV(e,t);return iL(t,r,"x",i.intersect,s)},y(t,e,i,s){let r=eV(e,t);return iL(t,r,"y",i.intersect,s)}}};let iF=["left","top","right","bottom"];function iz(t,e){return t.filter(t=>t.pos===e)}function iN(t,e){return t.filter(t=>-1===iF.indexOf(t.pos)&&t.box.axis===e)}function ij(t,e){return t.sort((t,i)=>{let s=e?i:t,r=e?t:i;return s.weight===r.weight?s.index-r.index:s.weight-r.weight})}function iV(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function iB(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function iW(t,e,i,s){let r,n,a,o,l,h,c=[];for(r=0,n=t.length,l=0;r<n;++r){(o=(a=t[r]).box).update(a.width||e.w,a.height||e.h,function(t,e){let i=e.maxPadding;var s=t?["left","right"]:["top","bottom"];let r={left:0,top:0,right:0,bottom:0};return s.forEach(t=>{r[t]=Math.max(e[t],i[t])}),r}(a.horizontal,e));let{same:n,other:d}=function(t,e,i,s){let{pos:r,box:n}=i,a=t.maxPadding;if(!z(r)){i.size&&(t[r]-=i.size);let e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?n.height:n.width),i.size=e.size/e.count,t[r]+=i.size}n.getPadding&&iB(a,n.getPadding());let o=Math.max(0,e.outerWidth-iV(a,t,"left","right")),l=Math.max(0,e.outerHeight-iV(a,t,"top","bottom")),h=o!==t.w,c=l!==t.h;return t.w=o,t.h=l,i.horizontal?{same:h,other:c}:{same:c,other:h}}(e,i,a,s);l|=n&&c.length,h=h||d,o.fullSize||c.push(a)}return l&&iW(c,e,i,s)||h}function iH(t,e,i,s,r){t.top=i,t.left=e,t.right=e+s,t.bottom=i+r,t.width=s,t.height=r}function i$(t,e,i,s){let r=i.padding,{x:n,y:a}=e;for(let o of t){let t=o.box,l=s[o.stack]||{count:1,placed:0,weight:1},h=o.stackWeight/l.weight||1;if(o.horizontal){let s=e.w*h,n=l.size||t.height;te(l.start)&&(a=l.start),t.fullSize?iH(t,r.left,a,i.outerWidth-r.right-r.left,n):iH(t,e.left+l.placed,a,s,n),l.start=a,l.placed+=s,a=t.bottom}else{let s=e.h*h,a=l.size||t.width;te(l.start)&&(n=l.start),t.fullSize?iH(t,n,r.top,a,i.outerHeight-r.bottom-r.top):iH(t,n,e.top+l.placed,a,s),l.start=n,l.placed+=s,n=t.right}}e.x=n,e.y=a}var iU={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){let i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;let r=ef(t.options.layout.padding),n=Math.max(e-r.width,0),a=Math.max(i-r.height,0),o=function(t){let e=function(t){let e,i,s,r,n,a,o=[];for(e=0,i=(t||[]).length;e<i;++e)s=t[e],({position:r,options:{stack:n,stackWeight:a=1}}=s),o.push({index:e,box:s,pos:r,horizontal:s.isHorizontal(),weight:s.weight,stack:n&&r+n,stackWeight:a});return o}(t),i=ij(e.filter(t=>t.box.fullSize),!0),s=ij(iz(e,"left"),!0),r=ij(iz(e,"right")),n=ij(iz(e,"top"),!0),a=ij(iz(e,"bottom")),o=iN(e,"x"),l=iN(e,"y");return{fullSize:i,leftAndTop:s.concat(n),rightAndBottom:r.concat(l).concat(a).concat(o),chartArea:iz(e,"chartArea"),vertical:s.concat(r).concat(l),horizontal:n.concat(a).concat(o)}}(t.boxes),l=o.vertical,h=o.horizontal;$(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});let c=Object.freeze({outerWidth:e,outerHeight:i,padding:r,availableWidth:n,availableHeight:a,vBoxMaxWidth:n/2/(l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1),hBoxMaxHeight:a/2}),d=Object.assign({},r);iB(d,ef(s));let u=Object.assign({maxPadding:d,w:n,h:a,x:r.left,y:r.top},r),f=function(t,e){let i,s,r,n=function(t){let e={};for(let i of t){let{stack:t,pos:s,stackWeight:r}=i;if(!t||!iF.includes(s))continue;let n=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});n.count++,n.weight+=r}return e}(t),{vBoxMaxWidth:a,hBoxMaxHeight:o}=e;for(i=0,s=t.length;i<s;++i){let{fullSize:s}=(r=t[i]).box,l=n[r.stack],h=l&&r.stackWeight/l.weight;r.horizontal?(r.width=h?h*a:s&&e.availableWidth,r.height=o):(r.width=a,r.height=h?h*o:s&&e.availableHeight)}return n}(l.concat(h),c);iW(o.fullSize,u,c,f),iW(l,u,c,f),iW(h,u,c,f)&&iW(l,u,c,f);let p=u.maxPadding;function g(t){let e=Math.max(p[t]-u[t],0);return u[t]+=e,e}u.y+=g("top"),u.x+=g("left"),g("right"),g("bottom"),i$(o.leftAndTop,u,c,f),u.x+=u.w,u.y+=u.h,i$(o.rightAndBottom,u,c,f),t.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},$(o.chartArea,e=>{let i=e.box;Object.assign(i,t.chartArea),i.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class iY{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class iX extends iY{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}let iq="$chartjs",iK={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},iG=t=>null===t||""===t,iQ=!!eH&&{passive:!0};function iZ(t,e){for(let i of t)if(i===e||i.contains(e))return!0}function iJ(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||iZ(i.addedNodes,s))&&!iZ(i.removedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}function i0(t,e,i){let s=t.canvas,r=new MutationObserver(t=>{let e=!1;for(let i of t)e=(e=e||iZ(i.removedNodes,s))&&!iZ(i.addedNodes,s);e&&i()});return r.observe(document,{childList:!0,subtree:!0}),r}let i1=new Map,i2=0;function i5(){let t=window.devicePixelRatio;t!==i2&&(i2=t,i1.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function i3(t,e,i){let s=t.canvas,r=s&&eL(s);if(!r)return;let n=tL((t,e)=>{let s=r.clientWidth;i(t,e),s<r.clientWidth&&i()},window),a=new ResizeObserver(t=>{let e=t[0],i=e.contentRect.width,s=e.contentRect.height;(0!==i||0!==s)&&n(i,s)});return a.observe(r),i1.size||window.addEventListener("resize",i5),i1.set(t,n),a}function i8(t,e,i){i&&i.disconnect(),"resize"===e&&(i1.delete(t),i1.size||window.removeEventListener("resize",i5))}function i6(t,e,i){let s=t.canvas,r=tL(e=>{null!==t.ctx&&i(function(t,e){let i=iK[t.type]||t.type,{x:s,y:r}=eV(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==r?r:null}}(e,t))},t);return s&&s.addEventListener(e,r,iQ),r}class i4 extends iY{acquireContext(t,e){let i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(!function(t,e){let i=t.style,s=t.getAttribute("height"),r=t.getAttribute("width");if(t[iq]={initial:{height:s,width:r,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",iG(r)){let e=e$(t,"width");void 0!==e&&(t.width=e)}if(iG(s))if(""===t.style.height)t.height=t.width/(e||2);else{let e=e$(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){let e=t.canvas;if(!e[iq])return!1;let i=e[iq].initial;["height","width"].forEach(t=>{let s=i[t];I(s)?e.removeAttribute(t):e.setAttribute(t,s)});let s=i.style||{};return Object.keys(s).forEach(t=>{e.style[t]=s[t]}),e.width=e.width,delete e[iq],!0}addEventListener(t,e,i){this.removeEventListener(t,e);let s=t.$proxies||(t.$proxies={}),r={attach:iJ,detach:i0,resize:i3}[e]||i6;s[e]=r(t,e,i)}removeEventListener(t,e){let i=t.$proxies||(t.$proxies={}),s=i[e];s&&((({attach:i8,detach:i8,resize:i8})[e]||function(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,iQ)})(t,e,s),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return function(t,e,i,s){let r=eF(t),n=eN(r,"margin"),a=eI(r.maxWidth,t,"clientWidth")||to,o=eI(r.maxHeight,t,"clientHeight")||to,l=function(t,e,i){let s,r;if(void 0===e||void 0===i){let n=t&&eL(t);if(n){let t=n.getBoundingClientRect(),a=eF(n),o=eN(a,"border","width"),l=eN(a,"padding");e=t.width-l.width-o.width,i=t.height-l.height-o.height,s=eI(a.maxWidth,n,"clientWidth"),r=eI(a.maxHeight,n,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||to,maxHeight:r||to}}(t,e,i),{width:h,height:c}=l;if("content-box"===r.boxSizing){let t=eN(r,"border","width"),e=eN(r,"padding");h-=e.width+t.width,c-=e.height+t.height}return h=Math.max(0,h-n.width),c=Math.max(0,s?h/s:c-n.height),h=eB(Math.min(h,a,l.maxWidth)),c=eB(Math.min(c,o,l.maxHeight)),h&&!c&&(c=eB(h/2)),(void 0!==e||void 0!==i)&&s&&l.height&&c>l.height&&(h=eB(Math.floor((c=l.height)*s))),{width:h,height:c}}(t,e,i,s)}isAttached(t){let e=t&&eL(t);return!!(e&&e.isConnected)}}class i7{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return tm(this.x)&&tm(this.y)}getProps(t,e){let i=this.$animations;if(!e||!i)return this;let s={};return t.forEach(t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),s}}function i9(t,e,i,s,r){let n,a,o,l=V(s,0),h=Math.min(V(r,t.length),t.length),c=0;for(i=Math.ceil(i),r&&(i=(n=r-s)/Math.floor(n/i)),o=l;o<0;)o=Math.round(l+ ++c*i);for(a=Math.max(l,0);a<h;a++)a===o&&(e.push(t[a]),o=Math.round(l+ ++c*i))}let st=t=>"left"===t?"right":"right"===t?"left":t,se=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,si=(t,e)=>Math.min(e||t,t);function ss(t,e){let i=[],s=t.length/e,r=t.length,n=0;for(;n<r;n+=s)i.push(t[Math.floor(n)]);return i}function sr(t){return t.drawTicks?t.tickLength:0}function sn(t,e){if(!t.display)return 0;let i=ep(t.font,e),s=ef(t.padding);return(F(t.text)?t.text.length:1)*i.lineHeight+s.height}class sa extends i7{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=j(t,Number.POSITIVE_INFINITY),e=j(e,Number.NEGATIVE_INFINITY),i=j(i,Number.POSITIVE_INFINITY),s=j(s,Number.NEGATIVE_INFINITY),{min:j(t,i),max:j(e,s),minDefined:N(t),maxDefined:N(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:r,maxDefined:n}=this.getUserBounds();if(r&&n)return{min:i,max:s};let a=this.getMatchingVisibleMetas();for(let o=0,l=a.length;o<l;++o)e=a[o].controller.getMinMax(this,t),r||(i=Math.min(i,e.min)),n||(s=Math.max(s,e.max));return i=n&&i>s?s:i,s=r&&i>s?i:s,{min:j(i,j(s,i)),max:j(s,j(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){H(this.options.beforeUpdate,[this])}update(t,e,i){let{beginAtZero:s,grace:r,ticks:n}=this.options,a=n.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){let{min:s,max:r}=t,n=W(e,(r-s)/2),a=(t,e)=>i&&0===t?0:t+e;return{min:a(s,-Math.abs(n)),max:a(r,n)}}(this,r,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let o=a<this.ticks.length;this._convertTicksToLabels(o?ss(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),n.display&&(n.autoSkip||"auto"===n.source)&&(this.ticks=function(t,e){let i=t.options.ticks,s=function(t){let e=t.options.offset,i=t._tickSize();return Math.floor(Math.min(t._length/i+ +!e,t._maxLength/i))}(t),r=Math.min(i.maxTicksLimit||s,s),n=i.major.enabled?function(t){let e,i,s=[];for(e=0,i=t.length;e<i;e++)t[e].major&&s.push(e);return s}(e):[],a=n.length,o=n[0],l=n[a-1],h=[];if(a>r)return function(t,e,i,s){let r,n=0,a=i[0];for(r=0,s=Math.ceil(s);r<t.length;r++)r===a&&(e.push(t[r]),a=i[++n*s])}(e,h,n,a/r),h;let c=function(t,e,i){let s=function(t){let e,i,s=t.length;if(s<2)return!1;for(i=t[0],e=1;e<s;++e)if(t[e]-t[e-1]!==i)return!1;return i}(t),r=e.length/i;if(!s)return Math.max(r,1);let n=function(t){let e,i=[],s=Math.sqrt(t);for(e=1;e<s;e++)t%e==0&&(i.push(e),i.push(t/e));return s===(0|s)&&i.push(s),i.sort((t,e)=>t-e).pop(),i}(s);for(let t=0,e=n.length-1;t<e;t++){let e=n[t];if(e>r)return e}return Math.max(r,1)}(n,e,r);if(a>0){let t,i,s=a>1?Math.round((l-o)/(a-1)):null;for(i9(e,h,c,I(s)?0:o-s,o),t=0,i=a-1;t<i;t++)i9(e,h,c,n[t],n[t+1]);return i9(e,h,c,l,I(s)?e.length:l+s),h}return i9(e,h,c),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){H(this.options.afterUpdate,[this])}beforeSetDimensions(){H(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){H(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),H(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){H(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){let e,i,s,r=this.options.ticks;for(e=0,i=t.length;e<i;e++)(s=t[e]).label=H(r.callback,[s.value,e,t],this)}afterTickToLabelConversion(){H(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){H(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let t,e,i,s=this.options,r=s.ticks,n=si(this.ticks.length,s.ticks.maxTicksLimit),a=r.minRotation||0,o=r.maxRotation,l=a;if(!this._isVisible()||!r.display||a>=o||n<=1||!this.isHorizontal()){this.labelRotation=a;return}let h=this._getLabelSizes(),c=h.widest.width,d=h.highest.height,u=tP(this.chart.width-c,0,this.maxWidth);c+6>(t=s.offset?this.maxWidth/n:u/(n-1))&&(t=u/(n-(s.offset?.5:1)),e=this.maxHeight-sr(s.grid)-r.padding-sn(s.title,this.chart.options.font),i=Math.sqrt(c*c+d*d),l=Math.max(a,Math.min(o,l=180/tr*Math.min(Math.asin(tP((h.highest.height+6)/t,-1,1)),Math.asin(tP(e/i,-1,1))-Math.asin(tP(d/i,-1,1)))))),this.labelRotation=l}afterCalculateLabelRotation(){H(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){H(this.options.beforeFit,[this])}fit(){let t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:r}}=this,n=this._isVisible(),a=this.isHorizontal();if(n){let n=sn(s,e.options.font);if(a?(t.width=this.maxWidth,t.height=sr(r)+n):(t.height=this.maxHeight,t.width=sr(r)+n),i.display&&this.ticks.length){let{first:e,last:s,widest:r,highest:n}=this._getLabelSizes(),o=2*i.padding,l=tx(this.labelRotation),h=Math.cos(l),c=Math.sin(l);if(a){let e=i.mirror?0:c*r.width+h*n.height;t.height=Math.min(this.maxHeight,t.height+e+o)}else{let e=i.mirror?0:h*r.width+c*n.height;t.width=Math.min(this.maxWidth,t.width+e+o)}this._calculatePadding(e,s,c,h)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){let{ticks:{align:r,padding:n},position:a}=this.options,o=0!==this.labelRotation,l="top"!==a&&"x"===this.axis;if(this.isHorizontal()){let a=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1),c=0,d=0;o?l?(c=s*t.width,d=i*e.height):(c=i*t.height,d=s*e.width):"start"===r?d=e.width:"end"===r?c=t.width:"inner"!==r&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-a+n)*this.width/(this.width-a),0),this.paddingRight=Math.max((d-h+n)*this.width/(this.width-h),0)}else{let i=e.height/2,s=t.height/2;"start"===r?(i=0,s=t.height):"end"===r&&(i=e.height,s=0),this.paddingTop=i+n,this.paddingBottom=s+n}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){H(this.options.afterFit,[this])}isHorizontal(){let{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)I(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){let e=this.options.ticks.sampleSize,i=this.ticks;e<i.length&&(i=ss(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){let s,r,n,a,o,l,h,c,d,u,f,{ctx:p,_longestTextCache:g}=this,m=[],b=[],x=Math.floor(e/si(e,i)),_=0,y=0;for(s=0;s<e;s+=x){if(a=t[s].label,p.font=l=(o=this._resolveTickFontOptions(s)).string,h=g[l]=g[l]||{data:{},gc:[]},c=o.lineHeight,d=u=0,I(a)||F(a)){if(F(a))for(r=0,n=a.length;r<n;++r)I(f=a[r])||F(f)||(d=t8(p,h.data,h.gc,d,f),u+=c)}else d=t8(p,h.data,h.gc,d,a),u=c;m.push(d),b.push(u),_=Math.max(d,_),y=Math.max(u,y)}$(g,t=>{let i,s=t.gc,r=s.length/2;if(r>e){for(i=0;i<r;++i)delete t.data[s[i]];s.splice(0,r)}});let v=m.indexOf(_),M=b.indexOf(y),w=t=>({width:m[t]||0,height:b[t]||0});return{first:w(0),last:w(e-1),widest:w(v),highest:w(M),widths:m,heights:b}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);let e=this._startPixel+t*this._length;return tP(this._alignToPixels?t6(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){let e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){var e,i;let s=this.ticks||[];if(t>=0&&t<s.length){let i=s[t];return i.$context||(e=this.getContext(),i.$context=em(e,{tick:i,index:t,type:"tick"}))}return this.$context||(this.$context=(i=this.chart.getContext(),em(i,{scale:this,type:"scale"})))}_tickSize(){let t=this.options.ticks,e=tx(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),r=this._getLabelSizes(),n=t.autoSkipPadding||0,a=r?r.widest.width+n:0,o=r?r.highest.height+n:0;return this.isHorizontal()?o*i>a*s?a/i:o/s:o*s<a*i?o/i:a/s}_isVisible(){let t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){let e,i,s,r,n,a,o,l,h,c,d,u,f=this.axis,p=this.chart,g=this.options,{grid:m,position:b,border:x}=g,_=m.offset,y=this.isHorizontal(),v=this.ticks.length+ +!!_,M=sr(m),w=[],k=x.setContext(this.getContext()),P=k.display?k.width:0,E=P/2,S=function(t){return t6(p,t,P)};if("top"===b)e=S(this.bottom),a=this.bottom-M,l=e-E,c=S(t.top)+E,u=t.bottom;else if("bottom"===b)e=S(this.top),c=t.top,u=S(t.bottom)-E,a=e+E,l=this.top+M;else if("left"===b)e=S(this.right),n=this.right-M,o=e-E,h=S(t.left)+E,d=t.right;else if("right"===b)e=S(this.left),h=t.left,d=S(t.right)-E,n=e+E,o=this.left+M;else if("x"===f){if("center"===b)e=S((t.top+t.bottom)/2+.5);else if(z(b)){let t=Object.keys(b)[0],i=b[t];e=S(this.chart.scales[t].getPixelForValue(i))}c=t.top,u=t.bottom,l=(a=e+E)+M}else if("y"===f){if("center"===b)e=S((t.left+t.right)/2);else if(z(b)){let t=Object.keys(b)[0],i=b[t];e=S(this.chart.scales[t].getPixelForValue(i))}o=(n=e-E)-M,h=t.left,d=t.right}let O=V(g.ticks.maxTicksLimit,v),R=Math.max(1,Math.ceil(v/O));for(i=0;i<v;i+=R){let t=this.getContext(i),e=m.setContext(t),f=x.setContext(t),g=e.lineWidth,b=e.color,v=f.dash||[],M=f.dashOffset,k=e.tickWidth,P=e.tickColor,E=e.tickBorderDash||[],S=e.tickBorderDashOffset;void 0!==(s=function(t,e,i){let s,r=t.ticks.length,n=Math.min(e,r-1),a=t._startPixel,o=t._endPixel,l=t.getPixelForTick(n);if(!i||(s=1===r?Math.max(l-a,o-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,!((l+=n<e?s:-s)<a-1e-6)&&!(l>o+1e-6)))return l}(this,i,_))&&(r=t6(p,s,g),y?n=o=h=d=r:a=l=c=u=r,w.push({tx1:n,ty1:a,tx2:o,ty2:l,x1:h,y1:c,x2:d,y2:u,width:g,color:b,borderDash:v,borderDashOffset:M,tickWidth:k,tickColor:P,tickBorderDash:E,tickBorderDashOffset:S}))}return this._ticksLength=v,this._borderValue=e,w}_computeLabelItems(t){let e,i,s,r,n,a,o,l,h,c,d,u=this.axis,f=this.options,{position:p,ticks:g}=f,m=this.isHorizontal(),b=this.ticks,{align:x,crossAlign:_,padding:y,mirror:v}=g,M=sr(f.grid),w=M+y,k=v?-y:w,P=-tx(this.labelRotation),E=[],S="middle";if("top"===p)n=this.bottom-k,a=this._getXAxisLabelAlignment();else if("bottom"===p)n=this.top+k,a=this._getXAxisLabelAlignment();else if("left"===p){let t=this._getYAxisLabelAlignment(M);a=t.textAlign,r=t.x}else if("right"===p){let t=this._getYAxisLabelAlignment(M);a=t.textAlign,r=t.x}else if("x"===u){if("center"===p)n=(t.top+t.bottom)/2+w;else if(z(p)){let t=Object.keys(p)[0],e=p[t];n=this.chart.scales[t].getPixelForValue(e)+w}a=this._getXAxisLabelAlignment()}else if("y"===u){if("center"===p)r=(t.left+t.right)/2-w;else if(z(p)){let t=Object.keys(p)[0],e=p[t];r=this.chart.scales[t].getPixelForValue(e)}a=this._getYAxisLabelAlignment(M).textAlign}"y"===u&&("start"===x?S="top":"end"===x&&(S="bottom"));let O=this._getLabelSizes();for(e=0,i=b.length;e<i;++e){let t;s=b[e].label;let u=g.setContext(this.getContext(e));o=this.getPixelForTick(e)+g.labelOffset,h=(l=this._resolveTickFontOptions(e)).lineHeight;let f=(c=F(s)?s.length:1)/2,x=u.color,y=u.textStrokeColor,M=u.textStrokeWidth,w=a;if(m?(r=o,"inner"===a&&(w=e===i-1?this.options.reverse?"left":"right":0===e?this.options.reverse?"right":"left":"center"),d="top"===p?"near"===_||0!==P?-c*h+h/2:"center"===_?-O.highest.height/2-f*h+h:-O.highest.height+h/2:"near"===_||0!==P?h/2:"center"===_?O.highest.height/2-f*h:O.highest.height-c*h,v&&(d*=-1),0===P||u.showLabelBackdrop||(r+=h/2*Math.sin(P))):(n=o,d=(1-c)*h/2),u.showLabelBackdrop){let s=ef(u.backdropPadding),r=O.heights[e],n=O.widths[e],o=d-s.top,l=0-s.left;switch(S){case"middle":o-=r/2;break;case"bottom":o-=r}switch(a){case"center":l-=n/2;break;case"right":l-=n;break;case"inner":e===i-1?l-=n:e>0&&(l-=n/2)}t={left:l,top:o,width:n+s.width,height:r+s.height,color:u.backdropColor}}E.push({label:s,font:l,textOffset:d,options:{rotation:P,color:x,strokeColor:y,strokeWidth:M,textAlign:w,textBaseline:S,translation:[r,n],backdrop:t}})}return E}_getXAxisLabelAlignment(){let{position:t,ticks:e}=this.options;if(-tx(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){let e,i,{position:s,ticks:{crossAlign:r,mirror:n,padding:a}}=this.options,o=this._getLabelSizes(),l=t+a,h=o.widest.width;return"left"===s?n?(i=this.right+a,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i+=h)):(i=this.right-l,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i=this.left)):"right"===s?n?(i=this.left+a,"near"===r?e="right":"center"===r?(e="center",i-=h/2):(e="left",i-=h)):(i=this.left+l,"near"===r?e="left":"center"===r?(e="center",i+=h/2):(e="right",i=this.right)):e="right",{textAlign:e,x:i}}_computeLabelArea(){if(this.options.ticks.mirror)return;let t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){let{ctx:t,options:{backgroundColor:e},left:i,top:s,width:r,height:n}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,r,n),t.restore())}getLineWidthForValue(t){let e=this.options.grid;if(!this._isVisible()||!e.display)return 0;let i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){let e,i,s=this.options.grid,r=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t)),a=(t,e,i)=>{i.width&&i.color&&(r.save(),r.lineWidth=i.width,r.strokeStyle=i.color,r.setLineDash(i.borderDash||[]),r.lineDashOffset=i.borderDashOffset,r.beginPath(),r.moveTo(t.x,t.y),r.lineTo(e.x,e.y),r.stroke(),r.restore())};if(s.display)for(e=0,i=n.length;e<i;++e){let t=n[e];s.drawOnChartArea&&a({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),s.drawTicks&&a({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){let t,e,i,s,{chart:r,ctx:n,options:{border:a,grid:o}}=this,l=a.setContext(this.getContext()),h=a.display?l.width:0;if(!h)return;let c=o.setContext(this.getContext(0)).lineWidth,d=this._borderValue;this.isHorizontal()?(t=t6(r,this.left,h)-h/2,e=t6(r,this.right,c)+c/2,i=s=d):(i=t6(r,this.top,h)-h/2,s=t6(r,this.bottom,c)+c/2,t=e=d),n.save(),n.lineWidth=l.width,n.strokeStyle=l.color,n.beginPath(),n.moveTo(t,i),n.lineTo(e,s),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;let e=this.ctx,i=this._computeLabelArea();for(let s of(i&&ee(e,i),this.getLabelItems(t))){let t=s.options,i=s.font;en(e,s.label,0,s.textOffset,i,t)}i&&ei(e)}drawTitle(){let t,{ctx:e,options:{position:i,title:s,reverse:r}}=this;if(!s.display)return;let n=ep(s.font),a=ef(s.padding),o=s.align,l=n.lineHeight/2;"bottom"===i||"center"===i||z(i)?(l+=a.bottom,F(s.text)&&(l+=n.lineHeight*(s.text.length-1))):l+=a.top;let{titleX:h,titleY:c,maxWidth:d,rotation:u}=function(t,e,i,s){let r,n,a,{top:o,left:l,bottom:h,right:c,chart:d}=t,{chartArea:u,scales:f}=d,p=0,g=h-o,m=c-l;if(t.isHorizontal()){if(n=tF(s,l,c),z(i)){let t=Object.keys(i)[0],s=i[t];a=f[t].getPixelForValue(s)+g-e}else a="center"===i?(u.bottom+u.top)/2+g-e:se(t,i,e);r=c-l}else{if(z(i)){let t=Object.keys(i)[0],s=i[t];n=f[t].getPixelForValue(s)-m+e}else n="center"===i?(u.left+u.right)/2-m+e:se(t,i,e);a=tF(s,h,o),p="left"===i?-th:th}return{titleX:n,titleY:a,maxWidth:r,rotation:p}}(this,l,i,o);en(e,s.text,0,0,n,{color:s.color,maxWidth:d,rotation:u,textAlign:(t=tI(o),(r&&"right"!==i||!r&&"right"===i)&&(t=st(t)),t),textBaseline:"middle",translation:[h,c]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){let t=this.options,e=t.ticks&&t.ticks.z||0,i=V(t.grid&&t.grid.z,-1),s=V(t.border&&t.border.z,0);return this._isVisible()&&this.draw===sa.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){let e,i,s=this.chart.getSortedVisibleDatasetMetas(),r=this.axis+"AxisID",n=[];for(e=0,i=s.length;e<i;++e){let i=s[e];i[r]!==this.id||t&&i.type!==t||n.push(i)}return n}_resolveTickFontOptions(t){return ep(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){let t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class so{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){var e;let i,s=Object.getPrototypeOf(t);"id"in(e=s)&&"defaults"in e&&(i=this.register(s));let r=this.items,n=t.id,a=this.scope+"."+n;if(!n)throw Error("class does not have id: "+t);return n in r||(r[n]=t,function(t,e,i){var s,r;let n=K(Object.create(null),[i?t3.get(i):{},t3.get(e),t.defaults]);t3.set(e,n),t.defaultRoutes&&(s=e,Object.keys(r=t.defaultRoutes).forEach(t=>{let e=t.split("."),i=e.pop(),n=[s].concat(e).join("."),a=r[t].split("."),o=a.pop(),l=a.join(".");t3.route(n,i,l,o)})),t.descriptors&&t3.describe(e,t.descriptors)}(t,a,i),this.override&&t3.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){let e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in t3[s]&&(delete t3[s][i],this.override&&delete tJ[i])}}class sl{constructor(){this.controllers=new so(ip,"datasets",!0),this.elements=new so(i7,"elements"),this.plugins=new so(Object,"plugins"),this.scales=new so(sa,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{let s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):$(e,e=>{let s=i||this._getRegistryForType(e);this._exec(t,s,e)})})}_exec(t,e,i){let s=tt(t);H(i["before"+s],[],i),e[t](i),H(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){let i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){let s=e.get(t);if(void 0===s)throw Error('"'+t+'" is not a registered '+i+".");return s}}var sh=new sl;class sc{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));let r=s?this._descriptors(t).filter(s):this._descriptors(t),n=this._notify(r,t,e,i);return"afterDestroy"===e&&(this._notify(r,t,"stop"),this._notify(this._init,t,"uninstall")),n}_notify(t,e,i,s){for(let r of(s=s||{},t)){let t=r.plugin;if(!1===H(t[i],[e,s,r.options],t)&&s.cancelable)return!1}return!0}invalidate(){I(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;let e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){let i=t&&t.config,s=V(i.options&&i.options.plugins,{}),r=function(t){let e={},i=[],s=Object.keys(sh.plugins.items);for(let t=0;t<s.length;t++)i.push(sh.getPlugin(s[t]));let r=t.plugins||[];for(let t=0;t<r.length;t++){let s=r[t];-1===i.indexOf(s)&&(i.push(s),e[s.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,r){let n=[],a=t.getContext();for(let l of e){var o;let e=l.id,h=(o=s[e],r||!1!==o?!0===o?{}:o:null);null!==h&&n.push({plugin:l,options:function(t,{plugin:e,local:i},s,r){let n=t.pluginScopeKeys(e),a=t.getOptionScopes(s,n);return i&&e.defaults&&a.push(e.defaults),t.createResolver(a,r,[""],{scriptable:!1,indexable:!1,allKeys:!0})}(t.config,{plugin:l,local:i[e]},h,a)})}return n}(t,r,s,e):[]}_notifyStateChanges(t){let e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function sd(t,e){let i=t3.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function su(t){if("x"===t||"y"===t||"r"===t)return t}function sf(t,...e){if(su(t))return t;for(let s of e){var i;let e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&su(t[0].toLowerCase());if(e)return e}throw Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function sp(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function sg(t){let e=t.options||(t.options={});e.plugins=V(e.plugins,{}),e.scales=function(t,e){let i=tJ[t.type]||{scales:{}},s=e.scales||{},r=sd(t.type,e),n=Object.create(null);return Object.keys(s).forEach(e=>{let a=s[e];if(!z(a))return console.error(`Invalid scale configuration for scale: ${e}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);let o=sf(e,a,function(t,e){if(e.data&&e.data.datasets){let i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return sp(t,"x",i[0])||sp(t,"y",i[0])}return{}}(e,t),t3.scales[a.type]),l=o===r?"_index_":"_value_",h=i.scales||{};n[e]=G(Object.create(null),[{axis:o},a,h[o],h[l]])}),t.data.datasets.forEach(i=>{let r=i.type||t.type,a=i.indexAxis||sd(r,e),o=(tJ[r]||{}).scales||{};Object.keys(o).forEach(t=>{let e,r=(e=t,"_index_"===t?e=a:"_value_"===t&&(e="x"===a?"y":"x"),e),l=i[r+"AxisID"]||r;n[l]=n[l]||Object.create(null),G(n[l],[{axis:r},s[l],o[t]])})}),Object.keys(n).forEach(t=>{let e=n[t];G(e,[t3.scales[e.type],t3.scale])}),n}(t,e)}function sm(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}let sb=new Map,sx=new Set;function s_(t,e){let i=sb.get(t);return i||(i=e(),sb.set(t,i),sx.add(i)),i}let sy=(t,e,i)=>{let s=J(e,i);void 0!==s&&t.add(s)};class sv{constructor(t){this._config=function(t){return(t=t||{}).data=sm(t.data),sg(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=sm(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){let t=this._config;this.clearCache(),sg(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return s_(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return s_(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return s_(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){let e=t.id,i=this.type;return s_(`${i}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){let i=this._scopeCache,s=i.get(t);return(!s||e)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){let{options:s,type:r}=this,n=this._cachedScopes(t,i),a=n.get(e);if(a)return a;let o=new Set;e.forEach(e=>{t&&(o.add(t),e.forEach(e=>sy(o,t,e))),e.forEach(t=>sy(o,s,t)),e.forEach(t=>sy(o,tJ[r]||{},t)),e.forEach(t=>sy(o,t3,t)),e.forEach(t=>sy(o,t0,t))});let l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),sx.has(e)&&n.set(e,l),l}chartOptionScopes(){let{options:t,type:e}=this;return[t,tJ[e]||{},t3.datasets[e]||{},{type:e},t3,t0]}resolveNamedOptions(t,e,i,s=[""]){let r={$shared:!0},{resolver:n,subPrefixes:a}=sM(this._resolverCache,t,s),o=n;if(function(t,e){let{isScriptable:i,isIndexable:s}=e_(t);for(let r of e){let e=i(r),n=s(r),a=(n||e)&&t[r];if(e&&(ti(a)||sw(a))||n&&F(a))return!0}return!1}(n,e)){r.$shared=!1,i=ti(i)?i():i;let e=this.createResolver(t,i,a);o=ex(n,i,e)}for(let t of e)r[t]=o[t];return r}createResolver(t,e,i=[""],s){let{resolver:r}=sM(this._resolverCache,t,i);return z(e)?ex(r,e,void 0,s):r}}function sM(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));let r=i.join(),n=s.get(r);return n||(n={resolver:eb(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},s.set(r,n)),n}let sw=t=>z(t)&&Object.getOwnPropertyNames(t).some(e=>ti(t[e])),sk=["top","bottom","left","right","chartArea"];function sP(t,e){return"top"===t||"bottom"===t||-1===sk.indexOf(t)&&"x"===e}function sE(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function sS(t){let e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),H(i&&i.onComplete,[t],e)}function sO(t){let e=t.chart,i=e.options.animation;H(i&&i.onProgress,[t],e)}function sR(t){return eD()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}let sA={},sT=t=>{let e=sR(t);return Object.values(sA).filter(t=>t.canvas===e).pop()};class sC{static defaults=t3;static instances=sA;static overrides=tJ;static registry=sh;static version="4.5.0";static getChart=sT;static register(...t){sh.add(...t),sD()}static unregister(...t){sh.remove(...t),sD()}constructor(t,e){let i=this.config=new sv(e),s=sR(t),r=sT(s);if(r)throw Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let n=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||(!eD()||"undefined"!=typeof OffscreenCanvas&&s instanceof OffscreenCanvas?iX:i4)),this.platform.updateConfig(i);let a=this.platform.acquireContext(s,n.aspectRatio),o=a&&a.canvas,l=o&&o.height,h=o&&o.width;if(this.id=L(),this.ctx=a,this.canvas=o,this.width=h,this.height=l,this._options=n,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new sc,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}(t=>this.update(t),n.resizeDelay||0),this._dataChanges=[],sA[this.id]=this,!a||!o)return void console.error("Failed to create chart: can't acquire context from the given item");e6.listen(this,"complete",sS),e6.listen(this,"progress",sO),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:r}=this;return I(t)?e&&r?r:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return sh}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():eW(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return t4(this.canvas,this.ctx),this}stop(){return e6.stop(this),this}resize(t,e){e6.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){let i=this.options,s=this.canvas,r=i.maintainAspectRatio&&this.aspectRatio,n=this.platform.getMaximumSize(s,t,e,r),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=n.width,this.height=n.height,this._aspectRatio=this.aspectRatio,eW(this,a,!0)&&(this.notifyPlugins("resize",{size:n}),H(i.onResize,[this,n],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){$(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){let t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{}),r=[];e&&(r=r.concat(Object.keys(e).map(t=>{let i=e[t],s=sf(t,i),r="r"===s,n="x"===s;return{options:i,dposition:r?"chartArea":n?"bottom":"left",dtype:r?"radialLinear":n?"category":"linear"}}))),$(r,e=>{let r=e.options,n=r.id,a=sf(n,r),o=V(r.type,e.dtype);(void 0===r.position||sP(r.position,a)!==sP(e.dposition))&&(r.position=e.dposition),s[n]=!0;let l=null;n in i&&i[n].type===o?l=i[n]:i[(l=new(sh.getScale(o))({id:n,type:o,ctx:this.ctx,chart:this})).id]=l,l.init(r,t)}),$(s,(t,e)=>{t||delete i[e]}),$(i,t=>{iU.configure(this,t,t.options),iU.addBox(this,t)})}_updateMetasets(){let t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(sE("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){let t,e,i=[],s=this.data.datasets;for(this._removeUnreferencedMetasets(),t=0,e=s.length;t<e;t++){let e=s[t],r=this.getDatasetMeta(t),n=e.type||this.config.type;if(r.type&&r.type!==n&&(this._destroyDatasetMeta(t),r=this.getDatasetMeta(t)),r.type=n,r.indexAxis=e.indexAxis||sd(n,this.options),r.order=e.order||0,r.index=t,r.label=""+e.label,r.visible=this.isDatasetVisible(t),r.controller)r.controller.updateIndex(t),r.controller.linkScales();else{let e=sh.getController(n),{datasetElementType:s,dataElementType:a}=t3.datasets[n];Object.assign(e,{dataElementType:sh.getElement(a),datasetElementType:s&&sh.getElement(s)}),r.controller=new e(this,t),i.push(r.controller)}}return this._updateMetasets(),i}_resetElements(){$(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let e=this.config;e.update();let i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let n=0;for(let t=0,e=this.data.datasets.length;t<e;t++){let{controller:e}=this.getDatasetMeta(t),i=!s&&-1===r.indexOf(e);e.buildOrUpdateElements(i),n=Math.max(+e.getMaxOverflow(),n)}n=this._minPadding=i.layout.autoPadding?n:0,this._updateLayout(n),s||$(r,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(sE("z","_idx"));let{_active:a,_lastEvent:o}=this;o?this._eventHandler(o,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){$(this.scales,t=>{iU.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options;ts(new Set(Object.keys(this._listeners)),new Set(t.events))&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this;for(let{method:i,start:s,count:r}of this._getUniformDataChanges()||[]){var e="_removeElements"===i?-r:r;for(let i of Object.keys(t)){let r=+i;if(r>=s){let n=t[i];delete t[i],(e>0||r>s)&&(t[r+e]=n)}}}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),s=i(0);for(let t=1;t<e;t++)if(!ts(s,i(t)))return;return Array.from(s).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;iU.update(this,this.width,this.height,t);let e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],$(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,ti(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){let i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(e6.has(this)?this.attached&&!e6.running(this)&&e6.start(this):(this.draw(),sS({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0||!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;let e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let e,i,s=this._sortedMetasets,r=[];for(e=0,i=s.length;e<i;++e){let i=s[e];(!t||i.visible)&&r.push(i)}return r}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;let t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let e=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=e3(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(s&&ee(e,s),t.controller.draw(),s&&ei(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return et(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){let r=iI.modes[e];return"function"==typeof r?r(this,t,i,s):[]}getDatasetMeta(t){let e=this.data.datasets[t],i=this._metasets,s=i.filter(t=>t&&t._dataset===e).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=em(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let e=this.data.datasets[t];if(!e)return!1;let i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){let s=i?"show":"hide",r=this.getDatasetMeta(t),n=r.controller._resolveAnimations(void 0,s);te(e)?(r.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),n.update(r,{visible:i}),this.update(e=>e.datasetIndex===t?s:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){let e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),e6.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),t4(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete sA[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};$(this.options.events,t=>i(t,s))}bindResponsiveEvents(){let t;this._responsiveListeners||(this._responsiveListeners={});let e=this._responsiveListeners,i=this.platform,s=(t,s)=>{i.addEventListener(this,t,s),e[t]=s},r=(t,s)=>{e[t]&&(i.removeEventListener(this,t,s),delete e[t])},n=(t,e)=>{this.canvas&&this.resize(t,e)},a=()=>{r("attach",a),this.attached=!0,this.resize(),s("resize",n),s("detach",t)};t=()=>{this.attached=!1,r("resize",n),this._stop(),this._resize(0,0),s("attach",a)},i.isAttached(this.canvas)?a():t()}unbindEvents(){$(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},$(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){let s,r,n,a=i?"set":"remove";for("dataset"===e&&this.getDatasetMeta(t[0].datasetIndex).controller["_"+a+"DatasetHoverStyle"](),r=0,n=t.length;r<n;++r){let e=(s=t[r])&&this.getDatasetMeta(s.datasetIndex).controller;e&&e[a+"HoverStyle"](s.element,s.datasetIndex,s.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{let i=this.getDatasetMeta(t);if(!i)throw Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});U(i,e)||(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){let s=this.options.hover,r=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),n=r(e,t),a=i?t:r(t,e);n.length&&this.updateHoverStyle(n,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,e){let i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;let r=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(r||i.changed)&&this.render(),this}_handleEvent(t,e,i){var s;let{_active:r=[],options:n}=this,a=this._getActiveElements(t,r,i,e),o="mouseup"===t.type||"click"===t.type||"contextmenu"===t.type,l=(s=this._lastEvent,i&&"mouseout"!==t.type?o?s:t:null);i&&(this._lastEvent=null,H(n.onHover,[t,a,this],this),o&&H(n.onClick,[t,a,this],this));let h=!U(a,r);return(h||e)&&(this._active=a,this._updateHoverStyles(a,r,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,s)}}function sD(){return $(sC.instances,t=>t._plugins.invalidate())}function sL(t,e,i,s){return{x:i+t*Math.cos(e),y:s+t*Math.sin(e)}}function sI(t,e,i,s,r,n){let{x:a,y:o,startAngle:l,pixelMargin:h,innerRadius:c}=e,d=Math.max(e.outerRadius+s+i-h,0),u=c>0?c+s+i+h:0,f=0,p=r-l;if(s){let t=d>0?d-s:0,e=((c>0?c-s:0)+t)/2;f=(p-(0!==e?p*e/(e+s):p))/2}let g=Math.max(.001,p*d-i/tr)/d,m=(p-g)/2,b=l+m+f,x=r-m-f,{outerStart:_,outerEnd:y,innerStart:v,innerEnd:M}=function(t,e,i,s){let r=ec(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),n=(i-e)/2,a=Math.min(n,s*e/2),o=t=>{let e=(i-Math.min(n,t))*s/2;return tP(t,0,Math.min(n,e))};return{outerStart:o(r.outerStart),outerEnd:o(r.outerEnd),innerStart:tP(r.innerStart,0,a),innerEnd:tP(r.innerEnd,0,a)}}(e,u,d,x-b),w=d-_,k=d-y,P=b+_/w,E=x-y/k,S=u+v,O=u+M,R=b+v/S,A=x-M/O;if(t.beginPath(),n){let e=(P+E)/2;if(t.arc(a,o,d,P,e),t.arc(a,o,d,e,E),y>0){let e=sL(k,E,a,o);t.arc(e.x,e.y,y,E,x+th)}let i=sL(O,x,a,o);if(t.lineTo(i.x,i.y),M>0){let e=sL(O,A,a,o);t.arc(e.x,e.y,M,x+th,A+Math.PI)}let s=(x-M/u+(b+v/u))/2;if(t.arc(a,o,u,x-M/u,s,!0),t.arc(a,o,u,s,b+v/u,!0),v>0){let e=sL(S,R,a,o);t.arc(e.x,e.y,v,R+Math.PI,b-th)}let r=sL(w,b,a,o);if(t.lineTo(r.x,r.y),_>0){let e=sL(w,P,a,o);t.arc(e.x,e.y,_,b-th,P)}}else{t.moveTo(a,o);let e=Math.cos(P)*d+a,i=Math.sin(P)*d+o;t.lineTo(e,i);let s=Math.cos(E)*d+a,r=Math.sin(E)*d+o;t.lineTo(s,r)}t.closePath()}class sF extends i7{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){let{angle:s,distance:r}=ty(this.getProps(["x","y"],i),{x:t,y:e}),{startAngle:n,endAngle:a,innerRadius:o,outerRadius:l,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),c=(this.options.spacing+this.options.borderWidth)/2,d=V(h,a-n),u=tk(s,n,a)&&n!==a,f=d>=tn||u,p=tE(r,o+c,l+c);return f&&p}getCenterPoint(t){let{x:e,y:i,startAngle:s,endAngle:r,innerRadius:n,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:o,spacing:l}=this.options,h=(s+r)/2,c=(n+a+l+o)/2;return{x:e+Math.cos(h)*c,y:i+Math.sin(h)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){let{options:e,circumference:i}=this,s=(e.offset||0)/4,r=(e.spacing||0)/2,n=e.circular;if(this.pixelMargin=.33*("inner"===e.borderAlign),this.fullCircles=i>tn?Math.floor(i/tn):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();let a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*s,Math.sin(a)*s);let o=s*(1-Math.sin(Math.min(tr,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,s,r){let{fullCircles:n,startAngle:a,circumference:o}=e,l=e.endAngle;if(n){sI(t,e,i,s,l,r);for(let e=0;e<n;++e)t.fill();isNaN(o)||(l=a+(o%tn||tn))}sI(t,e,i,s,l,r),t.fill()}(t,this,o,r,n),function(t,e,i,s,r){let{fullCircles:n,startAngle:a,circumference:o,options:l}=e,{borderWidth:h,borderJoinStyle:c,borderDash:d,borderDashOffset:u,borderRadius:f}=l,p="inner"===l.borderAlign;if(!h)return;t.setLineDash(d||[]),t.lineDashOffset=u,p?(t.lineWidth=2*h,t.lineJoin=c||"round"):(t.lineWidth=h,t.lineJoin=c||"bevel");let g=e.endAngle;if(n){sI(t,e,i,s,g,r);for(let e=0;e<n;++e)t.stroke();isNaN(o)||(g=a+(o%tn||tn))}p&&function(t,e,i){let{startAngle:s,pixelMargin:r,x:n,y:a,outerRadius:o,innerRadius:l}=e,h=r/o;t.beginPath(),t.arc(n,a,o,s-h,i+h),l>r?(h=r/l,t.arc(n,a,l,i+h,s-h,!0)):t.arc(n,a,r,i+th,s-th),t.closePath(),t.clip()}(t,e,g),l.selfJoin&&g-a>=tr&&0===f&&"miter"!==c&&function(t,e,i){let{startAngle:s,x:r,y:n,outerRadius:a,innerRadius:o,options:l}=e,{borderWidth:h,borderJoinStyle:c}=l,d=Math.min(h/a,tw(s-i));if(t.beginPath(),t.arc(r,n,a-h/2,s+d/2,i-d/2),o>0){let e=Math.min(h/o,tw(s-i));t.arc(r,n,o+h/2,i-e/2,s+e/2,!0)}else{let e=Math.min(h/2,a*tw(s-i));if("round"===c)t.arc(r,n,e,i-tr/2,s+tr/2,!0);else if("bevel"===c){let a=2*e*e,o=-a*Math.cos(i+tr/2)+r,l=-a*Math.sin(i+tr/2)+n,h=a*Math.cos(s+tr/2)+r,c=a*Math.sin(s+tr/2)+n;t.lineTo(o,l),t.lineTo(h,c)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}(t,e,g),n||(sI(t,e,i,s,g,r),t.stroke())}(t,this,o,r,n),t.restore()}}function sz(t,e,i=e){t.lineCap=V(i.borderCapStyle,e.borderCapStyle),t.setLineDash(V(i.borderDash,e.borderDash)),t.lineDashOffset=V(i.borderDashOffset,e.borderDashOffset),t.lineJoin=V(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=V(i.borderWidth,e.borderWidth),t.strokeStyle=V(i.borderColor,e.borderColor)}function sN(t,e,i){t.lineTo(i.x,i.y)}function sj(t,e,i={}){let s=t.length,{start:r=0,end:n=s-1}=i,{start:a,end:o}=e,l=Math.max(r,a),h=Math.min(n,o);return{count:s,start:l,loop:e.loop,ilen:h<l&&!(r<a&&n<a||r>o&&n>o)?s+h-l:h-l}}function sV(t,e,i,s){let r,n,a,{points:o,options:l}=e,{count:h,start:c,loop:d,ilen:u}=sj(o,i,s),f=l.stepped?es:l.tension||"monotone"===l.cubicInterpolationMode?er:sN,{move:p=!0,reverse:g}=s||{};for(r=0;r<=u;++r)(n=o[(c+(g?u-r:r))%h]).skip||(p?(t.moveTo(n.x,n.y),p=!1):f(t,a,n,g,l.stepped),a=n);return d&&f(t,a,n=o[(c+(g?u:0))%h],g,l.stepped),!!d}function sB(t,e,i,s){let r,n,a,o,l,h,c=e.points,{count:d,start:u,ilen:f}=sj(c,i,s),{move:p=!0,reverse:g}=s||{},m=0,b=0,x=t=>(u+(g?f-t:t))%d,_=()=>{o!==l&&(t.lineTo(m,l),t.lineTo(m,o),t.lineTo(m,h))};for(p&&(n=c[x(0)],t.moveTo(n.x,n.y)),r=0;r<=f;++r){if((n=c[x(r)]).skip)continue;let e=n.x,i=n.y,s=0|e;s===a?(i<o?o=i:i>l&&(l=i),m=(b*m+e)/++b):(_(),t.lineTo(e,i),a=s,b=0,o=l=i),h=i}_()}function sW(t){let e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?sV:sB}let sH="function"==typeof Path2D;class s$ extends i7{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){let i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){let s=i.spanGaps?this._loop:this._fullLoop;!function(t,e,i,s,r){let n,a,o,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let i,s,r,n=eT(e),a=t.length,o=Array(a).fill(0),l=Array(a),h=eA(t,0);for(i=0;i<a;++i)if(s=r,r=h,h=eA(t,i+1),r){if(h){let t=h[e]-r[e];o[i]=0!==t?(h[n]-r[n])/t:0}l[i]=s?h?tf(o[i-1])!==tf(o[i])?0:(o[i-1]+o[i])/2:o[i-1]:o[i]}!function(t,e,i){let s,r,n,a,o,l=t.length,h=eA(t,0);for(let c=0;c<l-1;++c)if(o=h,h=eA(t,c+1),o&&h){if(tp(e[c],0,eR)){i[c]=i[c+1]=0;continue}(a=Math.pow(s=i[c]/e[c],2)+Math.pow(r=i[c+1]/e[c],2))<=9||(n=3/Math.sqrt(a),i[c]=s*n*e[c],i[c+1]=r*n*e[c])}}(t,o,l),function(t,e,i="x"){let s,r,n,a=eT(i),o=t.length,l=eA(t,0);for(let h=0;h<o;++h){if(r=n,n=l,l=eA(t,h+1),!n)continue;let o=n[i],c=n[a];r&&(s=(o-r[i])/3,n[`cp1${i}`]=o-s,n[`cp1${a}`]=c-s*e[h]),l&&(s=(l[i]-o)/3,n[`cp2${i}`]=o+s,n[`cp2${a}`]=c+s*e[h])}}(t,l,e)}(t,r);else{let i=s?t[t.length-1]:t[0];for(n=0,a=t.length;n<a;++n)l=function(t,e,i,s){let r=t.skip?e:t,n=i.skip?e:i,a=tv(e,r),o=tv(n,e),l=a/(a+o),h=o/(a+o);l=isNaN(l)?0:l,h=isNaN(h)?0:h;let c=s*l,d=s*h;return{previous:{x:e.x-c*(n.x-r.x),y:e.y-c*(n.y-r.y)},next:{x:e.x+d*(n.x-r.x),y:e.y+d*(n.y-r.y)}}}(i,o=t[n],t[Math.min(n+1,a-!s)%a],e.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,i=o}e.capBezierPoints&&function(t,e){let i,s,r,n,a,o=et(t[0],e);for(i=0,s=t.length;i<s;++i)a=n,n=o,o=i<s-1&&et(t[i+1],e),n&&(r=t[i],a&&(r.cp1x=eC(r.cp1x,e.left,e.right),r.cp1y=eC(r.cp1y,e.top,e.bottom)),o&&(r.cp2x=eC(r.cp2x,e.left,e.right),r.cp2y=eC(r.cp2y,e.top,e.bottom)))}(t,i)}(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){let i=t.points,s=t.options.spanGaps,r=i.length;if(!r)return[];let n=!!t._loop,{start:a,end:o}=function(t,e,i,s){let r=0,n=e-1;if(i&&!s)for(;r<e&&!t[r].skip;)r++;for(;r<e&&t[r].skip;)r++;for(r%=e,i&&(n+=r);n>r&&t[n%e].skip;)n--;return{start:r,end:n%=e}}(i,r,n,s);if(!0===s)return e1(t,[{start:a,end:o,loop:n}],i,e);let l=o<a?o+r:o,h=!!t._fullLoop&&0===a&&o===r-1;return e1(t,function(t,e,i,s){let r,n=t.length,a=[],o=e,l=t[e];for(r=e+1;r<=i;++r){let i=t[r%n];i.skip||i.stop?l.skip||(s=!1,a.push({start:e%n,end:(r-1)%n,loop:s}),e=o=i.stop?r:null):(o=r,l.skip&&(e=r)),l=i}return null!==o&&a.push({start:e%n,end:o%n,loop:s}),a}(i,a,l,h),i,e)}(this,this.options.segment))}first(){let t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){let t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){let i,s,r=this.options,n=t[e],a=this.points,o=e0(this,{property:e,start:n,end:n});if(!o.length)return;let l=[],h=r.stepped?eY:r.tension||"monotone"===r.cubicInterpolationMode?eX:eU;for(i=0,s=o.length;i<s;++i){let{start:s,end:c}=o[i],d=a[s],u=a[c];if(d===u){l.push(d);continue}let f=Math.abs((n-d[e])/(u[e]-d[e])),p=h(d,u,f,r.stepped);p[e]=t[e],l.push(p)}return 1===l.length?l[0]:l}pathSegment(t,e,i){return sW(this)(t,this,e,i)}path(t,e,i){let s=this.segments,r=sW(this),n=this._loop;for(let a of(e=e||0,i=i||this.points.length-e,s))n&=r(t,this,a,{start:e,end:e+i-1});return!!n}draw(t,e,i,s){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),function(t,e,i,s){if(sH&&!e.options.segment){let r;(r=e._path)||(r=e._path=new Path2D,e.path(r,i,s)&&r.closePath()),sz(t,e.options),t.stroke(r)}else{let{segments:r,options:n}=e,a=sW(e);for(let o of r)sz(t,n,o.style),t.beginPath(),a(t,e,o,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}}(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function sU(t,e,i,s){let r=t.options,{[i]:n}=t.getProps([i],s);return Math.abs(e-n)<r.radius+r.hitRadius}class sY extends i7{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){let s=this.options,{x:r,y:n}=this.getProps(["x","y"],i);return Math.pow(t-r,2)+Math.pow(e-n,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return sU(this,t,"x",e)}inYRange(t,e){return sU(this,t,"y",e)}getCenterPoint(t){let{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0,i=(e=Math.max(e,e&&t.hoverRadius||0))&&t.borderWidth||0;return(e+i)*2}draw(t,e){let i=this.options;!this.skip&&!(i.radius<.1)&&et(this,e,this.size(i)/2)&&(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,t7(t,i,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}function sX(t,e){let i,s,r,n,a,{x:o,y:l,base:h,width:c,height:d}=t.getProps(["x","y","base","width","height"],e);return t.horizontal?(a=d/2,i=Math.min(o,h),s=Math.max(o,h),r=l-a,n=l+a):(i=o-(a=c/2),s=o+a,r=Math.min(l,h),n=Math.max(l,h)),{left:i,top:r,right:s,bottom:n}}function sq(t,e,i,s){return t?0:tP(e,i,s)}function sK(t,e,i,s){let r=null===e,n=null===i,a=t&&!(r&&n)&&sX(t,s);return a&&(r||tE(e,a.left,a.right))&&(n||tE(i,a.top,a.bottom))}function sG(t,e){t.rect(e.x,e.y,e.w,e.h)}function sQ(t,e,i={}){let s=t.x!==i.x?-e:0,r=t.y!==i.y?-e:0,n=(t.x+t.w!==i.x+i.w?e:0)-s,a=(t.y+t.h!==i.y+i.h?e:0)-r;return{x:t.x+s,y:t.y+r,w:t.w+n,h:t.h+a,radius:t.radius}}class sZ extends i7{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){var e;let{inflateAmount:i,options:{borderColor:s,backgroundColor:r}}=this,{inner:n,outer:a}=function(t){let e=sX(t),i=e.right-e.left,s=e.bottom-e.top,r=function(t,e,i){let s=t.options.borderWidth,r=t.borderSkipped,n=ed(s);return{t:sq(r.top,n.top,0,i),r:sq(r.right,n.right,0,e),b:sq(r.bottom,n.bottom,0,i),l:sq(r.left,n.left,0,e)}}(t,i/2,s/2),n=function(t,e,i){let{enableBorderRadius:s}=t.getProps(["enableBorderRadius"]),r=t.options.borderRadius,n=eu(r),a=Math.min(e,i),o=t.borderSkipped,l=s||z(r);return{topLeft:sq(!l||o.top||o.left,n.topLeft,0,a),topRight:sq(!l||o.top||o.right,n.topRight,0,a),bottomLeft:sq(!l||o.bottom||o.left,n.bottomLeft,0,a),bottomRight:sq(!l||o.bottom||o.right,n.bottomRight,0,a)}}(t,i/2,s/2);return{outer:{x:e.left,y:e.top,w:i,h:s,radius:n},inner:{x:e.left+r.l,y:e.top+r.t,w:i-r.l-r.r,h:s-r.t-r.b,radius:{topLeft:Math.max(0,n.topLeft-Math.max(r.t,r.l)),topRight:Math.max(0,n.topRight-Math.max(r.t,r.r)),bottomLeft:Math.max(0,n.bottomLeft-Math.max(r.b,r.l)),bottomRight:Math.max(0,n.bottomRight-Math.max(r.b,r.r))}}}}(this),o=(e=a.radius).topLeft||e.topRight||e.bottomLeft||e.bottomRight?ea:sG;t.save(),(a.w!==n.w||a.h!==n.h)&&(t.beginPath(),o(t,sQ(a,i,n)),t.clip(),o(t,sQ(n,-i,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),o(t,sQ(n,i)),t.fillStyle=r,t.fill(),t.restore()}inRange(t,e,i){return sK(this,t,e,i)}inXRange(t,e){return sK(this,t,null,e)}inYRange(t,e){return sK(this,null,t,e)}getCenterPoint(t){let{x:e,y:i,base:s,horizontal:r}=this.getProps(["x","y","base","horizontal"],t);return{x:r?(e+s)/2:e,y:r?i:(i+s)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}}let sJ=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],s0=sJ.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function s1(t,e,i,s){if(s)return;let r=e[t],n=i[t];return"angle"===t&&(r=tw(r),n=tw(n)),{property:t,start:r,end:n}}function s2(t,e,i){for(;e>t;e--){let t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function s5(t,e,i,s){return t&&e?s(t[i],e[i]):t?t[i]:e?e[i]:0}function s3(t,e){let i=[],s=!1;return F(t)?(s=!0,i=t):i=function(t,e){let{x:i=null,y:s=null}=t||{},r=e.points,n=[];return e.segments.forEach(({start:t,end:e})=>{e=s2(t,e,r);let a=r[t],o=r[e];null!==s?(n.push({x:a.x,y:s}),n.push({x:o.x,y:s})):null!==i&&(n.push({x:i,y:a.y}),n.push({x:i,y:o.y}))}),n}(t,e),i.length?new s$({points:i,options:{tension:0},_loop:s,_fullLoop:s}):null}class s8{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){let{x:s,y:r,radius:n}=this;return e=e||{start:0,end:tn},t.arc(s,r,n,e.end,e.start,!0),!i.bounds}interpolate(t){let{x:e,y:i,radius:s}=this,r=t.angle;return{x:e+Math.cos(r)*s,y:i+Math.sin(r)*s,angle:r}}}function s6(t,e,i){let{segments:s,points:r}=e,n=!0,a=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=r[s],c=r[s2(s,l,r)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),(a=!!e.pathSegment(t,o,{move:a}))?t.closePath():t.lineTo(c.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function s4(t,e,i){let{segments:s,points:r}=e,n=!0,a=!1;for(let o of(t.beginPath(),s)){let{start:s,end:l}=o,h=r[s],c=r[s2(s,l,r)];n?(t.moveTo(h.x,h.y),n=!1):(t.lineTo(i,h.y),t.lineTo(h.x,h.y)),(a=!!e.pathSegment(t,o,{move:a}))?t.closePath():t.lineTo(i,c.y)}t.lineTo(i,e.first().y),t.closePath(),t.clip()}function s7(t,e){let{line:i,target:s,property:r,color:n,scale:a,clip:o}=e;for(let{source:e,target:l,start:h,end:c}of function(t,e,i){let s=t.segments,r=t.points,n=e.points,a=[];for(let t of s){let{start:s,end:o}=t;o=s2(s,o,r);let l=s1(i,r[s],r[o],t.loop);if(!e.segments){a.push({source:t,target:l,start:r[s],end:r[o]});continue}for(let s of e0(e,l)){let e=s1(i,n[s.start],n[s.end],s.loop);for(let n of eJ(t,r,e))a.push({source:n,target:s,start:{[i]:s5(l,e,"start",Math.max)},end:{[i]:s5(l,e,"end",Math.min)}})}}return a}(i,s,r)){let d,{style:{backgroundColor:u=n}={}}=e,f=!0!==s;t.save(),t.fillStyle=u,function(t,e,i,s){let r=e.chart.chartArea,{property:n,start:a,end:o}=s||{};if("x"===n||"y"===n){let e,s,l,h;"x"===n?(e=a,s=r.top,l=o,h=r.bottom):(e=r.left,s=a,l=r.right,h=o),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),s=Math.max(s,i.top),h=Math.min(h,i.bottom)),t.rect(e,s,l-e,h-s),t.clip()}}(t,a,o,f&&s1(r,h,c)),t.beginPath();let p=!!i.pathSegment(t,e);if(f){p?t.closePath():s9(t,s,c,r);let e=!!s.pathSegment(t,l,{move:p,reverse:!0});(d=p&&e)||s9(t,s,h,r)}t.closePath(),t.fill(d?"evenodd":"nonzero"),t.restore()}}function s9(t,e,i,s){let r=e.interpolate(i,s);r&&t.lineTo(r.x,r.y)}let rt=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}},re=(t,e)=>null!==t&&null!==e&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class ri extends i7{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let t=this.options.labels||{},e=H(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){let t,e,{options:i,ctx:s}=this;if(!i.display){this.width=this.height=0;return}let r=i.labels,n=ep(r.font),a=n.size,o=this._computeTitleHeight(),{boxWidth:l,itemHeight:h}=rt(r,a);s.font=n.string,this.isHorizontal()?(t=this.maxWidth,e=this._fitRows(o,a,l,h)+10):(e=this.maxHeight,t=this._fitCols(o,n,l,h)+10),this.width=Math.min(t,i.maxWidth||this.maxWidth),this.height=Math.min(e,i.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){let{ctx:r,maxWidth:n,options:{labels:{padding:a}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],h=s+a,c=t;r.textAlign="left",r.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach((t,f)=>{let p=i+e/2+r.measureText(t.text).width;(0===f||l[l.length-1]+p+2*a>n)&&(c+=h,l[l.length-(f>0?0:1)]=0,u+=h,d++),o[f]={left:0,top:u,row:d,width:p,height:s},l[l.length-1]+=p+a}),c}_fitCols(t,e,i,s){let{ctx:r,maxHeight:n,options:{labels:{padding:a}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],h=n-t,c=a,d=0,u=0,f=0,p=0;return this.legendItems.forEach((t,n)=>{var g,m,b,x,_,y,v,M,w,k,P,E;let S,O,{itemWidth:R,itemHeight:A}=(g=i,m=e,b=r,x=t,_=s,{itemWidth:(y=x,v=g,M=m,w=b,(S=y.text)&&"string"!=typeof S&&(S=S.reduce((t,e)=>t.length>e.length?t:e)),v+M.size/2+w.measureText(S).width),itemHeight:(k=_,P=x,E=m.lineHeight,O=k,"string"!=typeof P.text&&(O=rs(P,E)),O)});n>0&&u+A+2*a>h&&(c+=d+a,l.push({width:d,height:u}),f+=d+a,p++,d=u=0),o[n]={left:f,top:u,col:p,width:R,height:A},d=Math.max(d,R),u+=A+a}),c+=d,l.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;let t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:r}}=this,n=eq(r,this.left,this.width);if(this.isHorizontal()){let r=0,a=tF(i,this.left+s,this.right-this.lineWidths[r]);for(let o of e)r!==o.row&&(r=o.row,a=tF(i,this.left+s,this.right-this.lineWidths[r])),o.top+=this.top+t+s,o.left=n.leftForLtr(n.x(a),o.width),a+=o.width+s}else{let r=0,a=tF(i,this.top+t+s,this.bottom-this.columnSizes[r].height);for(let o of e)o.col!==r&&(r=o.col,a=tF(i,this.top+t+s,this.bottom-this.columnSizes[r].height)),o.top=a,o.left+=this.left+s,o.left=n.leftForLtr(n.x(o.left),o.width),a+=o.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){let t=this.ctx;ee(t,this),this._draw(),ei(t)}}_draw(){let t,{options:e,columnSizes:i,lineWidths:s,ctx:r}=this,{align:n,labels:a}=e,o=t3.color,l=eq(e.rtl,this.left,this.width),h=ep(a.font),{padding:c}=a,d=h.size,u=d/2;this.drawTitle(),r.textAlign=l.textAlign("left"),r.textBaseline="middle",r.lineWidth=.5,r.font=h.string;let{boxWidth:f,boxHeight:p,itemHeight:g}=rt(a,d),m=function(t,e,i){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;r.save();let s=V(i.lineWidth,1);if(r.fillStyle=V(i.fillStyle,o),r.lineCap=V(i.lineCap,"butt"),r.lineDashOffset=V(i.lineDashOffset,0),r.lineJoin=V(i.lineJoin,"miter"),r.lineWidth=s,r.strokeStyle=V(i.strokeStyle,o),r.setLineDash(V(i.lineDash,[])),a.usePointStyle){let n={radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s};t9(r,n,l.xPlus(t,f/2),e+u,a.pointStyleWidth&&f)}else{let n=e+Math.max((d-p)/2,0),a=l.leftForLtr(t,f),o=eu(i.borderRadius);r.beginPath(),Object.values(o).some(t=>0!==t)?ea(r,{x:a,y:n,w:f,h:p,radius:o}):r.rect(a,n,f,p),r.fill(),0!==s&&r.stroke()}r.restore()},b=function(t,e,i){en(r,i.text,t,e+g/2,h,{strikethrough:i.hidden,textAlign:l.textAlign(i.textAlign)})},x=this.isHorizontal(),_=this._computeTitleHeight();t=x?{x:tF(n,this.left+c,this.right-s[0]),y:this.top+c+_,line:0}:{x:this.left+c,y:tF(n,this.top+_+c,this.bottom-i[0].height),line:0},eK(this.ctx,e.textDirection);let y=g+c;this.legendItems.forEach((o,d)=>{r.strokeStyle=o.fontColor,r.fillStyle=o.fontColor;let p=r.measureText(o.text).width,g=l.textAlign(o.textAlign||(o.textAlign=a.textAlign)),v=f+u+p,M=t.x,w=t.y;if(l.setWidth(this.width),x?d>0&&M+v+c>this.right&&(w=t.y+=y,t.line++,M=t.x=tF(n,this.left+c,this.right-s[t.line])):d>0&&w+y>this.bottom&&(M=t.x=M+i[t.line].width+c,t.line++,w=t.y=tF(n,this.top+_+c,this.bottom-i[t.line].height)),m(l.x(M),w,o),M=tz(g,M+f+u,x?M+v:this.right,e.rtl),b(l.x(M),w,o),x)t.x+=v+c;else if("string"!=typeof o.text){let e=h.lineHeight;t.y+=rs(o,e)+c}else t.y+=y}),eG(this.ctx,e.textDirection)}drawTitle(){let t,e=this.options,i=e.title,s=ep(i.font),r=ef(i.padding);if(!i.display)return;let n=eq(e.rtl,this.left,this.width),a=this.ctx,o=i.position,l=s.size/2,h=r.top+l,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),t=this.top+h,c=tF(e.align,c,this.right-d);else{let i=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);t=h+tF(e.align,this.top,this.bottom-i-e.labels.padding-this._computeTitleHeight())}let u=tF(o,c,c+d);a.textAlign=n.textAlign(tI(o)),a.textBaseline="middle",a.strokeStyle=i.color,a.fillStyle=i.color,a.font=s.string,en(a,i.text,u,t,s)}_computeTitleHeight(){let t=this.options.title,e=ep(t.font),i=ef(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,r;if(tE(t,this.left,this.right)&&tE(e,this.top,this.bottom)){for(i=0,r=this.legendHitBoxes;i<r.length;++i)if(tE(t,(s=r[i]).left,s.left+s.width)&&tE(e,s.top,s.top+s.height))return this.legendItems[i]}return null}handleEvent(t){var e,i;let s=this.options;if(e=t.type,i=s,("mousemove"!==e&&"mouseout"!==e||!i.onHover&&!i.onLeave)&&(!i.onClick||"click"!==e&&"mouseup"!==e))return;let r=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){let e=this._hoveredItem,i=re(e,r);e&&!i&&H(s.onLeave,[t,e,this],this),this._hoveredItem=r,r&&!i&&H(s.onHover,[t,r,this],this)}else r&&H(s.onClick,[t,r,this],this)}}function rs(t,e){return e*(t.text?t.text.length:0)}var rr={id:"legend",_element:ri,start(t,e,i){let s=t.legend=new ri({ctx:t.ctx,options:i,chart:t});iU.configure(t,s,i),iU.addBox(t,s)},stop(t){iU.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){let s=t.legend;iU.configure(t,s,i),s.options=i},afterUpdate(t){let e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){let s=e.datasetIndex,r=i.chart;r.isDatasetVisible(s)?(r.hide(s),e.hidden=!0):(r.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){let e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:r,color:n,useBorderRadius:a,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{let l=t.controller.getStyle(i?0:void 0),h=ef(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:n,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:s||l.pointStyle,rotation:l.rotation,textAlign:r||l.textAlign,borderRadius:a&&(o||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class rn extends i7{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){let i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;let s=F(i.text)?i.text.length:1;this._padding=ef(i.padding);let r=s*ep(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){let t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){let e,i,s,{top:r,left:n,bottom:a,right:o,options:l}=this,h=l.align,c=0;return this.isHorizontal()?(i=tF(h,n,o),s=r+t,e=o-n):("left"===l.position?(i=n+t,s=tF(h,a,r),c=-.5*tr):(i=o-t,s=tF(h,r,a),c=.5*tr),e=a-r),{titleX:i,titleY:s,maxWidth:e,rotation:c}}draw(){let t=this.ctx,e=this.options;if(!e.display)return;let i=ep(e.font),s=i.lineHeight/2+this._padding.top,{titleX:r,titleY:n,maxWidth:a,rotation:o}=this._drawArgs(s);en(t,e.text,0,0,i,{color:e.color,maxWidth:a,rotation:o,textAlign:tI(e.align),textBaseline:"middle",translation:[r,n]})}}var ra={id:"title",_element:rn,start(t,e,i){let s=new rn({ctx:t.ctx,options:i,chart:t});iU.configure(t,s,i),iU.addBox(t,s),t.titleBlock=s},stop(t){let e=t.titleBlock;iU.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){let s=t.titleBlock;iU.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};new WeakMap;let ro={average(t){let e,i;if(!t.length)return!1;let s=new Set,r=0,n=0;for(e=0,i=t.length;e<i;++e){let i=t[e].element;if(i&&i.hasValue()){let t=i.tooltipPosition();s.add(t.x),r+=t.y,++n}}return 0!==n&&0!==s.size&&{x:[...s].reduce((t,e)=>t+e)/s.size,y:r/n}},nearest(t,e){let i,s,r;if(!t.length)return!1;let n=e.x,a=e.y,o=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){let s=t[i].element;if(s&&s.hasValue()){let t=tv(e,s.getCenterPoint());t<o&&(o=t,r=s)}}if(r){let t=r.tooltipPosition();n=t.x,a=t.y}return{x:n,y:a}}};function rl(t,e){return e&&(F(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function rh(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function rc(t,e){let i=t.chart.ctx,{body:s,footer:r,title:n}=t,{boxWidth:a,boxHeight:o}=e,l=ep(e.bodyFont),h=ep(e.titleFont),c=ep(e.footerFont),d=n.length,u=r.length,f=s.length,p=ef(e.padding),g=p.height,m=0,b=s.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);b+=t.beforeBody.length+t.afterBody.length,d&&(g+=d*h.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),b&&(g+=f*(e.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*e.bodySpacing),u&&(g+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let x=0,_=function(t){m=Math.max(m,i.measureText(t).width+x)};return i.save(),i.font=h.string,$(t.title,_),i.font=l.string,$(t.beforeBody.concat(t.afterBody),_),x=e.displayColors?a+2+e.boxPadding:0,$(s,t=>{$(t.before,_),$(t.lines,_),$(t.after,_)}),x=0,i.font=c.string,$(t.footer,_),i.restore(),{width:m+=p.width,height:g}}function rd(t,e,i){let s=i.yAlign||e.yAlign||function(t,e){let{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||function(t,e,i,s){let{x:r,width:n}=i,{width:a,chartArea:{left:o,right:l}}=t,h="center";return"center"===s?h=r<=(o+l)/2?"left":"right":r<=n/2?h="left":r>=a-n/2&&(h="right"),function(t,e,i,s){let{x:r,width:n}=s,a=i.caretSize+i.caretPadding;if("left"===t&&r+n+a>e.width||"right"===t&&r-n-a<0)return!0}(h,t,e,i)&&(h="center"),h}(t,e,i,s),yAlign:s}}function ru(t,e,i,s){let{caretSize:r,caretPadding:n,cornerRadius:a}=t,{xAlign:o,yAlign:l}=i,h=r+n,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=eu(a),p=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,o),g=function(t,e,i){let{y:s,height:r}=t;return"top"===e?s+=i:"bottom"===e?s-=r+i:s-=r/2,s}(e,l,h);return"center"===l?"left"===o?p+=h:"right"===o&&(p-=h):"left"===o?p-=Math.max(c,u)+r:"right"===o&&(p+=Math.max(d,f)+r),{x:tP(p,0,s.width-e.width),y:tP(g,0,s.height-e.height)}}function rf(t,e,i){let s=ef(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function rp(t,e){let i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}let rg={beforeTitle:D,title(t){if(t.length>0){let e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:D,beforeBody:D,beforeLabel:D,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");let i=t.formattedValue;return I(i)||(e+=i),e},labelColor(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){let e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:D,afterBody:D,beforeFooter:D,footer:D,afterFooter:D};function rm(t,e,i,s){let r=t[e].call(i,s);return void 0===r?rg[e].call(i,s):r}class rb extends i7{static positioners=ro;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,r=new it(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){var t;return this.$context||(this.$context=(t=this.chart.getContext(),em(t,{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"})))}getTitle(t,e){let{callbacks:i}=e,s=rm(i,"beforeTitle",this,t),r=rm(i,"title",this,t),n=rm(i,"afterTitle",this,t),a=[];return a=rl(a,rh(s)),a=rl(a,rh(r)),a=rl(a,rh(n))}getBeforeBody(t,e){return rl([],rh(rm(e.callbacks,"beforeBody",this,t)))}getBody(t,e){let{callbacks:i}=e,s=[];return $(t,t=>{let e={before:[],lines:[],after:[]},r=rp(i,t);rl(e.before,rh(rm(r,"beforeLabel",this,t))),rl(e.lines,rm(r,"label",this,t)),rl(e.after,rh(rm(r,"afterLabel",this,t))),s.push(e)}),s}getAfterBody(t,e){return rl([],rh(rm(e.callbacks,"afterBody",this,t)))}getFooter(t,e){let{callbacks:i}=e,s=rm(i,"beforeFooter",this,t),r=rm(i,"footer",this,t),n=rm(i,"afterFooter",this,t),a=[];return a=rl(a,rh(s)),a=rl(a,rh(r)),a=rl(a,rh(n))}_createItems(t){let e,i,s=this._active,r=this.chart.data,n=[],a=[],o=[],l=[];for(e=0,i=s.length;e<i;++e)l.push(function(t,e){let{element:i,datasetIndex:s,index:r}=e,n=t.getDatasetMeta(s).controller,{label:a,value:o}=n.getLabelAndValue(r);return{chart:t,label:a,parsed:n.getParsed(r),raw:t.data.datasets[s].data[r],formattedValue:o,dataset:n.getDataset(),dataIndex:r,datasetIndex:s,element:i}}(this.chart,s[e]));return t.filter&&(l=l.filter((e,i,s)=>t.filter(e,i,s,r))),t.itemSort&&(l=l.sort((e,i)=>t.itemSort(e,i,r))),$(l,e=>{let i=rp(t.callbacks,e);n.push(rm(i,"labelColor",this,e)),a.push(rm(i,"labelPointStyle",this,e)),o.push(rm(i,"labelTextColor",this,e))}),this.labelColors=n,this.labelPointStyles=a,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){let i,s=this.options.setContext(this.getContext()),r=this._active,n=[];if(r.length){let t=ro[s.position].call(this,r,this._eventPosition);n=this._createItems(s),this.title=this.getTitle(n,s),this.beforeBody=this.getBeforeBody(n,s),this.body=this.getBody(n,s),this.afterBody=this.getAfterBody(n,s),this.footer=this.getFooter(n,s);let e=this._size=rc(this,s),a=Object.assign({},t,e),o=rd(this.chart,s,a),l=ru(s,a,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,i={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(i={opacity:0});this._tooltipItems=n,this.$context=void 0,i&&this._resolveAnimations().update(this,i),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){let r=this.getCaretPosition(t,i,s);e.lineTo(r.x1,r.y1),e.lineTo(r.x2,r.y2),e.lineTo(r.x3,r.y3)}getCaretPosition(t,e,i){let s,r,n,a,o,l,{xAlign:h,yAlign:c}=this,{caretSize:d,cornerRadius:u}=i,{topLeft:f,topRight:p,bottomLeft:g,bottomRight:m}=eu(u),{x:b,y:x}=t,{width:_,height:y}=e;return"center"===c?(o=x+y/2,"left"===h?(r=(s=b)-d,a=o+d,l=o-d):(r=(s=b+_)+d,a=o-d,l=o+d),n=s):(r="left"===h?b+Math.max(f,g)+d:"right"===h?b+_-Math.max(p,m)-d:this.caretX,"top"===c?(o=(a=x)-d,s=r-d,n=r+d):(o=(a=x+y)+d,s=r+d,n=r-d),l=a),{x1:s,x2:r,x3:n,y1:a,y2:o,y3:l}}drawTitle(t,e,i){let s,r,n,a=this.title,o=a.length;if(o){let l=eq(i.rtl,this.x,this.width);for(n=0,t.x=rf(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",s=ep(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=s.string;n<o;++n)e.fillText(a[n],l.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+r,n+1===o&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,s,r){let n=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:o,boxWidth:l}=r,h=ep(r.bodyFont),c=rf(this,"left",r),d=s.x(c),u=o<h.lineHeight?(h.lineHeight-o)/2:0,f=e.y+u;if(r.usePointStyle){let e={radius:Math.min(l,o)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},i=s.leftForLtr(d,l)+l/2,h=f+o/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,t7(t,e,i,h),t.strokeStyle=n.borderColor,t.fillStyle=n.backgroundColor,t7(t,e,i,h)}else{t.lineWidth=z(n.borderWidth)?Math.max(...Object.values(n.borderWidth)):n.borderWidth||1,t.strokeStyle=n.borderColor,t.setLineDash(n.borderDash||[]),t.lineDashOffset=n.borderDashOffset||0;let e=s.leftForLtr(d,l),i=s.leftForLtr(s.xPlus(d,1),l-2),a=eu(n.borderRadius);Object.values(a).some(t=>0!==t)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,ea(t,{x:e,y:f,w:l,h:o,radius:a}),t.fill(),t.stroke(),t.fillStyle=n.backgroundColor,t.beginPath(),ea(t,{x:i,y:f+1,w:l-2,h:o-2,radius:a}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(e,f,l,o),t.strokeRect(e,f,l,o),t.fillStyle=n.backgroundColor,t.fillRect(i,f+1,l-2,o-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){let s,r,n,a,o,l,{body:h}=this,{bodySpacing:c,bodyAlign:d,displayColors:u,boxHeight:f,boxWidth:p,boxPadding:g}=i,m=ep(i.bodyFont),b=m.lineHeight,x=0,_=eq(i.rtl,this.x,this.width),y=function(i){e.fillText(i,_.x(t.x+x),t.y+b/2),t.y+=b+c},v=_.textAlign(d);for(e.textAlign=d,e.textBaseline="middle",e.font=m.string,t.x=rf(this,v,i),e.fillStyle=i.bodyColor,$(this.beforeBody,y),x=u&&"right"!==v?"center"===d?p/2+g:p+2+g:0,n=0,o=h.length;n<o;++n){for(s=h[n],e.fillStyle=this.labelTextColors[n],$(s.before,y),r=s.lines,u&&r.length&&(this._drawColorBox(e,t,n,_,i),b=Math.max(m.lineHeight,f)),a=0,l=r.length;a<l;++a)y(r[a]),b=m.lineHeight;$(s.after,y)}x=0,b=m.lineHeight,$(this.afterBody,y),t.y-=c}drawFooter(t,e,i){let s,r,n=this.footer,a=n.length;if(a){let o=eq(i.rtl,this.x,this.width);for(t.x=rf(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=o.textAlign(i.footerAlign),e.textBaseline="middle",s=ep(i.footerFont),e.fillStyle=i.footerColor,e.font=s.string,r=0;r<a;++r)e.fillText(n[r],o.x(t.x),t.y+s.lineHeight/2),t.y+=s.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){let{xAlign:r,yAlign:n}=this,{x:a,y:o}=t,{width:l,height:h}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=eu(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(a+c,o),"top"===n&&this.drawCaret(t,e,i,s),e.lineTo(a+l-d,o),e.quadraticCurveTo(a+l,o,a+l,o+d),"center"===n&&"right"===r&&this.drawCaret(t,e,i,s),e.lineTo(a+l,o+h-f),e.quadraticCurveTo(a+l,o+h,a+l-f,o+h),"bottom"===n&&this.drawCaret(t,e,i,s),e.lineTo(a+u,o+h),e.quadraticCurveTo(a,o+h,a,o+h-u),"center"===n&&"left"===r&&this.drawCaret(t,e,i,s),e.lineTo(a,o+c),e.quadraticCurveTo(a,o,a+c,o),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){let e=this.chart,i=this.$animations,s=i&&i.x,r=i&&i.y;if(s||r){let i=ro[t.position].call(this,this._active,this._eventPosition);if(!i)return;let n=this._size=rc(this,t),a=Object.assign({},i,this._size),o=rd(e,t,a),l=ru(t,a,o,e);(s._to!==l.x||r._to!==l.y)&&(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=n.width,this.height=n.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){let e=this.options.setContext(this.getContext()),i=this.opacity;if(!i)return;this._updateAnimationTarget(e);let s={width:this.width,height:this.height},r={x:this.x,y:this.y};i=.001>Math.abs(i)?0:i;let n=ef(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(r,t,s,e),eK(t,e.textDirection),r.y+=n.top,this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),eG(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){let i=this._active,s=t.map(({datasetIndex:t,index:e})=>{let i=this.chart.getDatasetMeta(t);if(!i)throw Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),r=!U(i,s),n=this._positionChanged(s,e);(r||n)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let s=this.options,r=this._active||[],n=this._getActiveElements(t,r,e,i),a=this._positionChanged(n,t),o=e||!U(n,r)||a;return o&&(this._active=n,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),o}_getActiveElements(t,e,i,s){let r=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));let n=this.chart.getElementsAtEventForMode(t,r.mode,r,i);return r.reverse&&n.reverse(),n}_positionChanged(t,e){let{caretX:i,caretY:s,options:r}=this,n=ro[r.position].call(this,t,e);return!1!==n&&(i!==n.x||s!==n.y)}}var rx={id:"tooltip",_element:rb,positioners:ro,afterInit(t,e,i){i&&(t.tooltip=new rb({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){let e=t.tooltip;if(e&&e._willRender()){let i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){let i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:rg},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};let r_=(t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i),ry=(t,e)=>null===t?null:tP(Math.round(t),0,e);function rv(t){let e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class rM extends sa{static id="category";static defaults={ticks:{callback:rv}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let e=this._addedLabels;if(e.length){let t=this.getLabels();for(let{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(I(t))return null;let i=this.getLabels();return ry(e=isFinite(e)&&i[e]===t?e:function(t,e,i,s){let r=t.indexOf(e);return -1===r?r_(t,e,i,s):r!==t.lastIndexOf(e)?i:r}(i,t,V(e,t),this._addedLabels),i.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){let t=this.min,e=this.max,i=this.options.offset,s=[],r=this.getLabels();r=0===t&&e===r.length-1?r:r.slice(t,e+1),this._valueRange=Math.max(r.length-!i,1),this._startValue=this.min-.5*!!i;for(let i=t;i<=e;i++)s.push({value:i});return s}getLabelForValue(t){return rv.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function rw(t,e,{horizontal:i,minRotation:s}){let r=tx(s),n=(i?Math.sin(r):Math.cos(r))||.001,a=.75*e*(""+t).length;return Math.min(e/n,a)}class rk extends sa{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return I(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){let{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds(),{min:s,max:r}=this,n=t=>s=e?s:t,a=t=>r=i?r:t;if(t){let t=tf(s),e=tf(r);t<0&&e<0?a(0):t>0&&e>0&&n(0)}if(s===r){let e=0===r?1:Math.abs(.05*r);a(r+e),t||n(s-e)}this.min=s,this.max=r}getTickLimit(){let t,{maxTicksLimit:e,stepSize:i}=this.options.ticks;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1)>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${t} ticks. Limiting to 1000.`),t=1e3):(t=this.computeTickLimit(),e=e||11),e&&(t=Math.min(e,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let t=this.options,e=t.ticks,i=this.getTickLimit(),s=function(t,e){let i,s,r,n,a=[],{bounds:o,step:l,min:h,max:c,precision:d,count:u,maxTicks:f,maxDigits:p,includeBounds:g}=t,m=l||1,b=f-1,{min:x,max:_}=e,y=!I(h),v=!I(c),M=!I(u),w=(_-x)/(p+1),k=tg((_-x)/b/m)*m;if(k<1e-14&&!y&&!v)return[{value:x},{value:_}];(n=Math.ceil(_/k)-Math.floor(x/k))>b&&(k=tg(n*k/b/m)*m),I(d)||(k=Math.ceil(k*(i=Math.pow(10,d)))/i),"ticks"===o?(s=Math.floor(x/k)*k,r=Math.ceil(_/k)*k):(s=x,r=_),y&&v&&l&&function(t,e){let i=Math.round(t);return i-e<=t&&i+e>=t}((c-h)/l,k/1e3)?(n=Math.round(Math.min((c-h)/k,f)),k=(c-h)/n,s=h,r=c):M?(s=y?h:s,k=((r=v?c:r)-s)/(n=u-1)):n=tp(n=(r-s)/k,Math.round(n),k/1e3)?Math.round(n):Math.ceil(n);let P=Math.max(t_(k),t_(s));s=Math.round(s*(i=Math.pow(10,I(d)?P:d)))/i,r=Math.round(r*i)/i;let E=0;for(y&&(g&&s!==h?(a.push({value:h}),s<h&&E++,tp(Math.round((s+E*k)*i)/i,h,rw(h,w,t))&&E++):s<h&&E++);E<n;++E){let t=Math.round((s+E*k)*i)/i;if(v&&t>c)break;a.push({value:t})}return v&&g&&r!==c?a.length&&tp(a[a.length-1].value,c,rw(c,w,t))?a[a.length-1].value=c:a.push({value:c}):v&&r!==c||a.push({value:r}),a}({maxTicks:i=Math.max(2,i),bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&tb(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){let t=this.ticks,e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){let s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return tG(t,this.chart.options.locale,this.options.ticks.format)}}class rP extends rk{static id="linear";static defaults={ticks:{callback:tZ.formatters.numeric}};determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=N(t)?t:0,this.max=N(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){let t=this.isHorizontal(),e=t?this.width:this.height,i=tx(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001;return Math.ceil(e/Math.min(40,this._resolveTickFontOptions(0).lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}let rE=t=>Math.floor(tu(t)),rS=(t,e)=>Math.pow(10,rE(t)+e);function rO(t){return 1==t/Math.pow(10,rE(t))}function rR(t,e,i){let s=Math.pow(10,i),r=Math.floor(t/s);return Math.ceil(e/s)-r}class rA extends sa{static id="logarithmic";static defaults={ticks:{callback:tZ.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){let i=rk.prototype.parse.apply(this,[t,e]);if(0===i){this._zero=!0;return}return N(i)&&i>0?i:null}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!0);this.min=N(t)?Math.max(0,t):null,this.max=N(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!N(this._userMin)&&(this.min=t===rS(this.min,0)?rS(this.min,-1):rS(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:t,maxDefined:e}=this.getUserBounds(),i=this.min,s=this.max,r=e=>i=t?i:e,n=t=>s=e?s:t;i===s&&(i<=0?(r(1),n(10)):(r(rS(i,-1)),n(rS(s,1)))),i<=0&&r(rS(s,-1)),s<=0&&n(rS(i,1)),this.min=i,this.max=s}buildTicks(){let t=this.options,e=function(t,{min:e,max:i}){e=j(t.min,e);let s=[],r=rE(e),n=function(t,e){let i=rE(e-t);for(;rR(t,e,i)>10;)i++;for(;10>rR(t,e,i);)i--;return Math.min(i,rE(t))}(e,i),a=n<0?Math.pow(10,Math.abs(n)):1,o=Math.pow(10,n),l=r>n?Math.pow(10,r):0,h=Math.round((e-l)*a)/a,c=Math.floor((e-l)/o/10)*o*10,d=Math.floor((h-c)/Math.pow(10,n)),u=j(t.min,Math.round((l+c+d*Math.pow(10,n))*a)/a);for(;u<i;)s.push({value:u,major:rO(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(d=2,a=++n>=0?1:a),u=Math.round((l+c+d*Math.pow(10,n))*a)/a;let f=j(t.max,u);return s.push({value:f,major:rO(f),significand:d}),s}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&tb(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":tG(t,this.chart.options.locale,this.options.ticks.format)}configure(){let t=this.min;super.configure(),this._startValue=tu(t),this._valueRange=tu(this.max)-tu(t)}getPixelForValue(t){return((void 0===t||0===t)&&(t=this.min),null===t||isNaN(t))?NaN:this.getPixelForDecimal(t===this.min?0:(tu(t)-this._startValue)/this._valueRange)}getValueForPixel(t){let e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function rT(t){let e=t.ticks;if(e.display&&t.display){let t=ef(e.backdropPadding);return V(e.font&&e.font.size,t3.font.size)+t.height}return 0}function rC(t,e,i,s,r){return t===s||t===r?{start:e-i/2,end:e+i/2}:t<s||t>r?{start:e-i,end:e}:{start:e,end:e+i}}function rD(t,e,i,s){let{ctx:r}=t;if(i)r.arc(t.xCenter,t.yCenter,e,0,tn);else{let i=t.getPointPosition(0,e);r.moveTo(i.x,i.y);for(let n=1;n<s;n++)i=t.getPointPosition(n,e),r.lineTo(i.x,i.y)}}class rL extends rk{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:tZ.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:t=>t,padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let t=this._padding=ef(rT(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){let{min:t,max:e}=this.getMinMax(!1);this.min=N(t)&&!isNaN(t)?t:0,this.max=N(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/rT(this.options))}generateTickLabels(t){rk.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{let i=H(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){let t=this.options;t.display&&t.pointLabels.display?function(t){let e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),s=[],r=[],n=t._pointLabels.length,a=t.options.pointLabels,o=a.centerPointLabels?tr/n:0;for(let c=0;c<n;c++){var l,h;let n=a.setContext(t.getPointLabelContext(c));r[c]=n.padding;let d=t.getPointPosition(c,t.drawingArea+r[c],o),u=ep(n.font),f=(l=t.ctx,h=F(h=t._pointLabels[c])?h:[h],{w:function(t,e,i,s){let r,n,a,o,l,h=(s=s||{}).data=s.data||{},c=s.garbageCollect=s.garbageCollect||[];s.font!==e&&(h=s.data={},c=s.garbageCollect=[],s.font=e),t.save(),t.font=e;let d=0,u=i.length;for(r=0;r<u;r++)if(null==(o=i[r])||F(o)){if(F(o))for(n=0,a=o.length;n<a;n++)null==(l=o[n])||F(l)||(d=t8(t,h,c,d,l))}else d=t8(t,h,c,d,o);t.restore();let f=c.length/2;if(f>i.length){for(r=0;r<f;r++)delete h[c[r]];c.splice(0,f)}return d}(l,u.string,h),h:h.length*u.lineHeight});s[c]=f;let p=tw(t.getIndexAngle(c)+o),g=Math.round(180/tr*p);!function(t,e,i,s,r){let n=Math.abs(Math.sin(i)),a=Math.abs(Math.cos(i)),o=0,l=0;s.start<e.l?(o=(e.l-s.start)/n,t.l=Math.min(t.l,e.l-o)):s.end>e.r&&(o=(s.end-e.r)/n,t.r=Math.max(t.r,e.r+o)),r.start<e.t?(l=(e.t-r.start)/a,t.t=Math.min(t.t,e.t-l)):r.end>e.b&&(l=(r.end-e.b)/a,t.b=Math.max(t.b,e.b+l))}(i,e,p,rC(g,d.x,f.w,0,180),rC(g,d.y,f.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){let s,r=[],n=t._pointLabels.length,a=t.options,{centerPointLabels:o,display:l}=a.pointLabels,h={extra:rT(a)/2,additionalAngle:o?tr/n:0};for(let a=0;a<n;a++){h.padding=i[a],h.size=e[a];let n=function(t,e,i){var s,r,n,a,o,l,h;let c=t.drawingArea,{extra:d,additionalAngle:u,padding:f,size:p}=i,g=t.getPointPosition(e,c+d+f,u),m=Math.round(180/tr*tw(g.angle+th)),b=(s=g.y,r=p.h,90===(n=m)||270===n?s-=r/2:(n>270||n<90)&&(s-=r),s),x=0===(a=m)||180===a?"center":a<180?"left":"right",_=(o=g.x,l=p.w,"right"===(h=x)?o-=l:"center"===h&&(o-=l/2),o);return{visible:!0,x:g.x,y:b,textAlign:x,left:_,top:b,right:_+p.w,bottom:b+p.h}}(t,a,h);r.push(n),"auto"===l&&(n.visible=function(t,e){if(!e)return!0;let{left:i,top:s,right:r,bottom:n}=t;return!(et({x:i,y:s},e)||et({x:i,y:n},e)||et({x:r,y:s},e)||et({x:r,y:n},e))}(n,s),n.visible&&(s=n))}return r}(t,s,r)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,s){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,s))}getIndexAngle(t){return tw(t*(tn/(this._pointLabels.length||1))+tx(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(I(t))return NaN;let e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(I(t))return NaN;let e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){let e=this._pointLabels||[];if(t>=0&&t<e.length){var i;let s=e[t];return i=this.getContext(),em(i,{label:s,index:t,type:"pointLabel"})}}getPointPosition(t,e,i=0){let s=this.getIndexAngle(t)-th+i;return{x:Math.cos(s)*e+this.xCenter,y:Math.sin(s)*e+this.yCenter,angle:s}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){let{left:e,top:i,right:s,bottom:r}=this._pointLabelItems[t];return{left:e,top:i,right:s,bottom:r}}drawBackground(){let{backgroundColor:t,grid:{circular:e}}=this.options;if(t){let i=this.ctx;i.save(),i.beginPath(),rD(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){let t,e,i,s=this.ctx,r=this.options,{angleLines:n,grid:a,border:o}=r,l=this._pointLabels.length;if(r.pointLabels.display&&function(t,e){let{ctx:i,options:{pointLabels:s}}=t;for(let r=e-1;r>=0;r--){let e=t._pointLabelItems[r];if(!e.visible)continue;let n=s.setContext(t.getPointLabelContext(r));!function(t,e,i){let{left:s,top:r,right:n,bottom:a}=i,{backdropColor:o}=e;if(!I(o)){let i=eu(e.borderRadius),l=ef(e.backdropPadding);t.fillStyle=o;let h=s-l.left,c=r-l.top,d=n-s+l.width,u=a-r+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),ea(t,{x:h,y:c,w:d,h:u,radius:i}),t.fill()):t.fillRect(h,c,d,u)}}(i,n,e);let a=ep(n.font),{x:o,y:l,textAlign:h}=e;en(i,t._pointLabels[r],o,l+a.lineHeight/2,a,{color:n.color,textAlign:h,textBaseline:"middle"})}}(this,l),a.display&&this.ticks.forEach((t,i)=>{if(0!==i||0===i&&this.min<0){e=this.getDistanceFromCenterForValue(t.value);let s=this.getContext(i),r=a.setContext(s),n=o.setContext(s);!function(t,e,i,s,r){let n=t.ctx,a=e.circular,{color:o,lineWidth:l}=e;(a||s)&&o&&l&&!(i<0)&&(n.save(),n.strokeStyle=o,n.lineWidth=l,n.setLineDash(r.dash||[]),n.lineDashOffset=r.dashOffset,n.beginPath(),rD(t,i,a,s),n.closePath(),n.stroke(),n.restore())}(this,r,e,l,n)}}),n.display){for(s.save(),t=l-1;t>=0;t--){let a=n.setContext(this.getPointLabelContext(t)),{color:o,lineWidth:l}=a;l&&o&&(s.lineWidth=l,s.strokeStyle=o,s.setLineDash(a.borderDash),s.lineDashOffset=a.borderDashOffset,e=this.getDistanceFromCenterForValue(r.reverse?this.min:this.max),i=this.getPointPosition(t,e),s.beginPath(),s.moveTo(this.xCenter,this.yCenter),s.lineTo(i.x,i.y),s.stroke())}s.restore()}}drawBorder(){}drawLabels(){let t,e,i=this.ctx,s=this.options,r=s.ticks;if(!r.display)return;let n=this.getIndexAngle(0);i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((n,a)=>{if(0===a&&this.min>=0&&!s.reverse)return;let o=r.setContext(this.getContext(a)),l=ep(o.font);if(t=this.getDistanceFromCenterForValue(this.ticks[a].value),o.showLabelBackdrop){i.font=l.string,e=i.measureText(n.label).width,i.fillStyle=o.backdropColor;let s=ef(o.backdropPadding);i.fillRect(-e/2-s.left,-t-l.size/2-s.top,e+s.width,l.size+s.height)}en(i,n.label,0,-t,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),i.restore()}drawTitle(){}}let rI={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},rF=Object.keys(rI);function rz(t,e){return t-e}function rN(t,e){if(I(e))return null;let i=t._adapter,{parser:s,round:r,isoWeekday:n}=t._parseOpts,a=e;return("function"==typeof s&&(a=s(a)),N(a)||(a="string"==typeof s?i.parse(a,s):i.parse(a)),null===a)?null:(r&&(a="week"===r&&(tm(n)||!0===n)?i.startOf(a,"isoWeek",n):i.startOf(a,r)),+a)}function rj(t,e,i,s){let r=rF.length;for(let n=rF.indexOf(t);n<r-1;++n){let t=rI[rF[n]],r=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(r*t.size))<=s)return rF[n]}return rF[r-1]}function rV(t,e,i){if(i){if(i.length){let{lo:s,hi:r}=tS(i,e);t[i[s]>=e?i[s]:i[r]]=!0}}else t[e]=!0}function rB(t,e,i){let s,r,n=[],a={},o=e.length;for(s=0;s<o;++s)a[r=e[s]]=s,n.push({value:r,major:!1});return 0!==o&&i?function(t,e,i,s){let r,n,a=t._adapter,o=+a.startOf(e[0].value,s),l=e[e.length-1].value;for(r=o;r<=l;r=+a.add(r,1,s))(n=i[r])>=0&&(e[n].major=!0);return e}(t,n,a,i):n}class rW extends sa{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){let i=t.time||(t.time={}),s=this._adapter=new iA._date(t.adapters.date);s.init(e),G(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:rN(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,e=this._adapter,i=t.time.unit||"day",{min:s,max:r,minDefined:n,maxDefined:a}=this.getUserBounds();function o(t){n||isNaN(t.min)||(s=Math.min(s,t.min)),a||isNaN(t.max)||(r=Math.max(r,t.max))}n&&a||(o(this._getLabelBounds()),("ticks"!==t.bounds||"labels"!==t.ticks.source)&&o(this.getMinMax(!1))),s=N(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),r=N(r)&&!isNaN(r)?r:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,r-1),this.max=Math.max(s+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){let t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);let r=this.min,n=function(t,e,i){let s=0,r=t.length;for(;s<r&&t[s]<e;)s++;for(;r>s&&t[r-1]>i;)r--;return s>0||r<t.length?t.slice(s,r):t}(s,r,this.max);return this._unit=e.unit||(i.autoSkip?rj(e.minUnit,this.min,this.max,this._getLabelCapacity(r)):function(t,e,i,s,r){for(let n=rF.length-1;n>=rF.indexOf(i);n--){let i=rF[n];if(rI[i].common&&t._adapter.diff(r,s,i)>=e-1)return i}return rF[i?rF.indexOf(i):0]}(this,n.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=rF.indexOf(t)+1,i=rF.length;e<i;++e)if(rI[rF[e]].common)return rF[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&n.reverse(),rB(this,n,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,s=0,r=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),r=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);let n=t.length<3?.5:.25;s=tP(s,0,n),r=tP(r,0,n),this._offsets={start:s,end:r,factor:1/(s+1+r)}}_generate(){let t,e,i=this._adapter,s=this.min,r=this.max,n=this.options,a=n.time,o=a.unit||rj(a.minUnit,s,r,this._getLabelCapacity(s)),l=V(n.ticks.stepSize,1),h="week"===o&&a.isoWeekday,c=tm(h)||!0===h,d={},u=s;if(c&&(u=+i.startOf(u,"isoWeek",h)),u=+i.startOf(u,c?"day":o),i.diff(r,s,o)>1e5*l)throw Error(s+" and "+r+" are too far apart with stepSize of "+l+" "+o);let f="data"===n.ticks.source&&this.getDataTimestamps();for(t=u,e=0;t<r;t=+i.add(t,l,o),e++)rV(d,t,f);return(t===r||"ticks"===n.bounds||1===e)&&rV(d,t,f),Object.keys(d).sort(rz).map(t=>+t)}getLabelForValue(t){let e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){let i=this.options.time.displayFormats,s=this._unit,r=e||i[s];return this._adapter.format(t,r)}_tickFormatFunction(t,e,i,s){let r=this.options,n=r.ticks.callback;if(n)return H(n,[t,e,i],this);let a=r.time.displayFormats,o=this._unit,l=this._majorUnit,h=o&&a[o],c=l&&a[l],d=i[e],u=l&&c&&d&&d.major;return this._adapter.format(t,s||(u?c:h))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)(s=t[e]).label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){let e=this.options.ticks,i=this.ctx.measureText(t).width,s=tx(this.isHorizontal()?e.maxRotation:e.minRotation),r=Math.cos(s),n=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*r+a*n,h:i*n+a*r}}_getLabelCapacity(t){let e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,r=this._tickFormatFunction(t,0,rB(this,[t],this._majorUnit),s),n=this._getLabelSize(r),a=Math.floor(this.isHorizontal()?this.width/n.w:this.height/n.h)-1;return a>0?a:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;let s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){let t,e,i=this._cache.labels||[];if(i.length)return i;let s=this.getLabels();for(t=0,e=s.length;t<e;++t)i.push(rN(this,s[t]));return this._cache.labels=this._normalized?i:this.normalize(i)}normalize(t){return tC(t.sort(rz))}}function rH(t,e,i){let s,r,n,a,o=0,l=t.length-1;i?(e>=t[o].pos&&e<=t[l].pos&&({lo:o,hi:l}=tO(t,"pos",e)),{pos:s,time:n}=t[o],{pos:r,time:a}=t[l]):(e>=t[o].time&&e<=t[l].time&&({lo:o,hi:l}=tO(t,"time",e)),{time:s,pos:n}=t[o],{time:r,pos:a}=t[l]);let h=r-s;return h?n+(a-n)*(e-s)/h:n}class r$ extends rW{static id="timeseries";static defaults=rW.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=rH(e,this.min),this._tableRange=rH(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){let e,i,s,{min:r,max:n}=this,a=[],o=[];for(e=0,i=t.length;e<i;++e)(s=t[e])>=r&&s<=n&&a.push(s);if(a.length<2)return[{time:r,pos:0},{time:n,pos:1}];for(e=0,i=a.length;e<i;++e)Math.round((a[e+1]+a[e-1])/2)!==(s=a[e])&&o.push({time:s,pos:e/(i-1)});return o}_generate(){let t=this.min,e=this.max,i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;let e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t}getDecimalForValue(t){return(rH(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){let e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return rH(this._table,i*this._tableRange+this._minPos,!0)}}},3736:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseRelativeUrl",{enumerable:!0,get:function(){return r}}),i(4827);let s=i(2785);function r(t,e,i){void 0===i&&(i=!0);let r=new URL("http://n"),n=e?new URL(e,r):t.startsWith(".")?new URL("http://n"):r,{pathname:a,searchParams:o,search:l,hash:h,href:c,origin:d}=new URL(t,n);if(d!==r.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+t),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,s.searchParamsToUrlQuery)(o):void 0,search:l,hash:h,href:c.slice(d.length)}}},4722:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{normalizeAppPath:function(){return n},normalizeRscURL:function(){return a}});let s=i(5531),r=i(5499);function n(t){return(0,s.ensureLeadingSlash)(t.split("/").reduce((t,e,i,s)=>!e||(0,r.isGroupSegment)(e)||"@"===e[0]||("page"===e||"route"===e)&&i===s.length-1?t:t+"/"+e,""))}function a(t){return t.replace(/\.rsc($|\?)/,"$1")}},4827:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return b},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return u},ST:function(){return f},WEB_VITALS:function(){return i},execOnce:function(){return s},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return n},isResSent:function(){return h},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return _}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function s(t){let e,i=!1;return function(){for(var s=arguments.length,r=Array(s),n=0;n<s;n++)r[n]=arguments[n];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,n=t=>r.test(t);function a(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function o(){let{href:t}=window.location,e=a();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function h(t){return t.finished||t.headersSent}function c(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let s=await t.getInitialProps(e);if(i&&h(i))return s;if(!s)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+s+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s}let u="undefined"!=typeof performance,f=u&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class p extends Error{}class g extends Error{}class m extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class b extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function _(t){return JSON.stringify({message:t.message,stack:t.stack})}},5362:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{function t(t,e){void 0===e&&(e={});for(var i=function(t){for(var e=[],i=0;i<t.length;){var s=t[i];if("*"===s||"+"===s||"?"===s){e.push({type:"MODIFIER",index:i,value:t[i++]});continue}if("\\"===s){e.push({type:"ESCAPED_CHAR",index:i++,value:t[i++]});continue}if("{"===s){e.push({type:"OPEN",index:i,value:t[i++]});continue}if("}"===s){e.push({type:"CLOSE",index:i,value:t[i++]});continue}if(":"===s){for(var r="",n=i+1;n<t.length;){var a=t.charCodeAt(n);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){r+=t[n++];continue}break}if(!r)throw TypeError("Missing parameter name at "+i);e.push({type:"NAME",index:i,value:r}),i=n;continue}if("("===s){var o=1,l="",n=i+1;if("?"===t[n])throw TypeError('Pattern cannot start with "?" at '+n);for(;n<t.length;){if("\\"===t[n]){l+=t[n++]+t[n++];continue}if(")"===t[n]){if(0==--o){n++;break}}else if("("===t[n]&&(o++,"?"!==t[n+1]))throw TypeError("Capturing groups are not allowed at "+n);l+=t[n++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);e.push({type:"PATTERN",index:i,value:l}),i=n;continue}e.push({type:"CHAR",index:i,value:t[i++]})}return e.push({type:"END",index:i,value:""}),e}(t),s=e.prefixes,n=void 0===s?"./":s,a="[^"+r(e.delimiter||"/#?")+"]+?",o=[],l=0,h=0,c="",d=function(t){if(h<i.length&&i[h].type===t)return i[h++].value},u=function(t){var e=d(t);if(void 0!==e)return e;var s=i[h];throw TypeError("Unexpected "+s.type+" at "+s.index+", expected "+t)},f=function(){for(var t,e="";t=d("CHAR")||d("ESCAPED_CHAR");)e+=t;return e};h<i.length;){var p=d("CHAR"),g=d("NAME"),m=d("PATTERN");if(g||m){var b=p||"";-1===n.indexOf(b)&&(c+=b,b=""),c&&(o.push(c),c=""),o.push({name:g||l++,prefix:b,suffix:"",pattern:m||a,modifier:d("MODIFIER")||""});continue}var x=p||d("ESCAPED_CHAR");if(x){c+=x;continue}if(c&&(o.push(c),c=""),d("OPEN")){var b=f(),_=d("NAME")||"",y=d("PATTERN")||"",v=f();u("CLOSE"),o.push({name:_||(y?l++:""),pattern:_&&!y?a:y,prefix:b,suffix:v,modifier:d("MODIFIER")||""});continue}u("END")}return o}function i(t,e){void 0===e&&(e={});var i=n(e),s=e.encode,r=void 0===s?function(t){return t}:s,a=e.validate,o=void 0===a||a,l=t.map(function(t){if("object"==typeof t)return RegExp("^(?:"+t.pattern+")$",i)});return function(e){for(var i="",s=0;s<t.length;s++){var n=t[s];if("string"==typeof n){i+=n;continue}var a=e?e[n.name]:void 0,h="?"===n.modifier||"*"===n.modifier,c="*"===n.modifier||"+"===n.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+n.name+'" to not repeat, but got an array');if(0===a.length){if(h)continue;throw TypeError('Expected "'+n.name+'" to not be empty')}for(var d=0;d<a.length;d++){var u=r(a[d],n);if(o&&!l[s].test(u))throw TypeError('Expected all "'+n.name+'" to match "'+n.pattern+'", but got "'+u+'"');i+=n.prefix+u+n.suffix}continue}if("string"==typeof a||"number"==typeof a){var u=r(String(a),n);if(o&&!l[s].test(u))throw TypeError('Expected "'+n.name+'" to match "'+n.pattern+'", but got "'+u+'"');i+=n.prefix+u+n.suffix;continue}if(!h){var f=c?"an array":"a string";throw TypeError('Expected "'+n.name+'" to be '+f)}}return i}}function s(t,e,i){void 0===i&&(i={});var s=i.decode,r=void 0===s?function(t){return t}:s;return function(i){var s=t.exec(i);if(!s)return!1;for(var n=s[0],a=s.index,o=Object.create(null),l=1;l<s.length;l++)!function(t){if(void 0!==s[t]){var i=e[t-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=s[t].split(i.prefix+i.suffix).map(function(t){return r(t,i)}):o[i.name]=r(s[t],i)}}(l);return{path:n,index:a,params:o}}}function r(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function n(t){return t&&t.sensitive?"":"i"}function a(t,e,i){void 0===i&&(i={});for(var s=i.strict,a=void 0!==s&&s,o=i.start,l=i.end,h=i.encode,c=void 0===h?function(t){return t}:h,d="["+r(i.endsWith||"")+"]|$",u="["+r(i.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",p=0;p<t.length;p++){var g=t[p];if("string"==typeof g)f+=r(c(g));else{var m=r(c(g.prefix)),b=r(c(g.suffix));if(g.pattern)if(e&&e.push(g),m||b)if("+"===g.modifier||"*"===g.modifier){var x="*"===g.modifier?"?":"";f+="(?:"+m+"((?:"+g.pattern+")(?:"+b+m+"(?:"+g.pattern+"))*)"+b+")"+x}else f+="(?:"+m+"("+g.pattern+")"+b+")"+g.modifier;else f+="("+g.pattern+")"+g.modifier;else f+="(?:"+m+b+")"+g.modifier}}if(void 0===l||l)a||(f+=u+"?"),f+=i.endsWith?"(?="+d+")":"$";else{var _=t[t.length-1],y="string"==typeof _?u.indexOf(_[_.length-1])>-1:void 0===_;a||(f+="(?:"+u+"(?="+d+"))?"),y||(f+="(?="+u+"|"+d+")")}return new RegExp(f,n(i))}function o(e,i,s){if(e instanceof RegExp){if(!i)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var l=0;l<r.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return e}return Array.isArray(e)?RegExp("(?:"+e.map(function(t){return o(t,i,s).source}).join("|")+")",n(s)):a(t(e,s),i,s)}Object.defineProperty(e,"__esModule",{value:!0}),e.parse=t,e.compile=function(e,s){return i(t(e,s),s)},e.tokensToFunction=i,e.match=function(t,e){var i=[];return s(o(t,i,e),i,e)},e.regexpToFunction=s,e.tokensToRegexp=a,e.pathToRegexp=o})(),t.exports=e})()},5526:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{compileNonPath:function(){return c},matchHas:function(){return h},parseDestination:function(){return d},prepareDestination:function(){return u}});let s=i(5362),r=i(3293),n=i(6759),a=i(1437),o=i(8212);function l(t){return t.replace(/__ESC_COLON_/gi,":")}function h(t,e,i,s){void 0===i&&(i=[]),void 0===s&&(s=[]);let r={},n=i=>{let s,n=i.key;switch(i.type){case"header":n=n.toLowerCase(),s=t.headers[n];break;case"cookie":s="cookies"in t?t.cookies[i.key]:(0,o.getCookieParser)(t.headers)()[i.key];break;case"query":s=e[n];break;case"host":{let{host:e}=(null==t?void 0:t.headers)||{};s=null==e?void 0:e.split(":",1)[0].toLowerCase()}}if(!i.value&&s)return r[function(t){let e="";for(let i=0;i<t.length;i++){let s=t.charCodeAt(i);(s>64&&s<91||s>96&&s<123)&&(e+=t[i])}return e}(n)]=s,!0;if(s){let t=RegExp("^"+i.value+"$"),e=Array.isArray(s)?s.slice(-1)[0].match(t):s.match(t);if(e)return Array.isArray(e)&&(e.groups?Object.keys(e.groups).forEach(t=>{r[t]=e.groups[t]}):"host"===i.type&&e[0]&&(r.host=e[0])),!0}return!1};return!(!i.every(t=>n(t))||s.some(t=>n(t)))&&r}function c(t,e){if(!t.includes(":"))return t;for(let i of Object.keys(e))t.includes(":"+i)&&(t=t.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return t=t.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,s.compile)("/"+t,{validate:!1})(e).slice(1)}function d(t){let e=t.destination;for(let i of Object.keys({...t.params,...t.query}))i&&(e=e.replace(RegExp(":"+(0,r.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,n.parseUrl)(e),s=i.pathname;s&&(s=l(s));let a=i.href;a&&(a=l(a));let o=i.hostname;o&&(o=l(o));let h=i.hash;return h&&(h=l(h)),{...i,pathname:s,hostname:o,href:a,hash:h}}function u(t){let e,i,r=Object.assign({},t.query),n=d(t),{hostname:o,query:h}=n,u=n.pathname;n.hash&&(u=""+u+n.hash);let f=[],p=[];for(let t of((0,s.pathToRegexp)(u,p),p))f.push(t.name);if(o){let t=[];for(let e of((0,s.pathToRegexp)(o,t),t))f.push(e.name)}let g=(0,s.compile)(u,{validate:!1});for(let[i,r]of(o&&(e=(0,s.compile)(o,{validate:!1})),Object.entries(h)))Array.isArray(r)?h[i]=r.map(e=>c(l(e),t.params)):"string"==typeof r&&(h[i]=c(l(r),t.params));let m=Object.keys(t.params).filter(t=>"nextInternalLocale"!==t);if(t.appendParamsToQuery&&!m.some(t=>f.includes(t)))for(let e of m)e in h||(h[e]=t.params[e]);if((0,a.isInterceptionRouteAppPath)(u))for(let e of u.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));if(i){"(..)(..)"===i?(t.params["0"]="(..)",t.params["1"]="(..)"):t.params["0"]=i;break}}try{let[s,r]=(i=g(t.params)).split("#",2);e&&(n.hostname=e(t.params)),n.pathname=s,n.hash=(r?"#":"")+(r||""),delete n.search}catch(t){if(t.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw t}return n.query={...r,...n.query},{newUrl:i,destQuery:h,parsedDestination:n}}},5531:(t,e)=>{"use strict";function i(t){return t.startsWith("/")?t:"/"+t}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},6341:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getPreviouslyRevalidatedTags:function(){return b},getUtils:function(){return m},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return f}});let s=i(9551),r=i(1959),n=i(2437),a=i(7253),o=i(8034),l=i(5526),h=i(2887),c=i(4722),d=i(6143),u=i(7912);function f(t,e,i){let r=(0,s.parse)(t.url,!0);for(let t of(delete r.search,Object.keys(r.query))){let s=t!==d.NEXT_QUERY_PARAM_PREFIX&&t.startsWith(d.NEXT_QUERY_PARAM_PREFIX),n=t!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&t.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(s||n||e.includes(t)||i&&Object.keys(i.groups).includes(t))&&delete r.query[t]}t.url=(0,s.format)(r)}function p(t,e,i){if(!i)return t;for(let s of Object.keys(i.groups)){let r,{optional:n,repeat:a}=i.groups[s],o=`[${a?"...":""}${s}]`;n&&(o=`[${o}]`);let l=e[s];r=Array.isArray(l)?l.map(t=>t&&encodeURIComponent(t)).join("/"):l?encodeURIComponent(l):"",t=t.replaceAll(o,r)}return t}function g(t,e,i,s){let r={};for(let n of Object.keys(e.groups)){let a=t[n];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let o=i[n],l=e.groups[n].optional;if((Array.isArray(o)?o.some(t=>Array.isArray(a)?a.some(e=>e.includes(t)):null==a?void 0:a.includes(t)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&s))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${n}]]`))&&(a=void 0,delete t[n]),a&&"string"==typeof a&&e.groups[n].repeat&&(a=a.split("/")),a&&(r[n]=a)}return{params:r,hasValidParams:!0}}function m({page:t,i18n:e,basePath:i,rewrites:s,pageIsDynamic:c,trailingSlash:d,caseSensitive:m}){let b,x,_;return c&&(b=(0,a.getNamedRouteRegex)(t,{prefixRouteKeys:!1}),_=(x=(0,o.getRouteMatcher)(b))(t)),{handleRewrites:function(a,o){let u={},f=o.pathname,p=s=>{let h=(0,n.getPathMatch)(s.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!m});if(!o.pathname)return!1;let p=h(o.pathname);if((s.has||s.missing)&&p){let t=(0,l.matchHas)(a,o.query,s.has,s.missing);t?Object.assign(p,t):p=!1}if(p){let{parsedDestination:n,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:s.destination,params:p,query:o.query});if(n.protocol)return!0;if(Object.assign(u,a,p),Object.assign(o.query,n.query),delete n.query,Object.assign(o,n),!(f=o.pathname))return!1;if(i&&(f=f.replace(RegExp(`^${i}`),"")||"/"),e){let t=(0,r.normalizeLocalePath)(f,e.locales);f=t.pathname,o.query.nextInternalLocale=t.detectedLocale||p.nextInternalLocale}if(f===t)return!0;if(c&&x){let t=x(f);if(t)return o.query={...o.query,...t},!0}}return!1};for(let t of s.beforeFiles||[])p(t);if(f!==t){let e=!1;for(let t of s.afterFiles||[])if(e=p(t))break;if(!e&&!(()=>{let e=(0,h.removeTrailingSlash)(f||"");return e===(0,h.removeTrailingSlash)(t)||(null==x?void 0:x(e))})()){for(let t of s.fallback||[])if(e=p(t))break}}return u},defaultRouteRegex:b,dynamicRouteMatcher:x,defaultRouteMatches:_,getParamsFromRouteMatches:function(t){if(!b)return null;let{groups:e,routeKeys:i}=b,s=(0,o.getRouteMatcher)({re:{exec:t=>{let s=Object.fromEntries(new URLSearchParams(t));for(let[t,e]of Object.entries(s)){let i=(0,u.normalizeNextQueryParam)(t);i&&(s[i]=e,delete s[t])}let r={};for(let t of Object.keys(i)){let n=i[t];if(!n)continue;let a=e[n],o=s[t];if(!a.optional&&!o)return null;r[a.pos]=o}return r}},groups:e})(t);return s||null},normalizeDynamicRouteParams:(t,e)=>b&&_?g(t,b,_,e):{params:{},hasValidParams:!1},normalizeVercelUrl:(t,e)=>f(t,e,b),interpolateDynamicPath:(t,e)=>p(t,e,b)}}function b(t,e){return"string"==typeof t[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&t[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===e?t[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{e.parse=function(e,i){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},n=e.split(s),a=(i||{}).decode||t,o=0;o<n.length;o++){var l=n[o],h=l.indexOf("=");if(!(h<0)){var c=l.substr(0,h).trim(),d=l.substr(++h,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==r[c]&&(r[c]=function(t,e){try{return e(t)}catch(e){return t}}(d,a))}}return r},e.serialize=function(t,e,s){var n=s||{},a=n.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(t))throw TypeError("argument name is invalid");var o=a(e);if(o&&!r.test(o))throw TypeError("argument val is invalid");var l=t+"="+o;if(null!=n.maxAge){var h=n.maxAge-0;if(isNaN(h)||!isFinite(h))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(h)}if(n.domain){if(!r.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!r.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=decodeURIComponent,i=encodeURIComponent,s=/; */,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},6759:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseUrl",{enumerable:!0,get:function(){return n}});let s=i(2785),r=i(3736);function n(t){if(t.startsWith("/"))return(0,r.parseRelativeUrl)(t);let e=new URL(t);return{hash:e.hash,hostname:e.hostname,href:e.href,pathname:e.pathname,port:e.port,protocol:e.protocol,query:(0,s.searchParamsToUrlQuery)(e.searchParams),search:e.search}}},7253:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let s=i(6143),r=i(1437),n=i(3293),a=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(t){let e=t.match(o);return e?h(e[2]):h(t)}function h(t){let e=t.startsWith("[")&&t.endsWith("]");e&&(t=t.slice(1,-1));let i=t.startsWith("...");return i&&(t=t.slice(3)),{key:t,repeat:i,optional:e}}function c(t,e,i){let s={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.find(t=>d.startsWith(t)),a=d.match(o);if(t&&a&&a[2]){let{key:e,optional:i,repeat:r}=h(a[2]);s[e]={pos:l++,repeat:r,optional:i},c.push("/"+(0,n.escapeStringRegexp)(t)+"([^/]+?)")}else if(a&&a[2]){let{key:t,repeat:e,optional:r}=h(a[2]);s[t]={pos:l++,repeat:e,optional:r},i&&a[1]&&c.push("/"+(0,n.escapeStringRegexp)(a[1]));let o=e?r?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,n.escapeStringRegexp)(d));e&&a&&a[3]&&c.push((0,n.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:s}}function d(t,e){let{includeSuffix:i=!1,includePrefix:s=!1,excludeOptionalTrailingSlash:r=!1}=void 0===e?{}:e,{parameterizedRoute:n,groups:a}=c(t,i,s),o=n;return r||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function u(t){let e,{interceptionMarker:i,getSafeRouteKey:s,segment:r,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=t,{key:c,optional:d,repeat:u}=h(r),f=c.replace(/\W/g,"");o&&(f=""+o+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=s());let g=f in a;o?a[f]=""+o+c:a[f]=c;let m=i?(0,n.escapeStringRegexp)(i):"";return e=g&&l?"\\k<"+f+">":u?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+m+e+")?":"/"+m+e}function f(t,e,i,l,h){let c,d=(c=0,()=>{let t="",e=++c;for(;e>0;)t+=String.fromCharCode(97+(e-1)%26),e=Math.floor((e-1)/26);return t}),f={},p=[];for(let c of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=r.INTERCEPTION_ROUTE_MARKERS.some(t=>c.startsWith(t)),a=c.match(o);if(t&&a&&a[2])p.push(u({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:f,keyPrefix:e?s.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:h}));else if(a&&a[2]){l&&a[1]&&p.push("/"+(0,n.escapeStringRegexp)(a[1]));let t=u({getSafeRouteKey:d,segment:a[2],routeKeys:f,keyPrefix:e?s.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:h});l&&a[1]&&(t=t.substring(1)),p.push(t)}else p.push("/"+(0,n.escapeStringRegexp)(c));i&&a&&a[3]&&p.push((0,n.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(t,e){var i,s,r;let n=f(t,e.prefixRouteKeys,null!=(i=e.includeSuffix)&&i,null!=(s=e.includePrefix)&&s,null!=(r=e.backreferenceDuplicateKeys)&&r),a=n.namedParameterizedRoute;return e.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(t,e),namedRegex:"^"+a+"$",routeKeys:n.routeKeys}}function g(t,e){let{parameterizedRoute:i}=c(t,!1,!1),{catchAll:s=!0}=e;if("/"===i)return{namedRegex:"^/"+(s?".*":"")+"$"};let{namedParameterizedRoute:r}=f(t,!1,!1,!1,!1);return{namedRegex:"^"+r+(s?"(?:(/.*)?)":"")+"$"}}},8034:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getRouteMatcher",{enumerable:!0,get:function(){return r}});let s=i(4827);function r(t){let{re:e,groups:i}=t;return t=>{let r=e.exec(t);if(!r)return!1;let n=t=>{try{return decodeURIComponent(t)}catch(t){throw Object.defineProperty(new s.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[t,e]of Object.entries(i)){let i=r[e.pos];void 0!==i&&(e.repeat?a[t]=i.split("/").map(t=>n(t)):a[t]=n(i))}return a}}},8212:(t,e,i)=>{"use strict";function s(t){return function(){let{cookie:e}=t;if(!e)return{};let{parse:s}=i(6415);return s(Array.isArray(e)?e.join("; "):e)}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getCookieParser",{enumerable:!0,get:function(){return s}})},8304:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return u},isMetadataRouteFile:function(){return h},isStaticMetadataRoute:function(){return c}});let s=i(2958),r=i(4722),n=i(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(t,e)=>e&&0!==e.length?`(?:\\.(${t.join("|")})|(\\.(${e.join("|")})))`:`(\\.(?:${t.join("|")}))`;function h(t,e,i){let r=(i?"":"?")+"$",n=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(e.concat("txt"),null)}${r}`),RegExp(`^[\\\\/]manifest${l(e.concat("webmanifest","json"),null)}${r}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],e)}${r}`),RegExp(`[\\\\/]${a.icon.filename}${n}${l(a.icon.extensions,e)}${r}`),RegExp(`[\\\\/]${a.apple.filename}${n}${l(a.apple.extensions,e)}${r}`),RegExp(`[\\\\/]${a.openGraph.filename}${n}${l(a.openGraph.extensions,e)}${r}`),RegExp(`[\\\\/]${a.twitter.filename}${n}${l(a.twitter.extensions,e)}${r}`)],h=(0,s.normalizePathSep)(t);return o.some(t=>t.test(h))}function c(t){let e=t.replace(/\/route$/,"");return(0,n.isAppRouteRoute)(t)&&h(e,[],!0)&&"/robots.txt"!==e&&"/manifest.webmanifest"!==e&&!e.endsWith("/sitemap.xml")}function d(t){return!(0,n.isAppRouteRoute)(t)&&h(t,[],!1)}function u(t){let e=(0,r.normalizeAppPath)(t).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==e[0]&&(e="/"+e),(0,n.isAppRouteRoute)(t)&&h(e,[],!1)}},9947:(t,e,i)=>{"use strict";i.d(e,{N1:()=>c,nu:()=>u,yP:()=>d});var s=i(3210),r=i(3324);let n="label";function a(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function o(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n,s=[];t.datasets=e.map(e=>{let r=t.datasets.find(t=>t[i]===e[i]);return!r||!e.data||s.includes(r)?{...e}:(s.push(r),Object.assign(r,e),r)})}let l=(0,s.forwardRef)(function(t,e){let{height:i=150,width:l=300,redraw:h=!1,datasetIdKey:c,type:d,data:u,options:f,plugins:p=[],fallbackContent:g,updateMode:m,...b}=t,x=(0,s.useRef)(null),_=(0,s.useRef)(null),y=()=>{x.current&&(_.current=new r.t1(x.current,{type:d,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,i={labels:[],datasets:[]};return i.labels=t.labels,o(i,t.datasets,e),i}(u,c),options:f&&{...f},plugins:p}),a(e,_.current))},v=()=>{a(e,null),_.current&&(_.current.destroy(),_.current=null)};return(0,s.useEffect)(()=>{!h&&_.current&&f&&function(t,e){let i=t.options;i&&e&&Object.assign(i,e)}(_.current,f)},[h,f]),(0,s.useEffect)(()=>{!h&&_.current&&(_.current.config.data.labels=u.labels)},[h,u.labels]),(0,s.useEffect)(()=>{!h&&_.current&&u.datasets&&o(_.current.config.data,u.datasets,c)},[h,u.datasets]),(0,s.useEffect)(()=>{_.current&&(h?(v(),setTimeout(y)):_.current.update(m))},[h,f,u.labels,u.datasets,m]),(0,s.useEffect)(()=>{_.current&&(v(),setTimeout(y))},[d]),(0,s.useEffect)(()=>(y(),()=>v()),[]),s.createElement("canvas",{ref:x,role:"img",height:i,width:l,...b},g)});function h(t,e){return r.t1.register(e),(0,s.forwardRef)((e,i)=>s.createElement(l,{...e,ref:i,type:t}))}let c=h("line",r.ZT),d=h("bar",r.A6),u=h("doughnut",r.ju)}};