{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { useAnalysis } from '@/context/AnalysisContext';\n\ninterface FileUploadProps {\n  onAnalysisComplete: (analysisId: string) => void;\n}\n\nexport default function FileUpload({ onAnalysisComplete }: FileUploadProps) {\n  const [isDragging, setIsDragging] = useState(false);\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [isUploading, setIsUploading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { setIsLoading } = useAnalysis();\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    \n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      handleFileUpload(files[0]);\n    }\n  };\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      handleFileUpload(files[0]);\n    }\n  };\n\n  const handleFileUpload = async (file: File) => {\n    setIsUploading(true);\n    setIsLoading(true);\n    setError(null);\n    setUploadProgress(0);\n\n    const formData = new FormData();\n    formData.append('file', file);\n\n    try {\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Upload failed');\n      }\n\n      const result = await response.json();\n      setUploadProgress(100);\n      \n      // Simulate processing time for better UX\n      setTimeout(() => {\n        onAnalysisComplete(result.analysis_id);\n        setIsUploading(false);\n        setIsLoading(false);\n      }, 1000);\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Upload failed');\n      setIsUploading(false);\n      setIsLoading(false);\n      setUploadProgress(0);\n    }\n  };\n\n  const openFileDialog = () => {\n    fileInputRef.current?.click();\n  };\n\n  if (isUploading) {\n    return (\n      <div className=\"max-w-2xl mx-auto\">\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Analyzing Log File\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Processing your log file and generating insights...\n            </p>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${uploadProgress}%` }}\n              ></div>\n            </div>\n            <p className=\"text-sm text-gray-500 mt-2\">{uploadProgress}% complete</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Enterprise Log Analysis\n        </h2>\n        <p className=\"text-lg text-gray-600 mb-2\">\n          Upload your log files for intelligent analysis and insights\n        </p>\n        <p className=\"text-sm text-gray-500\">\n          Supports enterprise structured logs, JSON, Apache, Nginx, syslog, and more\n        </p>\n      </div>\n\n      <div\n        className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${\n          isDragging\n            ? 'border-blue-400 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n      >\n        <div className=\"space-y-4\">\n          <div className=\"text-6xl\">📄</div>\n          <div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Drop your log file here\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              or click to browse and select a file\n            </p>\n            <button\n              onClick={openFileDialog}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors\"\n            >\n              Choose File\n            </button>\n          </div>\n        </div>\n\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          onChange={handleFileSelect}\n          className=\"hidden\"\n          accept=\".log,.txt,.json,.csv\"\n        />\n      </div>\n\n      {error && (\n        <div className=\"mt-4 p-4 bg-red-50 border border-red-200 rounded-md\">\n          <div className=\"flex\">\n            <div className=\"text-red-400\">⚠️</div>\n            <div className=\"ml-3\">\n              <h3 className=\"text-sm font-medium text-red-800\">Upload Error</h3>\n              <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <div className=\"text-blue-600 text-2xl mb-3\">🏢</div>\n          <h3 className=\"font-semibold text-gray-900 mb-2\">Enterprise Structured</h3>\n          <p className=\"text-sm text-gray-600\">\n            Advanced parsing for enterprise log formats with bracketed fields and structured data\n          </p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <div className=\"text-green-600 text-2xl mb-3\">📊</div>\n          <h3 className=\"font-semibold text-gray-900 mb-2\">Performance Analytics</h3>\n          <p className=\"text-sm text-gray-600\">\n            Response time analysis, customer insights, and application performance monitoring\n          </p>\n        </div>\n        \n        <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\n          <div className=\"text-purple-600 text-2xl mb-3\">🔍</div>\n          <h3 className=\"font-semibold text-gray-900 mb-2\">Smart Detection</h3>\n          <p className=\"text-sm text-gray-600\">\n            Automatic format detection, anomaly identification, and intelligent pattern recognition\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,WAAW,EAAE,kBAAkB,EAAmB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAEnC,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,eAAe;QACf,aAAa;QACb,SAAS;QACT,kBAAkB;QAElB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,kBAAkB;YAElB,yCAAyC;YACzC,WAAW;gBACT,mBAAmB,OAAO,WAAW;gBACrC,eAAe;gBACf,aAAa;YACf,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,eAAe;YACf,aAAa;YACb,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa,OAAO,EAAE;IACxB;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,eAAe,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGzC,8OAAC;4BAAE,WAAU;;gCAA8B;gCAAe;;;;;;;;;;;;;;;;;;;;;;;IAKpE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC;gBACC,WAAW,CAAC,qEAAqE,EAC/E,aACI,+BACA,yCACJ;gBACF,YAAY;gBACZ,aAAa;gBACb,QAAQ;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAW;;;;;;0CAC1B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,UAAU;wBACV,WAAU;wBACV,QAAO;;;;;;;;;;;;YAIV,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAe;;;;;;sCAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;0CAC9C,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAKvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/ErrorAnalysisChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\ninterface ErrorAnalysisChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function ErrorAnalysisChart({ data, detailed = false }: ErrorAnalysisChartProps) {\n  if (!data || !data.error_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Error Analysis</h3>\n        <p className=\"text-gray-500\">No error data available</p>\n      </div>\n    );\n  }\n\n  const errorAnalysis = data.error_analysis;\n\n  // Error types chart\n  const errorTypes = errorAnalysis.error_types || {};\n  const typeChartData = {\n    labels: Object.keys(errorTypes),\n    datasets: [\n      {\n        data: Object.values(errorTypes),\n        backgroundColor: [\n          '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',\n          '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9'\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const typeChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom' as const,\n      },\n      title: {\n        display: true,\n        text: 'Error Types Distribution',\n      },\n    },\n  };\n\n  // Error trends chart\n  const errorTrends = errorAnalysis.error_trends || [];\n  const trendChartData = {\n    labels: errorTrends.map((trend: any) => \n      new Date(trend.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    ),\n    datasets: [\n      {\n        label: 'Error Count',\n        data: errorTrends.map((trend: any) => trend.error_count),\n        backgroundColor: 'rgba(239, 68, 68, 0.6)',\n        borderColor: 'rgba(239, 68, 68, 1)',\n        borderWidth: 2,\n      },\n    ],\n  };\n\n  const trendChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Error Trends Over Time',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Error Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {errorAnalysis.total_errors} total errors\n        </div>\n      </div>\n\n      {/* Error Summary */}\n      <div className=\"mb-6 p-4 bg-red-50 rounded-lg\">\n        <div className=\"flex items-center\">\n          <div className=\"text-red-400 text-xl mr-3\">🚨</div>\n          <div>\n            <div className=\"text-red-800 font-medium\">\n              {errorAnalysis.total_errors} errors detected\n            </div>\n            <div className=\"text-red-600 text-sm\">\n              {Object.keys(errorTypes).length} different error types identified\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts */}\n      <div className=\"space-y-6\">\n        {/* Error Types Distribution */}\n        {Object.keys(errorTypes).length > 0 && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <div>\n              <Doughnut data={typeChartData} options={typeChartOptions} />\n            </div>\n            \n            <div className=\"space-y-3\">\n              <h4 className=\"text-md font-medium text-gray-900\">Error Breakdown</h4>\n              {Object.entries(errorTypes)\n                .sort(([,a], [,b]) => (b as number) - (a as number))\n                .map(([type, count]) => (\n                  <div key={type} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                    <span className=\"text-sm font-medium capitalize\">{type.replace('_', ' ')}</span>\n                    <span className=\"text-sm text-gray-600\">{count as number} errors</span>\n                  </div>\n                ))}\n            </div>\n          </div>\n        )}\n\n        {/* Error Trends */}\n        {detailed && errorTrends.length > 0 && (\n          <div>\n            <Bar data={trendChartData} options={trendChartOptions} />\n          </div>\n        )}\n\n        {/* Top Error Messages */}\n        {detailed && errorAnalysis.top_error_messages && errorAnalysis.top_error_messages.length > 0 && (\n          <div>\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Most Common Error Messages</h4>\n            <div className=\"space-y-2\">\n              {errorAnalysis.top_error_messages.slice(0, 10).map((error: any, index: number) => (\n                <div key={index} className=\"p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm font-medium text-red-600\">\n                      {error.count} occurrences\n                    </span>\n                    <span className=\"text-xs text-gray-500\">\n                      #{index + 1}\n                    </span>\n                  </div>\n                  <div className=\"text-sm text-gray-700 font-mono bg-white p-2 rounded border\">\n                    {error.message.length > 200 \n                      ? error.message.substring(0, 200) + '...'\n                      : error.message\n                    }\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AAbA;;;;AAeA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,aAAU;AAQG,SAAS,mBAAmB,EAAE,IAAI,EAAE,WAAW,KAAK,EAA2B;IAC5F,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;QACjC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,gBAAgB,KAAK,cAAc;IAEzC,oBAAoB;IACpB,MAAM,aAAa,cAAc,WAAW,IAAI,CAAC;IACjD,MAAM,gBAAgB;QACpB,QAAQ,OAAO,IAAI,CAAC;QACpB,UAAU;YACR;gBACE,MAAM,OAAO,MAAM,CAAC;gBACpB,iBAAiB;oBACf;oBAAW;oBAAW;oBAAW;oBAAW;oBAC5C;oBAAW;oBAAW;oBAAW;oBAAW;iBAC7C;gBACD,aAAa;YACf;SACD;IACH;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,cAAc,YAAY,IAAI,EAAE;IACpD,MAAM,iBAAiB;QACrB,QAAQ,YAAY,GAAG,CAAC,CAAC,QACvB,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAExF,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,YAAY,GAAG,CAAC,CAAC,QAAe,MAAM,WAAW;gBACvD,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,YAAY;4BAAC;;;;;;;;;;;;;0BAKhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA4B;;;;;;sCAC3C,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,YAAY;wCAAC;;;;;;;8CAE9B,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,IAAI,CAAC,YAAY,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAI,WAAU;;oBAEZ,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,mBAChC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CACC,cAAA,8OAAC,sJAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAe,SAAS;;;;;;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;oCACjD,OAAO,OAAO,CAAC,YACb,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,AAAC,IAAgB,GACtC,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBACjB,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;8DAAkC,KAAK,OAAO,CAAC,KAAK;;;;;;8DACpE,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAgB;;;;;;;;2CAFjD;;;;;;;;;;;;;;;;;oBAUnB,YAAY,YAAY,MAAM,GAAG,mBAChC,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAgB,SAAS;;;;;;;;;;;oBAKvC,YAAY,cAAc,kBAAkB,IAAI,cAAc,kBAAkB,CAAC,MAAM,GAAG,mBACzF,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,cAAc,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,OAAY,sBAC9D,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,MAAM,KAAK;4DAAC;;;;;;;kEAEf,8OAAC;wDAAK,WAAU;;4DAAwB;4DACpC,QAAQ;;;;;;;;;;;;;0DAGd,8OAAC;gDAAI,WAAU;0DACZ,MAAM,OAAO,CAAC,MAAM,GAAG,MACpB,MAAM,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,QAClC,MAAM,OAAO;;;;;;;uCAZX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuB1B", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/TimelineChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Bar, Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface TimelineChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function TimelineChart({ data, detailed = false }: TimelineChartProps) {\n  if (!data || !data.time_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Timeline Analysis</h3>\n        <p className=\"text-gray-500\">No timeline data available</p>\n      </div>\n    );\n  }\n\n  const timeAnalysis = data.time_analysis;\n\n  // Hourly distribution chart\n  const hourlyData = timeAnalysis.hourly_distribution || {};\n  const hours = Array.from({ length: 24 }, (_, i) => i);\n  const hourlyCounts = hours.map(hour => hourlyData[hour] || 0);\n\n  const hourlyChartData = {\n    labels: hours.map(hour => `${hour.toString().padStart(2, '0')}:00`),\n    datasets: [\n      {\n        label: 'Log Entries',\n        data: hourlyCounts,\n        backgroundColor: 'rgba(59, 130, 246, 0.6)',\n        borderColor: 'rgba(59, 130, 246, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const hourlyChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Activity by Hour of Day',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  // Daily distribution chart\n  const dailyData = timeAnalysis.daily_distribution || {};\n  const dailyLabels = Object.keys(dailyData).sort();\n  const dailyCounts = dailyLabels.map(date => dailyData[date]);\n\n  const dailyChartData = {\n    labels: dailyLabels.map(date => new Date(date).toLocaleDateString()),\n    datasets: [\n      {\n        label: 'Daily Activity',\n        data: dailyCounts,\n        backgroundColor: 'rgba(16, 185, 129, 0.6)',\n        borderColor: 'rgba(16, 185, 129, 1)',\n        borderWidth: 2,\n        fill: false,\n      },\n    ],\n  };\n\n  const dailyChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Activity Over Time',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  const peakHour = timeAnalysis.peak_hour;\n  const totalDays = timeAnalysis.total_days || 0;\n  const avgEntriesPerHour = timeAnalysis.avg_entries_per_hour || 0;\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Timeline Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {totalDays} days analyzed\n        </div>\n      </div>\n\n      {/* Timeline Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <div className=\"text-blue-600 text-sm font-medium\">Peak Hour</div>\n          <div className=\"text-blue-900 text-lg font-bold\">\n            {peakHour !== null ? `${peakHour.toString().padStart(2, '0')}:00` : 'N/A'}\n          </div>\n          <div className=\"text-blue-600 text-xs\">\n            Highest activity period\n          </div>\n        </div>\n        \n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <div className=\"text-green-600 text-sm font-medium\">Avg/Hour</div>\n          <div className=\"text-green-900 text-lg font-bold\">\n            {Math.round(avgEntriesPerHour).toLocaleString()}\n          </div>\n          <div className=\"text-green-600 text-xs\">\n            Average entries per hour\n          </div>\n        </div>\n        \n        <div className=\"bg-purple-50 rounded-lg p-4\">\n          <div className=\"text-purple-600 text-sm font-medium\">Time Span</div>\n          <div className=\"text-purple-900 text-lg font-bold\">\n            {totalDays} {totalDays === 1 ? 'day' : 'days'}\n          </div>\n          <div className=\"text-purple-600 text-xs\">\n            Analysis period\n          </div>\n        </div>\n      </div>\n\n      {/* Charts */}\n      <div className=\"space-y-6\">\n        {/* Hourly Distribution */}\n        <div>\n          <Bar data={hourlyChartData} options={hourlyChartOptions} />\n        </div>\n\n        {/* Daily Distribution (only if detailed and multiple days) */}\n        {detailed && dailyLabels.length > 1 && (\n          <div>\n            <Line data={dailyChartData} options={dailyChartOptions} />\n          </div>\n        )}\n\n        {/* Activity Insights */}\n        {detailed && (\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Activity Insights</h4>\n            <div className=\"space-y-2 text-sm\">\n              {peakHour !== null && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-blue-600 mr-2\">📈</span>\n                  <span>Peak activity occurs at {peakHour.toString().padStart(2, '0')}:00</span>\n                </div>\n              )}\n              \n              {avgEntriesPerHour > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-600 mr-2\">⚡</span>\n                  <span>Average of {Math.round(avgEntriesPerHour)} entries per hour</span>\n                </div>\n              )}\n              \n              {totalDays > 1 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-purple-600 mr-2\">📅</span>\n                  <span>Data spans {totalDays} days</span>\n                </div>\n              )}\n\n              {/* Activity level assessment */}\n              {avgEntriesPerHour > 1000 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-orange-600 mr-2\">🔥</span>\n                  <span>High-volume system with significant activity</span>\n                </div>\n              )}\n              \n              {avgEntriesPerHour < 10 && avgEntriesPerHour > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-gray-600 mr-2\">🔍</span>\n                  <span>Low-volume system with minimal activity</span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAWA;AAdA;;;;AAgBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAQO,SAAS,cAAc,EAAE,IAAI,EAAE,WAAW,KAAK,EAAsB;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE;QAChC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,eAAe,KAAK,aAAa;IAEvC,4BAA4B;IAC5B,MAAM,aAAa,aAAa,mBAAmB,IAAI,CAAC;IACxD,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM;IACnD,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAAQ,UAAU,CAAC,KAAK,IAAI;IAE3D,MAAM,kBAAkB;QACtB,QAAQ,MAAM,GAAG,CAAC,CAAA,OAAQ,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;QAClE,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,YAAY,aAAa,kBAAkB,IAAI,CAAC;IACtD,MAAM,cAAc,OAAO,IAAI,CAAC,WAAW,IAAI;IAC/C,MAAM,cAAc,YAAY,GAAG,CAAC,CAAA,OAAQ,SAAS,CAAC,KAAK;IAE3D,MAAM,iBAAiB;QACrB,QAAQ,YAAY,GAAG,CAAC,CAAA,OAAQ,IAAI,KAAK,MAAM,kBAAkB;QACjE,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,aAAa;gBACb,MAAM;YACR;SACD;IACH;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,MAAM,WAAW,aAAa,SAAS;IACvC,MAAM,YAAY,aAAa,UAAU,IAAI;IAC7C,MAAM,oBAAoB,aAAa,oBAAoB,IAAI;IAE/D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ;4BAAU;;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,OAAO,GAAG,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;;;;;;0CAEtE,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,mBAAmB,cAAc;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAsC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;oCACZ;oCAAU;oCAAE,cAAc,IAAI,QAAQ;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAiB,SAAS;;;;;;;;;;;oBAItC,YAAY,YAAY,MAAM,GAAG,mBAChC,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,OAAI;4BAAC,MAAM;4BAAgB,SAAS;;;;;;;;;;;oBAKxC,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,aAAa,sBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;oDAAyB,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;oDAAK;;;;;;;;;;;;;oCAIvE,oBAAoB,mBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;oDAAY,KAAK,KAAK,CAAC;oDAAmB;;;;;;;;;;;;;oCAInD,YAAY,mBACX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;;oDAAK;oDAAY;oDAAU;;;;;;;;;;;;;oCAK/B,oBAAoB,sBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;;oCAIT,oBAAoB,MAAM,oBAAoB,mBAC7C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/PatternAnalysisChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface PatternAnalysisChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function PatternAnalysisChart({ data, detailed = false }: PatternAnalysisChartProps) {\n  if (!data || !data.pattern_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Pattern Analysis</h3>\n        <p className=\"text-gray-500\">No pattern data available</p>\n      </div>\n    );\n  }\n\n  const patternAnalysis = data.pattern_analysis;\n\n  // Common messages chart\n  const commonMessages = patternAnalysis.common_messages || [];\n  const messageChartData = {\n    labels: commonMessages.slice(0, 10).map((msg: any, index: number) => \n      `Message ${index + 1}`\n    ),\n    datasets: [\n      {\n        label: 'Occurrences',\n        data: commonMessages.slice(0, 10).map((msg: any) => msg.count),\n        backgroundColor: 'rgba(139, 92, 246, 0.6)',\n        borderColor: 'rgba(139, 92, 246, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const messageChartOptions = {\n    responsive: true,\n    indexAxis: 'y' as const,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Most Common Log Messages',\n      },\n      tooltip: {\n        callbacks: {\n          afterLabel: function(context: any) {\n            const messageIndex = context.dataIndex;\n            const message = commonMessages[messageIndex]?.message || '';\n            return message.length > 100 ? message.substring(0, 100) + '...' : message;\n          }\n        }\n      }\n    },\n    scales: {\n      x: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  // IP analysis\n  const ipAnalysis = patternAnalysis.ip_analysis || {};\n  const topIps = ipAnalysis.top_ips || [];\n\n  // Status patterns\n  const statusPatterns = patternAnalysis.status_patterns || {};\n  const statusDistribution = statusPatterns.status_distribution || {};\n  const statusCategories = statusPatterns.status_categories || {};\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Pattern Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {commonMessages.length} patterns identified\n        </div>\n      </div>\n\n      {/* Pattern Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div className=\"bg-purple-50 rounded-lg p-4\">\n          <div className=\"text-purple-600 text-sm font-medium\">Common Messages</div>\n          <div className=\"text-purple-900 text-lg font-bold\">\n            {commonMessages.length}\n          </div>\n          <div className=\"text-purple-600 text-xs\">\n            Recurring patterns found\n          </div>\n        </div>\n        \n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <div className=\"text-blue-600 text-sm font-medium\">Unique IPs</div>\n          <div className=\"text-blue-900 text-lg font-bold\">\n            {ipAnalysis.total_unique_ips || 0}\n          </div>\n          <div className=\"text-blue-600 text-xs\">\n            Different IP addresses\n          </div>\n        </div>\n        \n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <div className=\"text-green-600 text-sm font-medium\">Status Codes</div>\n          <div className=\"text-green-900 text-lg font-bold\">\n            {Object.keys(statusDistribution).length}\n          </div>\n          <div className=\"text-green-600 text-xs\">\n            Different response codes\n          </div>\n        </div>\n      </div>\n\n      {/* Charts and Tables */}\n      <div className=\"space-y-6\">\n        {/* Common Messages Chart */}\n        {commonMessages.length > 0 && (\n          <div>\n            <Bar data={messageChartData} options={messageChartOptions} />\n          </div>\n        )}\n\n        {/* Detailed Tables */}\n        {detailed && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Top IPs */}\n            {topIps.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Top IP Addresses</h4>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          IP Address\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Requests\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {topIps.slice(0, 10).map((ip: any, index: number) => (\n                        <tr key={index}>\n                          <td className=\"px-4 py-2 text-sm font-mono text-gray-900\">\n                            {ip.ip}\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-900\">\n                            {ip.count.toLocaleString()}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            )}\n\n            {/* Status Code Categories */}\n            {Object.keys(statusCategories).length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">HTTP Status Categories</h4>\n                <div className=\"space-y-2\">\n                  {Object.entries(statusCategories).map(([category, count]) => (\n                    <div key={category} className=\"flex items-center justify-between p-3 bg-gray-50 rounded\">\n                      <div className=\"flex items-center\">\n                        <span className={`w-3 h-3 rounded-full mr-3 ${\n                          category === '2xx' ? 'bg-green-400' :\n                          category === '3xx' ? 'bg-blue-400' :\n                          category === '4xx' ? 'bg-yellow-400' :\n                          category === '5xx' ? 'bg-red-400' : 'bg-gray-400'\n                        }`}></span>\n                        <span className=\"text-sm font-medium\">{category} Status Codes</span>\n                      </div>\n                      <span className=\"text-sm text-gray-600\">{count as number} requests</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Common Messages Details */}\n        {detailed && commonMessages.length > 0 && (\n          <div>\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Message Pattern Details</h4>\n            <div className=\"space-y-3\">\n              {commonMessages.slice(0, 5).map((msg: any, index: number) => (\n                <div key={index} className=\"p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm font-medium text-purple-600\">\n                      Pattern #{index + 1}\n                    </span>\n                    <div className=\"text-sm text-gray-600\">\n                      <span className=\"font-medium\">{msg.count}</span> occurrences \n                      <span className=\"ml-2\">({msg.percentage}%)</span>\n                    </div>\n                  </div>\n                  <div className=\"text-sm text-gray-700 font-mono bg-white p-3 rounded border\">\n                    {msg.message}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Pattern Insights */}\n        {detailed && (\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Pattern Insights</h4>\n            <div className=\"space-y-2 text-sm\">\n              {commonMessages.length > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-purple-600 mr-2\">🔍</span>\n                  <span>Found {commonMessages.length} recurring message patterns</span>\n                </div>\n              )}\n              \n              {ipAnalysis.total_unique_ips > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-blue-600 mr-2\">🌐</span>\n                  <span>{ipAnalysis.total_unique_ips} unique IP addresses detected</span>\n                </div>\n              )}\n              \n              {Object.keys(statusDistribution).length > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-600 mr-2\">📊</span>\n                  <span>{Object.keys(statusDistribution).length} different HTTP status codes</span>\n                </div>\n              )}\n\n              {/* Success rate insight */}\n              {statusCategories['2xx'] && statusCategories['4xx'] && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-yellow-600 mr-2\">✅</span>\n                  <span>\n                    Success rate: {(\n                      (statusCategories['2xx'] / \n                      (statusCategories['2xx'] + statusCategories['4xx'] + (statusCategories['5xx'] || 0))) * 100\n                    ).toFixed(1)}%\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAZA;;;;AAcA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAQO,SAAS,qBAAqB,EAAE,IAAI,EAAE,WAAW,KAAK,EAA6B;IAChG,IAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB,EAAE;QACnC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,kBAAkB,KAAK,gBAAgB;IAE7C,wBAAwB;IACxB,MAAM,iBAAiB,gBAAgB,eAAe,IAAI,EAAE;IAC5D,MAAM,mBAAmB;QACvB,QAAQ,eAAe,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,KAAU,QACjD,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAExB,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,eAAe,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAa,IAAI,KAAK;gBAC7D,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,sBAAsB;QAC1B,YAAY;QACZ,WAAW;QACX,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;YACA,SAAS;gBACP,WAAW;oBACT,YAAY,SAAS,OAAY;wBAC/B,MAAM,eAAe,QAAQ,SAAS;wBACtC,MAAM,UAAU,cAAc,CAAC,aAAa,EAAE,WAAW;wBACzD,OAAO,QAAQ,MAAM,GAAG,MAAM,QAAQ,SAAS,CAAC,GAAG,OAAO,QAAQ;oBACpE;gBACF;YACF;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,cAAc;IACd,MAAM,aAAa,gBAAgB,WAAW,IAAI,CAAC;IACnD,MAAM,SAAS,WAAW,OAAO,IAAI,EAAE;IAEvC,kBAAkB;IAClB,MAAM,iBAAiB,gBAAgB,eAAe,IAAI,CAAC;IAC3D,MAAM,qBAAqB,eAAe,mBAAmB,IAAI,CAAC;IAClE,MAAM,mBAAmB,eAAe,iBAAiB,IAAI,CAAC;IAE9D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ,eAAe,MAAM;4BAAC;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAsC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CACZ,eAAe,MAAM;;;;;;0CAExB,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,gBAAgB,IAAI;;;;;;0CAElC,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,oBAAoB,MAAM;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;0BAO5C,8OAAC;gBAAI,WAAU;;oBAEZ,eAAe,MAAM,GAAG,mBACvB,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAkB,SAAS;;;;;;;;;;;oBAKzC,0BACC,8OAAC;wBAAI,WAAU;;4BAEZ,OAAO,MAAM,GAAG,mBACf,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;;;;;;;;;;;;8DAKpF,8OAAC;oDAAM,WAAU;8DACd,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAS,sBACjC,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,GAAG,EAAE;;;;;;8EAER,8OAAC;oEAAG,WAAU;8EACX,GAAG,KAAK,CAAC,cAAc;;;;;;;2DALnB;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAgBpB,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,mBACtC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,iBACtD,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,0BAA0B,EAC1C,aAAa,QAAQ,iBACrB,aAAa,QAAQ,gBACrB,aAAa,QAAQ,kBACrB,aAAa,QAAQ,eAAe,eACpC;;;;;;0EACF,8OAAC;gEAAK,WAAU;;oEAAuB;oEAAS;;;;;;;;;;;;;kEAElD,8OAAC;wDAAK,WAAU;;4DAAyB;4DAAgB;;;;;;;;+CAVjD;;;;;;;;;;;;;;;;;;;;;;oBAoBrB,YAAY,eAAe,MAAM,GAAG,mBACnC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAU,sBACzC,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAsC;4DAC1C,QAAQ;;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAe,IAAI,KAAK;;;;;;4DAAQ;0EAChD,8OAAC;gEAAK,WAAU;;oEAAO;oEAAE,IAAI,UAAU;oEAAC;;;;;;;;;;;;;;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;0DACZ,IAAI,OAAO;;;;;;;uCAXN;;;;;;;;;;;;;;;;oBAoBjB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,MAAM,GAAG,mBACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;;oDAAK;oDAAO,eAAe,MAAM;oDAAC;;;;;;;;;;;;;oCAItC,WAAW,gBAAgB,GAAG,mBAC7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAM,WAAW,gBAAgB;oDAAC;;;;;;;;;;;;;oCAItC,OAAO,IAAI,CAAC,oBAAoB,MAAM,GAAG,mBACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAM,OAAO,IAAI,CAAC,oBAAoB,MAAM;oDAAC;;;;;;;;;;;;;oCAKjD,gBAAgB,CAAC,MAAM,IAAI,gBAAgB,CAAC,MAAM,kBACjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;;oDAAK;oDACW,CACb,AAAC,gBAAgB,CAAC,MAAM,GACxB,CAAC,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,CAAC,IAAK,GAC1F,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC", "debugId": null}}, {"offset": {"line": 1941, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/PerformanceChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Bar, Line } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface PerformanceChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function PerformanceChart({ data, detailed = false }: PerformanceChartProps) {\n  if (!data || !data.performance_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Performance Analysis</h3>\n        <p className=\"text-gray-500\">No performance data available (requires enterprise structured logs)</p>\n      </div>\n    );\n  }\n\n  const perfAnalysis = data.performance_analysis;\n\n  // App performance chart\n  const appPerformance = perfAnalysis.performance_by_app || {};\n  const appNames = Object.keys(appPerformance);\n  const appResponseTimes = appNames.map(app => appPerformance[app].avg_response_time);\n\n  const appChartData = {\n    labels: appNames,\n    datasets: [\n      {\n        label: 'Avg Response Time (ms)',\n        data: appResponseTimes,\n        backgroundColor: 'rgba(34, 197, 94, 0.6)',\n        borderColor: 'rgba(34, 197, 94, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const appChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Average Response Time by Application',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Response Time (ms)'\n        }\n      },\n    },\n  };\n\n  // Module performance chart\n  const modulePerformance = perfAnalysis.performance_by_module || {};\n  const moduleNames = Object.keys(modulePerformance);\n  const moduleResponseTimes = moduleNames.map(module => modulePerformance[module].avg_response_time);\n\n  const moduleChartData = {\n    labels: moduleNames,\n    datasets: [\n      {\n        label: 'Avg Response Time (ms)',\n        data: moduleResponseTimes,\n        backgroundColor: 'rgba(59, 130, 246, 0.6)',\n        borderColor: 'rgba(59, 130, 246, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const moduleChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Average Response Time by Module',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Response Time (ms)'\n        }\n      },\n    },\n  };\n\n  const totalRequests = perfAnalysis.total_requests || 0;\n  const avgResponseTime = perfAnalysis.avg_response_time || 0;\n  const p95ResponseTime = perfAnalysis.p95_response_time || 0;\n  const p99ResponseTime = perfAnalysis.p99_response_time || 0;\n  const slowRequestCount = perfAnalysis.slow_request_count || 0;\n  const slowRequests = perfAnalysis.slow_requests || [];\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Performance Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {totalRequests.toLocaleString()} requests analyzed\n        </div>\n      </div>\n\n      {/* Performance Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <div className=\"text-green-600 text-sm font-medium\">Avg Response</div>\n          <div className=\"text-green-900 text-lg font-bold\">\n            {avgResponseTime.toFixed(0)}ms\n          </div>\n          <div className=\"text-green-600 text-xs\">\n            Average response time\n          </div>\n        </div>\n        \n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <div className=\"text-blue-600 text-sm font-medium\">95th Percentile</div>\n          <div className=\"text-blue-900 text-lg font-bold\">\n            {p95ResponseTime.toFixed(0)}ms\n          </div>\n          <div className=\"text-blue-600 text-xs\">\n            95% of requests faster than\n          </div>\n        </div>\n        \n        <div className=\"bg-purple-50 rounded-lg p-4\">\n          <div className=\"text-purple-600 text-sm font-medium\">99th Percentile</div>\n          <div className=\"text-purple-900 text-lg font-bold\">\n            {p99ResponseTime.toFixed(0)}ms\n          </div>\n          <div className=\"text-purple-600 text-xs\">\n            99% of requests faster than\n          </div>\n        </div>\n        \n        <div className=\"bg-red-50 rounded-lg p-4\">\n          <div className=\"text-red-600 text-sm font-medium\">Slow Requests</div>\n          <div className=\"text-red-900 text-lg font-bold\">\n            {slowRequestCount}\n          </div>\n          <div className=\"text-red-600 text-xs\">\n            Requests &gt; 5 seconds\n          </div>\n        </div>\n      </div>\n\n      {/* Performance Status */}\n      <div className=\"mb-6 p-4 rounded-lg\">\n        {avgResponseTime < 500 ? (\n          <div className=\"bg-green-50 border border-green-200\">\n            <div className=\"flex items-center p-4\">\n              <div className=\"text-green-400 text-xl mr-3\">⚡</div>\n              <div>\n                <div className=\"text-green-800 font-medium\">Excellent Performance</div>\n                <div className=\"text-green-600 text-sm\">\n                  Average response time is under 500ms\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : avgResponseTime < 2000 ? (\n          <div className=\"bg-yellow-50 border border-yellow-200\">\n            <div className=\"flex items-center p-4\">\n              <div className=\"text-yellow-400 text-xl mr-3\">⚠️</div>\n              <div>\n                <div className=\"text-yellow-800 font-medium\">Good Performance</div>\n                <div className=\"text-yellow-600 text-sm\">\n                  Average response time is acceptable but could be optimized\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"bg-red-50 border border-red-200\">\n            <div className=\"flex items-center p-4\">\n              <div className=\"text-red-400 text-xl mr-3\">🐌</div>\n              <div>\n                <div className=\"text-red-800 font-medium\">Performance Issues Detected</div>\n                <div className=\"text-red-600 text-sm\">\n                  High response times may impact user experience\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Charts */}\n      <div className=\"space-y-6\">\n        {/* Application Performance */}\n        {appNames.length > 0 && (\n          <div>\n            <Bar data={appChartData} options={appChartOptions} />\n          </div>\n        )}\n\n        {/* Module Performance */}\n        {detailed && moduleNames.length > 0 && (\n          <div>\n            <Bar data={moduleChartData} options={moduleChartOptions} />\n          </div>\n        )}\n\n        {/* Performance Tables */}\n        {detailed && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Application Performance Table */}\n            {appNames.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Application Performance</h4>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Application\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Avg Time\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Requests\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Slow\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {appNames.map((app) => {\n                        const appData = appPerformance[app];\n                        return (\n                          <tr key={app}>\n                            <td className=\"px-4 py-2 text-sm font-medium text-gray-900\">\n                              {app}\n                            </td>\n                            <td className=\"px-4 py-2 text-sm text-gray-900\">\n                              {appData.avg_response_time.toFixed(0)}ms\n                            </td>\n                            <td className=\"px-4 py-2 text-sm text-gray-900\">\n                              {appData.request_count.toLocaleString()}\n                            </td>\n                            <td className=\"px-4 py-2 text-sm text-gray-900\">\n                              {appData.slow_request_count}\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            )}\n\n            {/* Slowest Requests */}\n            {slowRequests.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Slowest Requests</h4>\n                <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                  {slowRequests.slice(0, 10).map((request: any, index: number) => (\n                    <div key={index} className=\"p-3 bg-red-50 rounded border\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <span className=\"text-sm font-medium text-red-600\">\n                          {request.response_time.toFixed(0)}ms\n                        </span>\n                        <span className=\"text-xs text-gray-500\">\n                          {request.app_name}\n                        </span>\n                      </div>\n                      <div className=\"text-xs text-gray-600\">\n                        {request.url && (\n                          <div className=\"font-mono\">{request.url}</div>\n                        )}\n                        {request.module && (\n                          <div>Module: {request.module}</div>\n                        )}\n                        {request.request_id && (\n                          <div>ID: {request.request_id}</div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAWA;AAdA;;;;AAgBA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,eAAY,EACZ,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAQO,SAAS,iBAAiB,EAAE,IAAI,EAAE,WAAW,KAAK,EAAyB;IACxF,IAAI,CAAC,QAAQ,CAAC,KAAK,oBAAoB,EAAE;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,eAAe,KAAK,oBAAoB;IAE9C,wBAAwB;IACxB,MAAM,iBAAiB,aAAa,kBAAkB,IAAI,CAAC;IAC3D,MAAM,WAAW,OAAO,IAAI,CAAC;IAC7B,MAAM,mBAAmB,SAAS,GAAG,CAAC,CAAA,MAAO,cAAc,CAAC,IAAI,CAAC,iBAAiB;IAElF,MAAM,eAAe;QACnB,QAAQ;QACR,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,kBAAkB;QACtB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,aAAa,qBAAqB,IAAI,CAAC;IACjE,MAAM,cAAc,OAAO,IAAI,CAAC;IAChC,MAAM,sBAAsB,YAAY,GAAG,CAAC,CAAA,SAAU,iBAAiB,CAAC,OAAO,CAAC,iBAAiB;IAEjG,MAAM,kBAAkB;QACtB,QAAQ;QACR,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,aAAa,cAAc,IAAI;IACrD,MAAM,kBAAkB,aAAa,iBAAiB,IAAI;IAC1D,MAAM,kBAAkB,aAAa,iBAAiB,IAAI;IAC1D,MAAM,kBAAkB,aAAa,iBAAiB,IAAI;IAC1D,MAAM,mBAAmB,aAAa,kBAAkB,IAAI;IAC5D,MAAM,eAAe,aAAa,aAAa,IAAI,EAAE;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,cAAc;4BAAG;;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,OAAO,CAAC;oCAAG;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,OAAO,CAAC;oCAAG;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAsC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;oCACZ,gBAAgB,OAAO,CAAC;oCAAG;;;;;;;0CAE9B,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,8OAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,oBACjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;2BAM5C,kBAAkB,qBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;0CAC9C,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA8B;;;;;;kDAC7C,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;yCAO/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA4B;;;;;;0CAC3C,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA2B;;;;;;kDAC1C,8OAAC;wCAAI,WAAU;kDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhD,8OAAC;gBAAI,WAAU;;oBAEZ,SAAS,MAAM,GAAG,mBACjB,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAc,SAAS;;;;;;;;;;;oBAKrC,YAAY,YAAY,MAAM,GAAG,mBAChC,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAiB,SAAS;;;;;;;;;;;oBAKxC,0BACC,8OAAC;wBAAI,WAAU;;4BAEZ,SAAS,MAAM,GAAG,mBACjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;;;;;;;;;;;;8DAKpF,8OAAC;oDAAM,WAAU;8DACd,SAAS,GAAG,CAAC,CAAC;wDACb,MAAM,UAAU,cAAc,CAAC,IAAI;wDACnC,qBACE,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX;;;;;;8EAEH,8OAAC;oEAAG,WAAU;;wEACX,QAAQ,iBAAiB,CAAC,OAAO,CAAC;wEAAG;;;;;;;8EAExC,8OAAC;oEAAG,WAAU;8EACX,QAAQ,aAAa,CAAC,cAAc;;;;;;8EAEvC,8OAAC;oEAAG,WAAU;8EACX,QAAQ,kBAAkB;;;;;;;2DAXtB;;;;;oDAeb;;;;;;;;;;;;;;;;;;;;;;;4BAQT,aAAa,MAAM,GAAG,mBACrB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,SAAc,sBAC5C,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEACb,QAAQ,aAAa,CAAC,OAAO,CAAC;oEAAG;;;;;;;0EAEpC,8OAAC;gEAAK,WAAU;0EACb,QAAQ,QAAQ;;;;;;;;;;;;kEAGrB,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,GAAG,kBACV,8OAAC;gEAAI,WAAU;0EAAa,QAAQ,GAAG;;;;;;4DAExC,QAAQ,MAAM,kBACb,8OAAC;;oEAAI;oEAAS,QAAQ,MAAM;;;;;;;4DAE7B,QAAQ,UAAU,kBACjB,8OAAC;;oEAAI;oEAAK,QAAQ,UAAU;;;;;;;;;;;;;;+CAjBxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8B9B", "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/CustomerChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { Bar } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface CustomerChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function CustomerChart({ data, detailed = false }: CustomerChartProps) {\n  if (!data || !data.customer_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Customer Analysis</h3>\n        <p className=\"text-gray-500\">No customer data available (requires enterprise structured logs)</p>\n      </div>\n    );\n  }\n\n  const customerAnalysis = data.customer_analysis;\n  const topCustomers = customerAnalysis.top_customers || [];\n  const errorProneCustomers = customerAnalysis.error_prone_customers || [];\n  const totalUniqueCustomers = customerAnalysis.total_unique_customers || 0;\n\n  // Top customers activity chart\n  const activityChartData = {\n    labels: topCustomers.slice(0, 10).map((customer: any) => \n      customer.customer_id.length > 10 \n        ? customer.customer_id.substring(0, 10) + '...'\n        : customer.customer_id\n    ),\n    datasets: [\n      {\n        label: 'Activity Count',\n        data: topCustomers.slice(0, 10).map((customer: any) => customer.activity_count),\n        backgroundColor: 'rgba(34, 197, 94, 0.6)',\n        borderColor: 'rgba(34, 197, 94, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const activityChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Top 10 Most Active Customers',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  // Error-prone customers chart\n  const errorChartData = {\n    labels: errorProneCustomers.slice(0, 10).map((customer: any) => \n      customer.customer_id.length > 10 \n        ? customer.customer_id.substring(0, 10) + '...'\n        : customer.customer_id\n    ),\n    datasets: [\n      {\n        label: 'Error Count',\n        data: errorProneCustomers.slice(0, 10).map((customer: any) => customer.error_count),\n        backgroundColor: 'rgba(239, 68, 68, 0.6)',\n        borderColor: 'rgba(239, 68, 68, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const errorChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Customers with Most Errors',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Customer Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {totalUniqueCustomers} unique customers\n        </div>\n      </div>\n\n      {/* Customer Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <div className=\"text-blue-600 text-sm font-medium\">Total Customers</div>\n          <div className=\"text-blue-900 text-lg font-bold\">\n            {totalUniqueCustomers.toLocaleString()}\n          </div>\n          <div className=\"text-blue-600 text-xs\">\n            Unique customer IDs\n          </div>\n        </div>\n        \n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <div className=\"text-green-600 text-sm font-medium\">Active Customers</div>\n          <div className=\"text-green-900 text-lg font-bold\">\n            {topCustomers.length}\n          </div>\n          <div className=\"text-green-600 text-xs\">\n            Customers with activity\n          </div>\n        </div>\n        \n        <div className=\"bg-red-50 rounded-lg p-4\">\n          <div className=\"text-red-600 text-sm font-medium\">Error-Prone</div>\n          <div className=\"text-red-900 text-lg font-bold\">\n            {errorProneCustomers.length}\n          </div>\n          <div className=\"text-red-600 text-xs\">\n            Customers with errors\n          </div>\n        </div>\n      </div>\n\n      {/* Charts */}\n      <div className=\"space-y-6\">\n        {/* Top Active Customers */}\n        {topCustomers.length > 0 && (\n          <div>\n            <Bar data={activityChartData} options={activityChartOptions} />\n          </div>\n        )}\n\n        {/* Error-Prone Customers */}\n        {detailed && errorProneCustomers.length > 0 && (\n          <div>\n            <Bar data={errorChartData} options={errorChartOptions} />\n          </div>\n        )}\n\n        {/* Detailed Tables */}\n        {detailed && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Top Customers Table */}\n            {topCustomers.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Most Active Customers</h4>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Customer ID\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Activity\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Errors\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Apps\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {topCustomers.slice(0, 10).map((customer: any, index: number) => (\n                        <tr key={index}>\n                          <td className=\"px-4 py-2 text-sm font-mono text-gray-900\">\n                            {customer.customer_id.length > 15 \n                              ? customer.customer_id.substring(0, 15) + '...'\n                              : customer.customer_id\n                            }\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-900\">\n                            {customer.activity_count.toLocaleString()}\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-900\">\n                            {customer.error_count}\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-900\">\n                            {customer.apps_used.length}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            )}\n\n            {/* Error-Prone Customers Table */}\n            {errorProneCustomers.length > 0 && (\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 mb-3\">Customers with Errors</h4>\n                <div className=\"overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Customer ID\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                          Error Count\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {errorProneCustomers.slice(0, 10).map((customer: any, index: number) => (\n                        <tr key={index}>\n                          <td className=\"px-4 py-2 text-sm font-mono text-gray-900\">\n                            {customer.customer_id.length > 20 \n                              ? customer.customer_id.substring(0, 20) + '...'\n                              : customer.customer_id\n                            }\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-red-600 font-medium\">\n                            {customer.error_count}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Customer Insights */}\n        {detailed && (\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Customer Insights</h4>\n            <div className=\"space-y-2 text-sm\">\n              {totalUniqueCustomers > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-blue-600 mr-2\">👥</span>\n                  <span>Serving {totalUniqueCustomers} unique customers</span>\n                </div>\n              )}\n              \n              {topCustomers.length > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-600 mr-2\">📈</span>\n                  <span>\n                    Most active customer: {topCustomers[0]?.customer_id} \n                    ({topCustomers[0]?.activity_count} activities)\n                  </span>\n                </div>\n              )}\n              \n              {errorProneCustomers.length > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-red-600 mr-2\">⚠️</span>\n                  <span>\n                    {errorProneCustomers.length} customers experiencing errors\n                  </span>\n                </div>\n              )}\n\n              {/* Multi-app usage */}\n              {topCustomers.some((c: any) => c.apps_used.length > 1) && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-purple-600 mr-2\">📱</span>\n                  <span>\n                    Some customers use multiple applications\n                  </span>\n                </div>\n              )}\n\n              {/* Platform diversity */}\n              {topCustomers.some((c: any) => c.platforms_used && c.platforms_used.length > 1) && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-orange-600 mr-2\">🌐</span>\n                  <span>\n                    Multi-platform customer access detected\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAZA;;;;AAcA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM;AAQO,SAAS,cAAc,EAAE,IAAI,EAAE,WAAW,KAAK,EAAsB;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,iBAAiB,EAAE;QACpC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,mBAAmB,KAAK,iBAAiB;IAC/C,MAAM,eAAe,iBAAiB,aAAa,IAAI,EAAE;IACzD,MAAM,sBAAsB,iBAAiB,qBAAqB,IAAI,EAAE;IACxE,MAAM,uBAAuB,iBAAiB,sBAAsB,IAAI;IAExE,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB,QAAQ,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,WACrC,SAAS,WAAW,CAAC,MAAM,GAAG,KAC1B,SAAS,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACxC,SAAS,WAAW;QAE1B,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,WAAkB,SAAS,cAAc;gBAC9E,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,uBAAuB;QAC3B,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,iBAAiB;QACrB,QAAQ,oBAAoB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,WAC5C,SAAS,WAAW,CAAC,MAAM,GAAG,KAC1B,SAAS,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACxC,SAAS,WAAW;QAE1B,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,oBAAoB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,WAAkB,SAAS,WAAW;gBAClF,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,oBAAoB;QACxB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ;4BAAqB;;;;;;;;;;;;;0BAK1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,cAAc;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,MAAM;;;;;;0CAE7B,8OAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;;oBAEZ,aAAa,MAAM,GAAG,mBACrB,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAmB,SAAS;;;;;;;;;;;oBAK1C,YAAY,oBAAoB,MAAM,GAAG,mBACxC,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAgB,SAAS;;;;;;;;;;;oBAKvC,0BACC,8OAAC;wBAAI,WAAU;;4BAEZ,aAAa,MAAM,GAAG,mBACrB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;;;;;;;;;;;;8DAKpF,8OAAC;oDAAM,WAAU;8DACd,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAe,sBAC7C,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,SAAS,WAAW,CAAC,MAAM,GAAG,KAC3B,SAAS,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACxC,SAAS,WAAW;;;;;;8EAG1B,8OAAC;oEAAG,WAAU;8EACX,SAAS,cAAc,CAAC,cAAc;;;;;;8EAEzC,8OAAC;oEAAG,WAAU;8EACX,SAAS,WAAW;;;;;;8EAEvB,8OAAC;oEAAG,WAAU;8EACX,SAAS,SAAS,CAAC,MAAM;;;;;;;2DAdrB;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAyBpB,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;0EAGhF,8OAAC;gEAAG,WAAU;0EAAkE;;;;;;;;;;;;;;;;;8DAKpF,8OAAC;oDAAM,WAAU;8DACd,oBAAoB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,UAAe,sBACpD,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,SAAS,WAAW,CAAC,MAAM,GAAG,KAC3B,SAAS,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACxC,SAAS,WAAW;;;;;;8EAG1B,8OAAC;oEAAG,WAAU;8EACX,SAAS,WAAW;;;;;;;2DARhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAqBxB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,uBAAuB,mBACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;oDAAS;oDAAqB;;;;;;;;;;;;;oCAIvC,aAAa,MAAM,GAAG,mBACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;oDACmB,YAAY,CAAC,EAAE,EAAE;oDAAY;oDAClD,YAAY,CAAC,EAAE,EAAE;oDAAe;;;;;;;;;;;;;oCAKvC,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;0DACpC,8OAAC;;oDACE,oBAAoB,MAAM;oDAAC;;;;;;;;;;;;;oCAMjC,aAAa,IAAI,CAAC,CAAC,IAAW,EAAE,SAAS,CAAC,MAAM,GAAG,oBAClD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;;oCAOT,aAAa,IAAI,CAAC,CAAC,IAAW,EAAE,cAAc,IAAI,EAAE,cAAc,CAAC,MAAM,GAAG,oBAC3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 3365, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/charts/ApplicationChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement,\n} from 'chart.js';\nimport { Bar, Doughnut } from 'react-chartjs-2';\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ArcElement\n);\n\ninterface ApplicationChartProps {\n  data: any;\n  detailed?: boolean;\n}\n\nexport default function ApplicationChart({ data, detailed = false }: ApplicationChartProps) {\n  if (!data || !data.application_analysis) {\n    return (\n      <div className=\"bg-white rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Application Analysis</h3>\n        <p className=\"text-gray-500\">No application data available (requires enterprise structured logs)</p>\n      </div>\n    );\n  }\n\n  const appAnalysis = data.application_analysis;\n  const applicationSummary = appAnalysis.application_summary || [];\n  const totalApplications = appAnalysis.total_applications || 0;\n\n  // Application activity chart\n  const activityChartData = {\n    labels: applicationSummary.slice(0, 10).map((app: any) => app.app_name),\n    datasets: [\n      {\n        label: 'Activity Count',\n        data: applicationSummary.slice(0, 10).map((app: any) => app.activity_count),\n        backgroundColor: 'rgba(59, 130, 246, 0.6)',\n        borderColor: 'rgba(59, 130, 246, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const activityChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Application Activity',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        ticks: {\n          precision: 0\n        }\n      },\n    },\n  };\n\n  // Error rate distribution\n  const errorRateData = {\n    labels: applicationSummary.map((app: any) => app.app_name),\n    datasets: [\n      {\n        data: applicationSummary.map((app: any) => app.error_rate),\n        backgroundColor: [\n          '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',\n          '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9'\n        ],\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const errorRateOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        position: 'bottom' as const,\n      },\n      title: {\n        display: true,\n        text: 'Error Rate by Application',\n      },\n      tooltip: {\n        callbacks: {\n          label: function(context: any) {\n            return `${context.label}: ${context.parsed}% error rate`;\n          }\n        }\n      }\n    },\n  };\n\n  // Performance chart\n  const performanceChartData = {\n    labels: applicationSummary\n      .filter((app: any) => app.avg_response_time > 0)\n      .map((app: any) => app.app_name),\n    datasets: [\n      {\n        label: 'Avg Response Time (ms)',\n        data: applicationSummary\n          .filter((app: any) => app.avg_response_time > 0)\n          .map((app: any) => app.avg_response_time),\n        backgroundColor: 'rgba(34, 197, 94, 0.6)',\n        borderColor: 'rgba(34, 197, 94, 1)',\n        borderWidth: 1,\n      },\n    ],\n  };\n\n  const performanceChartOptions = {\n    responsive: true,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: true,\n        text: 'Average Response Time by Application',\n      },\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        title: {\n          display: true,\n          text: 'Response Time (ms)'\n        }\n      },\n    },\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Application Analysis</h3>\n        <div className=\"text-sm text-gray-500\">\n          {totalApplications} applications monitored\n        </div>\n      </div>\n\n      {/* Application Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n        <div className=\"bg-blue-50 rounded-lg p-4\">\n          <div className=\"text-blue-600 text-sm font-medium\">Total Apps</div>\n          <div className=\"text-blue-900 text-lg font-bold\">\n            {totalApplications}\n          </div>\n          <div className=\"text-blue-600 text-xs\">\n            Applications monitored\n          </div>\n        </div>\n        \n        <div className=\"bg-green-50 rounded-lg p-4\">\n          <div className=\"text-green-600 text-sm font-medium\">Most Active</div>\n          <div className=\"text-green-900 text-lg font-bold\">\n            {applicationSummary[0]?.app_name || 'N/A'}\n          </div>\n          <div className=\"text-green-600 text-xs\">\n            Highest activity app\n          </div>\n        </div>\n        \n        <div className=\"bg-red-50 rounded-lg p-4\">\n          <div className=\"text-red-600 text-sm font-medium\">Highest Error Rate</div>\n          <div className=\"text-red-900 text-lg font-bold\">\n            {Math.max(...applicationSummary.map((app: any) => app.error_rate)).toFixed(1)}%\n          </div>\n          <div className=\"text-red-600 text-xs\">\n            Maximum error rate\n          </div>\n        </div>\n        \n        <div className=\"bg-purple-50 rounded-lg p-4\">\n          <div className=\"text-purple-600 text-sm font-medium\">Avg Modules</div>\n          <div className=\"text-purple-900 text-lg font-bold\">\n            {applicationSummary.length > 0 \n              ? Math.round(applicationSummary.reduce((sum: number, app: any) => sum + app.module_count, 0) / applicationSummary.length)\n              : 0\n            }\n          </div>\n          <div className=\"text-purple-600 text-xs\">\n            Modules per application\n          </div>\n        </div>\n      </div>\n\n      {/* Charts */}\n      <div className=\"space-y-6\">\n        {/* Application Activity */}\n        {applicationSummary.length > 0 && (\n          <div>\n            <Bar data={activityChartData} options={activityChartOptions} />\n          </div>\n        )}\n\n        {/* Error Rate and Performance Charts */}\n        {detailed && applicationSummary.length > 0 && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Error Rate Distribution */}\n            <div>\n              <Doughnut data={errorRateData} options={errorRateOptions} />\n            </div>\n\n            {/* Performance Chart */}\n            {applicationSummary.some((app: any) => app.avg_response_time > 0) && (\n              <div>\n                <Bar data={performanceChartData} options={performanceChartOptions} />\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Application Details Table */}\n        {detailed && applicationSummary.length > 0 && (\n          <div>\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Application Details</h4>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Application\n                    </th>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Activity\n                    </th>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Errors\n                    </th>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Error Rate\n                    </th>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Avg Response\n                    </th>\n                    <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase\">\n                      Modules\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {applicationSummary.map((app: any, index: number) => (\n                    <tr key={index}>\n                      <td className=\"px-4 py-2 text-sm font-medium text-gray-900\">\n                        {app.app_name}\n                      </td>\n                      <td className=\"px-4 py-2 text-sm text-gray-900\">\n                        {app.activity_count.toLocaleString()}\n                      </td>\n                      <td className=\"px-4 py-2 text-sm text-gray-900\">\n                        {app.error_count}\n                      </td>\n                      <td className=\"px-4 py-2 text-sm\">\n                        <span className={`font-medium ${\n                          app.error_rate > 10 ? 'text-red-600' :\n                          app.error_rate > 5 ? 'text-yellow-600' :\n                          'text-green-600'\n                        }`}>\n                          {app.error_rate.toFixed(1)}%\n                        </span>\n                      </td>\n                      <td className=\"px-4 py-2 text-sm text-gray-900\">\n                        {app.avg_response_time > 0 ? `${app.avg_response_time.toFixed(0)}ms` : 'N/A'}\n                      </td>\n                      <td className=\"px-4 py-2 text-sm text-gray-900\">\n                        {app.module_count}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        )}\n\n        {/* Application Insights */}\n        {detailed && (\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h4 className=\"text-md font-medium text-gray-900 mb-3\">Application Insights</h4>\n            <div className=\"space-y-2 text-sm\">\n              {totalApplications > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-blue-600 mr-2\">📱</span>\n                  <span>Monitoring {totalApplications} applications</span>\n                </div>\n              )}\n              \n              {applicationSummary.length > 0 && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-green-600 mr-2\">📈</span>\n                  <span>\n                    Most active: {applicationSummary[0]?.app_name} \n                    ({applicationSummary[0]?.activity_count.toLocaleString()} activities)\n                  </span>\n                </div>\n              )}\n              \n              {/* High error rate apps */}\n              {applicationSummary.some((app: any) => app.error_rate > 10) && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-red-600 mr-2\">⚠️</span>\n                  <span>\n                    {applicationSummary.filter((app: any) => app.error_rate > 10).length} \n                    {' '}applications have high error rates (&gt;10%)\n                  </span>\n                </div>\n              )}\n\n              {/* Performance insights */}\n              {applicationSummary.some((app: any) => app.avg_response_time > 2000) && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-orange-600 mr-2\">🐌</span>\n                  <span>\n                    Some applications have slow response times (&gt;2s)\n                  </span>\n                </div>\n              )}\n\n              {/* Module complexity */}\n              {applicationSummary.some((app: any) => app.module_count > 5) && (\n                <div className=\"flex items-center\">\n                  <span className=\"text-purple-600 mr-2\">🔧</span>\n                  <span>\n                    Complex applications with multiple modules detected\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAUA;AAbA;;;;AAeA,4JAAA,CAAA,QAAO,CAAC,QAAQ,CACd,4JAAA,CAAA,gBAAa,EACb,4JAAA,CAAA,cAAW,EACX,4JAAA,CAAA,aAAU,EACV,4JAAA,CAAA,QAAK,EACL,4JAAA,CAAA,UAAO,EACP,4JAAA,CAAA,SAAM,EACN,4JAAA,CAAA,aAAU;AAQG,SAAS,iBAAiB,EAAE,IAAI,EAAE,WAAW,KAAK,EAAyB;IACxF,IAAI,CAAC,QAAQ,CAAC,KAAK,oBAAoB,EAAE;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,MAAM,cAAc,KAAK,oBAAoB;IAC7C,MAAM,qBAAqB,YAAY,mBAAmB,IAAI,EAAE;IAChE,MAAM,oBAAoB,YAAY,kBAAkB,IAAI;IAE5D,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,QAAQ,mBAAmB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAa,IAAI,QAAQ;QACtE,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,mBAAmB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAa,IAAI,cAAc;gBAC1E,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,uBAAuB;QAC3B,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,WAAW;gBACb;YACF;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB;QACpB,QAAQ,mBAAmB,GAAG,CAAC,CAAC,MAAa,IAAI,QAAQ;QACzD,UAAU;YACR;gBACE,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAa,IAAI,UAAU;gBACzD,iBAAiB;oBACf;oBAAW;oBAAW;oBAAW;oBAAW;oBAC5C;oBAAW;oBAAW;oBAAW;oBAAW;iBAC7C;gBACD,aAAa;YACf;SACD;IACH;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;YACA,SAAS;gBACP,WAAW;oBACT,OAAO,SAAS,OAAY;wBAC1B,OAAO,GAAG,QAAQ,KAAK,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,YAAY,CAAC;oBAC1D;gBACF;YACF;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB;QAC3B,QAAQ,mBACL,MAAM,CAAC,CAAC,MAAa,IAAI,iBAAiB,GAAG,GAC7C,GAAG,CAAC,CAAC,MAAa,IAAI,QAAQ;QACjC,UAAU;YACR;gBACE,OAAO;gBACP,MAAM,mBACH,MAAM,CAAC,CAAC,MAAa,IAAI,iBAAiB,GAAG,GAC7C,GAAG,CAAC,CAAC,MAAa,IAAI,iBAAiB;gBAC1C,iBAAiB;gBACjB,aAAa;gBACb,aAAa;YACf;SACD;IACH;IAEA,MAAM,0BAA0B;QAC9B,YAAY;QACZ,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;gBACT,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;gBACD,aAAa;gBACb,OAAO;oBACL,SAAS;oBACT,MAAM;gBACR;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;4BACZ;4BAAkB;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAoC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;0CACZ,kBAAkB,CAAC,EAAE,EAAE,YAAY;;;;;;0CAEtC,8OAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;0CAClD,8OAAC;gCAAI,WAAU;;oCACZ,KAAK,GAAG,IAAI,mBAAmB,GAAG,CAAC,CAAC,MAAa,IAAI,UAAU,GAAG,OAAO,CAAC;oCAAG;;;;;;;0CAEhF,8OAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAsC;;;;;;0CACrD,8OAAC;gCAAI,WAAU;0CACZ,mBAAmB,MAAM,GAAG,IACzB,KAAK,KAAK,CAAC,mBAAmB,MAAM,CAAC,CAAC,KAAa,MAAa,MAAM,IAAI,YAAY,EAAE,KAAK,mBAAmB,MAAM,IACtH;;;;;;0CAGN,8OAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAI,WAAU;;oBAEZ,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC;kCACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;4BAAC,MAAM;4BAAmB,SAAS;;;;;;;;;;;oBAK1C,YAAY,mBAAmB,MAAM,GAAG,mBACvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;0CACC,cAAA,8OAAC,sJAAA,CAAA,WAAQ;oCAAC,MAAM;oCAAe,SAAS;;;;;;;;;;;4BAIzC,mBAAmB,IAAI,CAAC,CAAC,MAAa,IAAI,iBAAiB,GAAG,oBAC7D,8OAAC;0CACC,cAAA,8OAAC,sJAAA,CAAA,MAAG;oCAAC,MAAM;oCAAsB,SAAS;;;;;;;;;;;;;;;;;oBAOjD,YAAY,mBAAmB,MAAM,GAAG,mBACvC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;kEAGhF,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;kEAGhF,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;kEAGhF,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;kEAGhF,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;kEAGhF,8OAAC;wDAAG,WAAU;kEAAkE;;;;;;;;;;;;;;;;;sDAKpF,8OAAC;4CAAM,WAAU;sDACd,mBAAmB,GAAG,CAAC,CAAC,KAAU,sBACjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,IAAI,QAAQ;;;;;;sEAEf,8OAAC;4DAAG,WAAU;sEACX,IAAI,cAAc,CAAC,cAAc;;;;;;sEAEpC,8OAAC;4DAAG,WAAU;sEACX,IAAI,WAAW;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,YAAY,EAC5B,IAAI,UAAU,GAAG,KAAK,iBACtB,IAAI,UAAU,GAAG,IAAI,oBACrB,kBACA;;oEACC,IAAI,UAAU,CAAC,OAAO,CAAC;oEAAG;;;;;;;;;;;;sEAG/B,8OAAC;4DAAG,WAAU;sEACX,IAAI,iBAAiB,GAAG,IAAI,GAAG,IAAI,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG;;;;;;sEAEzE,8OAAC;4DAAG,WAAU;sEACX,IAAI,YAAY;;;;;;;mDAvBZ;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAkCpB,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;oCACZ,oBAAoB,mBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;0DACrC,8OAAC;;oDAAK;oDAAY;oDAAkB;;;;;;;;;;;;;oCAIvC,mBAAmB,MAAM,GAAG,mBAC3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,8OAAC;;oDAAK;oDACU,kBAAkB,CAAC,EAAE,EAAE;oDAAS;oDAC5C,kBAAkB,CAAC,EAAE,EAAE,eAAe;oDAAiB;;;;;;;;;;;;;oCAM9D,mBAAmB,IAAI,CAAC,CAAC,MAAa,IAAI,UAAU,GAAG,qBACtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoB;;;;;;0DACpC,8OAAC;;oDACE,mBAAmB,MAAM,CAAC,CAAC,MAAa,IAAI,UAAU,GAAG,IAAI,MAAM;oDACnE;oDAAI;;;;;;;;;;;;;oCAMV,mBAAmB,IAAI,CAAC,CAAC,MAAa,IAAI,iBAAiB,GAAG,uBAC7D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;;oCAOT,mBAAmB,IAAI,CAAC,CAAC,MAAa,IAAI,YAAY,GAAG,oBACxD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 4091, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/LogSearch.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface LogSearchProps {\n  analysisId: string;\n}\n\ninterface SearchResult {\n  line_number: number;\n  raw_line: string;\n  timestamp: string | null;\n  level: string | null;\n  message: string | null;\n  fields: any;\n}\n\nexport default function LogSearch({ analysisId }: LogSearchProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [levelFilter, setLevelFilter] = useState('');\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [totalResults, setTotalResults] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [hasMore, setHasMore] = useState(false);\n  const resultsPerPage = 50;\n\n  const searchLogs = async (page = 1, append = false) => {\n    if (!searchQuery.trim() && !levelFilter) return;\n\n    setLoading(true);\n    try {\n      const params = new URLSearchParams({\n        q: searchQuery,\n        limit: resultsPerPage.toString(),\n        offset: ((page - 1) * resultsPerPage).toString(),\n      });\n\n      if (levelFilter) {\n        params.append('level', levelFilter);\n      }\n\n      const response = await fetch(`/api/analysis/${analysisId}/search?${params}`);\n      const data = await response.json();\n\n      if (append) {\n        setResults(prev => [...prev, ...data.results]);\n      } else {\n        setResults(data.results);\n      }\n\n      setTotalResults(data.total_results);\n      setHasMore(data.has_more);\n      setCurrentPage(page);\n    } catch (error) {\n      console.error('Search failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = () => {\n    setCurrentPage(1);\n    searchLogs(1, false);\n  };\n\n  const loadMore = () => {\n    searchLogs(currentPage + 1, true);\n  };\n\n  const clearSearch = () => {\n    setSearchQuery('');\n    setLevelFilter('');\n    setResults([]);\n    setTotalResults(0);\n    setCurrentPage(1);\n    setHasMore(false);\n  };\n\n  const formatTimestamp = (timestamp: string | null) => {\n    if (!timestamp) return 'N/A';\n    try {\n      return new Date(timestamp).toLocaleString();\n    } catch {\n      return timestamp;\n    }\n  };\n\n  const getLevelColor = (level: string | null) => {\n    switch (level?.toUpperCase()) {\n      case 'ERROR': return 'text-red-600 bg-red-50';\n      case 'WARN': \n      case 'WARNING': return 'text-yellow-600 bg-yellow-50';\n      case 'INFO': return 'text-blue-600 bg-blue-50';\n      case 'DEBUG': return 'text-gray-600 bg-gray-50';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Search Controls */}\n      <div className=\"bg-white rounded-lg p-6 border\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Search Log Entries</h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Query\n            </label>\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              placeholder=\"Enter search terms...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Log Level\n            </label>\n            <select\n              value={levelFilter}\n              onChange={(e) => setLevelFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"\">All Levels</option>\n              <option value=\"ERROR\">Error</option>\n              <option value=\"WARN\">Warning</option>\n              <option value=\"INFO\">Info</option>\n              <option value=\"DEBUG\">Debug</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={handleSearch}\n            disabled={loading || (!searchQuery.trim() && !levelFilter)}\n            className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors\"\n          >\n            {loading ? 'Searching...' : 'Search'}\n          </button>\n          \n          <button\n            onClick={clearSearch}\n            className=\"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors\"\n          >\n            Clear\n          </button>\n        </div>\n\n        {totalResults > 0 && (\n          <div className=\"mt-4 text-sm text-gray-600\">\n            Found {totalResults.toLocaleString()} results\n          </div>\n        )}\n      </div>\n\n      {/* Search Results */}\n      {results.length > 0 && (\n        <div className=\"bg-white rounded-lg border\">\n          <div className=\"p-4 border-b\">\n            <h4 className=\"text-md font-medium text-gray-900\">Search Results</h4>\n          </div>\n          \n          <div className=\"divide-y divide-gray-200\">\n            {results.map((result, index) => (\n              <div key={index} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-sm text-gray-500 font-mono\">\n                      Line {result.line_number}\n                    </span>\n                    \n                    {result.level && (\n                      <span className={`px-2 py-1 rounded text-xs font-medium ${getLevelColor(result.level)}`}>\n                        {result.level}\n                      </span>\n                    )}\n                    \n                    {result.timestamp && (\n                      <span className=\"text-xs text-gray-500\">\n                        {formatTimestamp(result.timestamp)}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"text-sm text-gray-900 font-mono bg-gray-50 p-3 rounded border overflow-x-auto\">\n                  {result.raw_line}\n                </div>\n\n                {result.message && result.message !== result.raw_line && (\n                  <div className=\"mt-2 text-sm text-gray-700\">\n                    <strong>Parsed Message:</strong> {result.message}\n                  </div>\n                )}\n\n                {result.fields && Object.keys(result.fields).length > 0 && (\n                  <div className=\"mt-2\">\n                    <details className=\"text-sm\">\n                      <summary className=\"cursor-pointer text-gray-600 hover:text-gray-800\">\n                        View parsed fields ({Object.keys(result.fields).length})\n                      </summary>\n                      <div className=\"mt-2 bg-gray-50 p-2 rounded text-xs font-mono\">\n                        <pre>{JSON.stringify(result.fields, null, 2)}</pre>\n                      </div>\n                    </details>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {hasMore && (\n            <div className=\"p-4 border-t text-center\">\n              <button\n                onClick={loadMore}\n                disabled={loading}\n                className=\"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors\"\n              >\n                {loading ? 'Loading...' : 'Load More Results'}\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* No Results */}\n      {!loading && results.length === 0 && (searchQuery.trim() || levelFilter) && (\n        <div className=\"bg-white rounded-lg p-8 text-center border\">\n          <div className=\"text-gray-400 text-4xl mb-4\">🔍</div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Results Found</h3>\n          <p className=\"text-gray-600\">\n            Try adjusting your search terms or filters to find matching log entries.\n          </p>\n        </div>\n      )}\n\n      {/* Search Tips */}\n      {results.length === 0 && !searchQuery.trim() && !levelFilter && (\n        <div className=\"bg-blue-50 rounded-lg p-6 border border-blue-200\">\n          <h4 className=\"text-md font-medium text-blue-900 mb-3\">Search Tips</h4>\n          <ul className=\"text-sm text-blue-800 space-y-1\">\n            <li>• Enter keywords to search in log messages and raw content</li>\n            <li>• Use the level filter to find specific types of log entries</li>\n            <li>• Search is case-insensitive and matches partial words</li>\n            <li>• Combine text search with level filters for precise results</li>\n            <li>• Results are paginated - use \"Load More\" to see additional entries</li>\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBe,SAAS,UAAU,EAAE,UAAU,EAAkB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB;IAEvB,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,SAAS,KAAK;QAChD,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,aAAa;QAEzC,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG;gBACH,OAAO,eAAe,QAAQ;gBAC9B,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,cAAc,EAAE,QAAQ;YAChD;YAEA,IAAI,aAAa;gBACf,OAAO,MAAM,CAAC,SAAS;YACzB;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,QAAQ,EAAE,QAAQ;YAC3E,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,QAAQ;gBACV,WAAW,CAAA,OAAQ;2BAAI;2BAAS,KAAK,OAAO;qBAAC;YAC/C,OAAO;gBACL,WAAW,KAAK,OAAO;YACzB;YAEA,gBAAgB,KAAK,aAAa;YAClC,WAAW,KAAK,QAAQ;YACxB,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,WAAW,GAAG;IAChB;IAEA,MAAM,WAAW;QACf,WAAW,cAAc,GAAG;IAC9B;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,eAAe;QACf,WAAW,EAAE;QACb,gBAAgB;QAChB,eAAe;QACf,WAAW;IACb;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,OAAO;QACvB,IAAI;YACF,OAAO,IAAI,KAAK,WAAW,cAAc;QAC3C,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO;YACb,KAAK;gBAAS,OAAO;YACrB,KAAK;YACL,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,WAAU;wCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0CAI5C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU,WAAY,CAAC,YAAY,IAAI,MAAM,CAAC;gCAC9C,WAAU;0CAET,UAAU,iBAAiB;;;;;;0CAG9B,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;oBAKF,eAAe,mBACd,8OAAC;wBAAI,WAAU;;4BAA6B;4BACnC,aAAa,cAAc;4BAAG;;;;;;;;;;;;;YAM1C,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAkC;wDAC1C,OAAO,WAAW;;;;;;;gDAGzB,OAAO,KAAK,kBACX,8OAAC;oDAAK,WAAW,CAAC,sCAAsC,EAAE,cAAc,OAAO,KAAK,GAAG;8DACpF,OAAO,KAAK;;;;;;gDAIhB,OAAO,SAAS,kBACf,8OAAC;oDAAK,WAAU;8DACb,gBAAgB,OAAO,SAAS;;;;;;;;;;;;;;;;;kDAMzC,8OAAC;wCAAI,WAAU;kDACZ,OAAO,QAAQ;;;;;;oCAGjB,OAAO,OAAO,IAAI,OAAO,OAAO,KAAK,OAAO,QAAQ,kBACnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAwB;4CAAE,OAAO,OAAO;;;;;;;oCAInD,OAAO,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,GAAG,mBACpD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAQ,WAAU;;8DACjB,8OAAC;oDAAQ,WAAU;;wDAAmD;wDAC/C,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM;wDAAC;;;;;;;8DAEzD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAK,KAAK,SAAS,CAAC,OAAO,MAAM,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;+BAtC1C;;;;;;;;;;oBA+Cb,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,UAAU,eAAe;;;;;;;;;;;;;;;;;YAQnC,CAAC,WAAW,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY,IAAI,MAAM,WAAW,mBACrE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;YAOhC,QAAQ,MAAM,KAAK,KAAK,CAAC,YAAY,IAAI,MAAM,CAAC,6BAC/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 4617, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/InsightsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface InsightsPanelProps {\n  data: any;\n}\n\nexport default function InsightsPanel({ data }: InsightsPanelProps) {\n  if (!data) {\n    return null;\n  }\n\n  // Extract insights from various analysis results\n  const insights: string[] = [];\n  const recommendations: string[] = [];\n\n  // Summary insights\n  const summary = data.summary?.summary;\n  if (summary) {\n    if (summary.health_status === 'critical') {\n      insights.push(`🚨 System health is CRITICAL with ${summary.error_rate}% error rate`);\n      recommendations.push('Immediate action required: Investigate and resolve critical errors');\n    } else if (summary.health_status === 'warning') {\n      insights.push(`⚠️ System health needs attention with ${summary.error_rate}% error rate`);\n      recommendations.push('Monitor error trends closely and implement error reduction strategies');\n    } else if (summary.health_status === 'healthy') {\n      insights.push(`✅ System appears healthy with low error rate (${summary.error_rate}%)`);\n    }\n\n    if (summary.total_entries > 100000) {\n      insights.push(`📊 High volume system with ${summary.total_entries.toLocaleString()} log entries`);\n    } else if (summary.total_entries < 100) {\n      insights.push(`📉 Low activity detected with only ${summary.total_entries} log entries`);\n    }\n  }\n\n  // Error analysis insights\n  const errorAnalysis = data.errorAnalysis?.error_analysis;\n  if (errorAnalysis && errorAnalysis.total_errors > 0) {\n    const topErrorType = Object.entries(errorAnalysis.error_types || {})\n      .sort(([,a], [,b]) => (b as number) - (a as number))[0];\n    if (topErrorType) {\n      insights.push(`🔍 Most common error type: ${topErrorType[0]} (${topErrorType[1]} occurrences)`);\n    }\n  }\n\n  // Performance insights\n  const perfAnalysis = data.performanceAnalysis?.performance_analysis;\n  if (perfAnalysis && perfAnalysis.total_requests > 0) {\n    const avgTime = perfAnalysis.avg_response_time;\n    const slowCount = perfAnalysis.slow_request_count;\n\n    if (avgTime > 2000) {\n      insights.push(`🐌 High average response time: ${avgTime.toFixed(0)}ms`);\n      recommendations.push('Consider performance optimization to reduce response times');\n    } else if (avgTime < 500) {\n      insights.push(`⚡ Excellent performance: Average response time is ${avgTime.toFixed(0)}ms`);\n    }\n\n    if (slowCount > 0) {\n      insights.push(`⏱️ Found ${slowCount} slow requests (>5 seconds) that need investigation`);\n      if (slowCount > perfAnalysis.total_requests * 0.05) {\n        recommendations.push('Investigate and optimize slow requests (>5% of requests are slow)');\n      }\n    }\n  }\n\n  // Customer insights\n  const customerAnalysis = data.customerAnalysis?.customer_analysis;\n  if (customerAnalysis && customerAnalysis.total_unique_customers > 0) {\n    insights.push(`👥 Serving ${customerAnalysis.total_unique_customers} unique customers`);\n\n    if (customerAnalysis.error_prone_customers?.length > 0) {\n      const topErrorCustomer = customerAnalysis.error_prone_customers[0];\n      insights.push(`🚨 Customer ${topErrorCustomer.customer_id} has ${topErrorCustomer.error_count} errors`);\n      recommendations.push('Provide additional support to customers experiencing frequent errors');\n    }\n  }\n\n  // Application insights\n  const appAnalysis = data.applicationAnalysis?.application_analysis;\n  if (appAnalysis && appAnalysis.total_applications > 1) {\n    insights.push(`📱 Monitoring ${appAnalysis.total_applications} different applications`);\n\n    const apps = appAnalysis.application_summary || [];\n    if (apps.length > 0) {\n      const highestErrorApp = apps.reduce((max: any, app: any) => \n        app.error_rate > max.error_rate ? app : max\n      );\n      if (highestErrorApp.error_rate > 5) {\n        insights.push(`⚠️ Application '${highestErrorApp.app_name}' has high error rate: ${highestErrorApp.error_rate}%`);\n        if (highestErrorApp.error_rate > 10) {\n          recommendations.push(`Investigate high error rate in application '${highestErrorApp.app_name}'`);\n        }\n      }\n    }\n  }\n\n  // Time analysis insights\n  const timeAnalysis = data.timeAnalysis?.time_analysis;\n  if (timeAnalysis) {\n    const peakHour = timeAnalysis.peak_hour;\n    if (peakHour !== null) {\n      insights.push(`📈 Peak activity occurs at ${peakHour.toString().padStart(2, '0')}:00`);\n    }\n\n    const totalDays = timeAnalysis.total_days;\n    if (totalDays > 1) {\n      insights.push(`📅 Data spans ${totalDays} days`);\n    }\n  }\n\n  // Pattern insights\n  const patternAnalysis = data.patternAnalysis?.pattern_analysis;\n  if (patternAnalysis) {\n    const commonMessages = patternAnalysis.common_messages || [];\n    if (commonMessages.length > 0) {\n      insights.push(`🔍 Found ${commonMessages.length} recurring message patterns`);\n    }\n\n    const ipAnalysis = patternAnalysis.ip_analysis;\n    if (ipAnalysis && ipAnalysis.total_unique_ips > 0) {\n      insights.push(`🌐 ${ipAnalysis.total_unique_ips} unique IP addresses detected`);\n    }\n  }\n\n  // General recommendations\n  if (summary && summary.total_entries > 10000) {\n    recommendations.push('Consider implementing log rotation and archival for large log files');\n  }\n\n  // If no specific insights, add general ones\n  if (insights.length === 0) {\n    insights.push('📊 Analysis completed successfully');\n    insights.push('🔍 Review the detailed charts and tables for more insights');\n  }\n\n  if (recommendations.length === 0) {\n    recommendations.push('Continue monitoring system health and performance');\n    recommendations.push('Set up alerts for error rate thresholds');\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      {/* Insights */}\n      <div className=\"bg-white rounded-lg p-6 border\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"text-blue-600 text-xl mr-3\">💡</div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Key Insights</h3>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {insights.slice(0, 8).map((insight, index) => (\n            <div key={index} className=\"flex items-start\">\n              <div className=\"flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3\"></div>\n              <p className=\"text-sm text-gray-700\">{insight}</p>\n            </div>\n          ))}\n        </div>\n\n        {insights.length > 8 && (\n          <div className=\"mt-4 text-sm text-gray-500\">\n            And {insights.length - 8} more insights available in detailed views...\n          </div>\n        )}\n      </div>\n\n      {/* Recommendations */}\n      <div className=\"bg-white rounded-lg p-6 border\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"text-green-600 text-xl mr-3\">🎯</div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Recommendations</h3>\n        </div>\n        \n        <div className=\"space-y-3\">\n          {recommendations.slice(0, 6).map((recommendation, index) => (\n            <div key={index} className=\"flex items-start\">\n              <div className=\"flex-shrink-0 w-2 h-2 bg-green-400 rounded-full mt-2 mr-3\"></div>\n              <p className=\"text-sm text-gray-700\">{recommendation}</p>\n            </div>\n          ))}\n        </div>\n\n        {recommendations.length > 6 && (\n          <div className=\"mt-4 text-sm text-gray-500\">\n            And {recommendations.length - 6} more recommendations...\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAQe,SAAS,cAAc,EAAE,IAAI,EAAsB;IAChE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,WAAqB,EAAE;IAC7B,MAAM,kBAA4B,EAAE;IAEpC,mBAAmB;IACnB,MAAM,UAAU,KAAK,OAAO,EAAE;IAC9B,IAAI,SAAS;QACX,IAAI,QAAQ,aAAa,KAAK,YAAY;YACxC,SAAS,IAAI,CAAC,CAAC,kCAAkC,EAAE,QAAQ,UAAU,CAAC,YAAY,CAAC;YACnF,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,QAAQ,aAAa,KAAK,WAAW;YAC9C,SAAS,IAAI,CAAC,CAAC,sCAAsC,EAAE,QAAQ,UAAU,CAAC,YAAY,CAAC;YACvF,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,QAAQ,aAAa,KAAK,WAAW;YAC9C,SAAS,IAAI,CAAC,CAAC,8CAA8C,EAAE,QAAQ,UAAU,CAAC,EAAE,CAAC;QACvF;QAEA,IAAI,QAAQ,aAAa,GAAG,QAAQ;YAClC,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,QAAQ,aAAa,CAAC,cAAc,GAAG,YAAY,CAAC;QAClG,OAAO,IAAI,QAAQ,aAAa,GAAG,KAAK;YACtC,SAAS,IAAI,CAAC,CAAC,mCAAmC,EAAE,QAAQ,aAAa,CAAC,YAAY,CAAC;QACzF;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,KAAK,aAAa,EAAE;IAC1C,IAAI,iBAAiB,cAAc,YAAY,GAAG,GAAG;QACnD,MAAM,eAAe,OAAO,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC,GAC/D,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,AAAC,IAAgB,EAAa,CAAC,EAAE;QACzD,IAAI,cAAc;YAChB,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,YAAY,CAAC,EAAE,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,aAAa,CAAC;QAChG;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAe,KAAK,mBAAmB,EAAE;IAC/C,IAAI,gBAAgB,aAAa,cAAc,GAAG,GAAG;QACnD,MAAM,UAAU,aAAa,iBAAiB;QAC9C,MAAM,YAAY,aAAa,kBAAkB;QAEjD,IAAI,UAAU,MAAM;YAClB,SAAS,IAAI,CAAC,CAAC,+BAA+B,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,CAAC;YACtE,gBAAgB,IAAI,CAAC;QACvB,OAAO,IAAI,UAAU,KAAK;YACxB,SAAS,IAAI,CAAC,CAAC,kDAAkD,EAAE,QAAQ,OAAO,CAAC,GAAG,EAAE,CAAC;QAC3F;QAEA,IAAI,YAAY,GAAG;YACjB,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,mDAAmD,CAAC;YACxF,IAAI,YAAY,aAAa,cAAc,GAAG,MAAM;gBAClD,gBAAgB,IAAI,CAAC;YACvB;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,KAAK,gBAAgB,EAAE;IAChD,IAAI,oBAAoB,iBAAiB,sBAAsB,GAAG,GAAG;QACnE,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,iBAAiB,sBAAsB,CAAC,iBAAiB,CAAC;QAEtF,IAAI,iBAAiB,qBAAqB,EAAE,SAAS,GAAG;YACtD,MAAM,mBAAmB,iBAAiB,qBAAqB,CAAC,EAAE;YAClE,SAAS,IAAI,CAAC,CAAC,YAAY,EAAE,iBAAiB,WAAW,CAAC,KAAK,EAAE,iBAAiB,WAAW,CAAC,OAAO,CAAC;YACtG,gBAAgB,IAAI,CAAC;QACvB;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,KAAK,mBAAmB,EAAE;IAC9C,IAAI,eAAe,YAAY,kBAAkB,GAAG,GAAG;QACrD,SAAS,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY,kBAAkB,CAAC,uBAAuB,CAAC;QAEtF,MAAM,OAAO,YAAY,mBAAmB,IAAI,EAAE;QAClD,IAAI,KAAK,MAAM,GAAG,GAAG;YACnB,MAAM,kBAAkB,KAAK,MAAM,CAAC,CAAC,KAAU,MAC7C,IAAI,UAAU,GAAG,IAAI,UAAU,GAAG,MAAM;YAE1C,IAAI,gBAAgB,UAAU,GAAG,GAAG;gBAClC,SAAS,IAAI,CAAC,CAAC,gBAAgB,EAAE,gBAAgB,QAAQ,CAAC,uBAAuB,EAAE,gBAAgB,UAAU,CAAC,CAAC,CAAC;gBAChH,IAAI,gBAAgB,UAAU,GAAG,IAAI;oBACnC,gBAAgB,IAAI,CAAC,CAAC,4CAA4C,EAAE,gBAAgB,QAAQ,CAAC,CAAC,CAAC;gBACjG;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,KAAK,YAAY,EAAE;IACxC,IAAI,cAAc;QAChB,MAAM,WAAW,aAAa,SAAS;QACvC,IAAI,aAAa,MAAM;YACrB,SAAS,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;QACvF;QAEA,MAAM,YAAY,aAAa,UAAU;QACzC,IAAI,YAAY,GAAG;YACjB,SAAS,IAAI,CAAC,CAAC,cAAc,EAAE,UAAU,KAAK,CAAC;QACjD;IACF;IAEA,mBAAmB;IACnB,MAAM,kBAAkB,KAAK,eAAe,EAAE;IAC9C,IAAI,iBAAiB;QACnB,MAAM,iBAAiB,gBAAgB,eAAe,IAAI,EAAE;QAC5D,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,2BAA2B,CAAC;QAC9E;QAEA,MAAM,aAAa,gBAAgB,WAAW;QAC9C,IAAI,cAAc,WAAW,gBAAgB,GAAG,GAAG;YACjD,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,WAAW,gBAAgB,CAAC,6BAA6B,CAAC;QAChF;IACF;IAEA,0BAA0B;IAC1B,IAAI,WAAW,QAAQ,aAAa,GAAG,OAAO;QAC5C,gBAAgB,IAAI,CAAC;IACvB;IAEA,4CAA4C;IAC5C,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC;IAChB;IAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,gBAAgB,IAAI,CAAC;QACrB,gBAAgB,IAAI,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAC5C,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;+BAF9B;;;;;;;;;;oBAOb,SAAS,MAAM,GAAG,mBACjB,8OAAC;wBAAI,WAAU;;4BAA6B;4BACrC,SAAS,MAAM,GAAG;4BAAE;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA8B;;;;;;0CAC7C,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,gBAAgB,sBAChD,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;+BAF9B;;;;;;;;;;oBAOb,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;wBAAI,WAAU;;4BAA6B;4BACrC,gBAAgB,MAAM,GAAG;4BAAE;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 4906, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAnalysis } from '@/context/AnalysisContext';\nimport ErrorAnalysisChart from './charts/ErrorAnalysisChart';\nimport Timeline<PERSON>hart from './charts/TimelineChart';\nimport PatternAnalysisChart from './charts/PatternAnalysisChart';\nimport PerformanceChart from './charts/PerformanceChart';\nimport CustomerChart from './charts/CustomerChart';\nimport ApplicationChart from './charts/ApplicationChart';\nimport LogSearch from './LogSearch';\nimport InsightsPanel from './InsightsPanel';\n\ninterface DashboardProps {\n  analysisId: string;\n}\n\nexport default function Dashboard({ analysisId }: DashboardProps) {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [analysisData, setAnalysisData] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const { setAnalysisData: setContextAnalysisData } = useAnalysis();\n\n  useEffect(() => {\n    loadAnalysisData();\n  }, [analysisId]);\n\n  const loadAnalysisData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const [summary, errorAnalysis, timeAnalysis, patternAnalysis] = await Promise.all([\n        fetch(`/api/analysis/${analysisId}/summary`).then(res => res.json()),\n        fetch(`/api/analysis/${analysisId}/errors`).then(res => res.json()),\n        fetch(`/api/analysis/${analysisId}/timeline`).then(res => res.json()),\n        fetch(`/api/analysis/${analysisId}/patterns`).then(res => res.json()),\n      ]);\n\n      // Try to get enterprise-specific analysis data\n      let performanceAnalysis = {};\n      let customerAnalysis = {};\n      let applicationAnalysis = {};\n\n      try {\n        const response = await fetch(`/api/analysis/${analysisId}/performance`);\n        if (response.ok) {\n          performanceAnalysis = await response.json();\n        }\n      } catch (e) {\n        console.log('Performance analysis not available');\n      }\n\n      try {\n        const response = await fetch(`/api/analysis/${analysisId}/customers`);\n        if (response.ok) {\n          customerAnalysis = await response.json();\n        }\n      } catch (e) {\n        console.log('Customer analysis not available');\n      }\n\n      try {\n        const response = await fetch(`/api/analysis/${analysisId}/applications`);\n        if (response.ok) {\n          applicationAnalysis = await response.json();\n        }\n      } catch (e) {\n        console.log('Application analysis not available');\n      }\n\n      const combinedData = {\n        summary,\n        errorAnalysis,\n        timeAnalysis,\n        patternAnalysis,\n        performanceAnalysis,\n        customerAnalysis,\n        applicationAnalysis,\n      };\n\n      setAnalysisData(combinedData);\n      setContextAnalysisData(combinedData);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load analysis data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: '📊' },\n    { id: 'errors', label: 'Error Analysis', icon: '🚨' },\n    { id: 'performance', label: 'Performance', icon: '⚡' },\n    { id: 'customers', label: 'Customers', icon: '👥' },\n    { id: 'applications', label: 'Applications', icon: '📱' },\n    { id: 'patterns', label: 'Patterns', icon: '🔍' },\n    { id: 'timeline', label: 'Timeline', icon: '📈' },\n    { id: 'search', label: 'Search Logs', icon: '🔎' },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-96\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading analysis data...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n        <div className=\"flex\">\n          <div className=\"text-red-400\">⚠️</div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-red-800\">Error Loading Analysis</h3>\n            <p className=\"text-sm text-red-700 mt-1\">{error}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with summary */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Analysis Results</h2>\n          <div className=\"flex items-center space-x-4\">\n            <span className=\"text-sm text-gray-500\">Analysis ID: {analysisId}</span>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\"\n            >\n              Refresh\n            </button>\n          </div>\n        </div>\n\n        {analysisData?.summary && (\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div className=\"bg-blue-50 rounded-lg p-4\">\n              <div className=\"text-blue-600 text-sm font-medium\">Total Entries</div>\n              <div className=\"text-blue-900 text-2xl font-bold\">\n                {analysisData.summary.summary?.total_entries?.toLocaleString() || 0}\n              </div>\n            </div>\n            \n            <div className=\"bg-red-50 rounded-lg p-4\">\n              <div className=\"text-red-600 text-sm font-medium\">Error Rate</div>\n              <div className=\"text-red-900 text-2xl font-bold\">\n                {analysisData.summary.summary?.error_rate?.toFixed(1) || 0}%\n              </div>\n            </div>\n            \n            <div className=\"bg-green-50 rounded-lg p-4\">\n              <div className=\"text-green-600 text-sm font-medium\">Health Status</div>\n              <div className=\"text-green-900 text-lg font-bold capitalize\">\n                {analysisData.summary.summary?.health_status || 'Unknown'}\n              </div>\n            </div>\n            \n            <div className=\"bg-purple-50 rounded-lg p-4\">\n              <div className=\"text-purple-600 text-sm font-medium\">Parse Success</div>\n              <div className=\"text-purple-900 text-2xl font-bold\">\n                {analysisData.summary.summary?.parse_success_rate?.toFixed(1) || 0}%\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Insights Panel */}\n      <InsightsPanel data={analysisData} />\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white rounded-lg shadow-sm\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <span className=\"mr-2\">{tab.icon}</span>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-8\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                <ErrorAnalysisChart data={analysisData.errorAnalysis} />\n                <PatternAnalysisChart data={analysisData.patternAnalysis} />\n              </div>\n              {analysisData.performanceAnalysis?.performance_analysis && (\n                <PerformanceChart data={analysisData.performanceAnalysis} />\n              )}\n              <TimelineChart data={analysisData.timeAnalysis} />\n            </div>\n          )}\n\n          {activeTab === 'errors' && (\n            <ErrorAnalysisChart data={analysisData.errorAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'performance' && (\n            <PerformanceChart data={analysisData.performanceAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'customers' && (\n            <CustomerChart data={analysisData.customerAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'applications' && (\n            <ApplicationChart data={analysisData.applicationAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'patterns' && (\n            <PatternAnalysisChart data={analysisData.patternAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'timeline' && (\n            <TimelineChart data={analysisData.timeAnalysis} detailed={true} />\n          )}\n\n          {activeTab === 'search' && (\n            <LogSearch analysisId={analysisId} />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAiBe,SAAS,UAAU,EAAE,UAAU,EAAkB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,iBAAiB,sBAAsB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,CAAC,SAAS,eAAe,cAAc,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChF,MAAM,CAAC,cAAc,EAAE,WAAW,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;gBACjE,MAAM,CAAC,cAAc,EAAE,WAAW,OAAO,CAAC,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;gBAChE,MAAM,CAAC,cAAc,EAAE,WAAW,SAAS,CAAC,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;gBAClE,MAAM,CAAC,cAAc,EAAE,WAAW,SAAS,CAAC,EAAE,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI;aACnE;YAED,+CAA+C;YAC/C,IAAI,sBAAsB,CAAC;YAC3B,IAAI,mBAAmB,CAAC;YACxB,IAAI,sBAAsB,CAAC;YAE3B,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,YAAY,CAAC;gBACtE,IAAI,SAAS,EAAE,EAAE;oBACf,sBAAsB,MAAM,SAAS,IAAI;gBAC3C;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,UAAU,CAAC;gBACpE,IAAI,SAAS,EAAE,EAAE;oBACf,mBAAmB,MAAM,SAAS,IAAI;gBACxC;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,aAAa,CAAC;gBACvE,IAAI,SAAS,EAAE,EAAE;oBACf,sBAAsB,MAAM,SAAS,IAAI;gBAC3C;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,gBAAgB;YAChB,uBAAuB;QACzB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAK;QAChD;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM;QAAK;QACpD;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM;QAAI;QACrD;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM;QAAK;QAClD;YAAE,IAAI;YAAgB,OAAO;YAAgB,MAAM;QAAK;QACxD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAK;QAChD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAK;QAChD;YAAE,IAAI;YAAU,OAAO;YAAe,MAAM;QAAK;KAClD;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAe;;;;;;kCAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAc;;;;;;;kDACtD,8OAAC;wCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAMJ,cAAc,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,OAAO,CAAC,OAAO,EAAE,eAAe,oBAAoB;;;;;;;;;;;;0CAItE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,OAAO,CAAC,OAAO,EAAE,YAAY,QAAQ,MAAM;4CAAE;;;;;;;;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDACZ,aAAa,OAAO,CAAC,OAAO,EAAE,iBAAiB;;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,OAAO,CAAC,OAAO,EAAE,oBAAoB,QAAQ,MAAM;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7E,8OAAC,mIAAA,CAAA,UAAa;gBAAC,MAAM;;;;;;0BAGrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;sDAEF,8OAAC;4CAAK,WAAU;sDAAQ,IAAI,IAAI;;;;;;wCAC/B,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;;;;;;;;;;;kCAgBnB,8OAAC;wBAAI,WAAU;;4BACZ,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kJAAA,CAAA,UAAkB;gDAAC,MAAM,aAAa,aAAa;;;;;;0DACpD,8OAAC,oJAAA,CAAA,UAAoB;gDAAC,MAAM,aAAa,eAAe;;;;;;;;;;;;oCAEzD,aAAa,mBAAmB,EAAE,sCACjC,8OAAC,gJAAA,CAAA,UAAgB;wCAAC,MAAM,aAAa,mBAAmB;;;;;;kDAE1D,8OAAC,6IAAA,CAAA,UAAa;wCAAC,MAAM,aAAa,YAAY;;;;;;;;;;;;4BAIjD,cAAc,0BACb,8OAAC,kJAAA,CAAA,UAAkB;gCAAC,MAAM,aAAa,aAAa;gCAAE,UAAU;;;;;;4BAGjE,cAAc,+BACb,8OAAC,gJAAA,CAAA,UAAgB;gCAAC,MAAM,aAAa,mBAAmB;gCAAE,UAAU;;;;;;4BAGrE,cAAc,6BACb,8OAAC,6IAAA,CAAA,UAAa;gCAAC,MAAM,aAAa,gBAAgB;gCAAE,UAAU;;;;;;4BAG/D,cAAc,gCACb,8OAAC,gJAAA,CAAA,UAAgB;gCAAC,MAAM,aAAa,mBAAmB;gCAAE,UAAU;;;;;;4BAGrE,cAAc,4BACb,8OAAC,oJAAA,CAAA,UAAoB;gCAAC,MAAM,aAAa,eAAe;gCAAE,UAAU;;;;;;4BAGrE,cAAc,4BACb,8OAAC,6IAAA,CAAA,UAAa;gCAAC,MAAM,aAAa,YAAY;gCAAE,UAAU;;;;;;4BAG3D,cAAc,0BACb,8OAAC,+HAAA,CAAA,UAAS;gCAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 5469, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport FileUpload from '@/components/FileUpload';\nimport Dashboard from '@/components/Dashboard';\n\nexport default function Home() {\n  const [currentAnalysis, setCurrentAnalysis] = useState<string | null>(null);\n\n  return (\n    <>\n      {currentAnalysis ? (\n        <Dashboard analysisId={currentAnalysis} />\n      ) : (\n        <FileUpload onAnalysisComplete={setCurrentAnalysis} />\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,qBACE;kBACG,gCACC,8OAAC,+HAAA,CAAA,UAAS;YAAC,YAAY;;;;;iCAEvB,8OAAC,gIAAA,CAAA,UAAU;YAAC,oBAAoB;;;;;;;AAIxC", "debugId": null}}]}