{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/context/AnalysisContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\ninterface AnalysisContextType {\n  currentAnalysis: string | null;\n  setCurrentAnalysis: (id: string | null) => void;\n  analysisData: any;\n  setAnalysisData: (data: any) => void;\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n}\n\nconst AnalysisContext = createContext<AnalysisContextType | undefined>(undefined);\n\nexport function AnalysisProvider({ children }: { children: ReactNode }) {\n  const [currentAnalysis, setCurrentAnalysis] = useState<string | null>(null);\n  const [analysisData, setAnalysisData] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  return (\n    <AnalysisContext.Provider\n      value={{\n        currentAnalysis,\n        setCurrentAnalysis,\n        analysisData,\n        setAnalysisData,\n        isLoading,\n        setIsLoading,\n      }}\n    >\n      {children}\n    </AnalysisContext.Provider>\n  );\n}\n\nexport function useAnalysis() {\n  const context = useContext(AnalysisContext);\n  if (context === undefined) {\n    throw new Error('useAnalysis must be used within an AnalysisProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useAnalysis } from '@/context/AnalysisContext';\n\nexport default function Header() {\n  const { currentAnalysis, setCurrentAnalysis } = useAnalysis();\n\n  const handleNewAnalysis = () => {\n    setCurrentAnalysis(null);\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              🔍 Log Analyzer\n            </h1>\n            <span className=\"text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n              Enterprise Edition\n            </span>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            {currentAnalysis && (\n              <button\n                onClick={handleNewAnalysis}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                New Analysis\n              </button>\n            )}\n            \n            <div className=\"text-sm text-gray-500\">\n              <span className=\"inline-flex items-center\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                Backend Connected\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE1D,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC;gCAAK,WAAU;0CAAsD;;;;;;;;;;;;kCAKxE,8OAAC;wBAAI,WAAU;;4BACZ,iCACC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;;;;;wCAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShF", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sher<PERSON>/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}