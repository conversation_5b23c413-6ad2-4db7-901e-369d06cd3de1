{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Sherwyn/Project%20Repos/AI%20Projects%20/log_analyzer/frontend-nextjs/src/app/api/%5B...path%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5000';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  const { path: pathArray } = await params;\n  const path = pathArray.join('/');\n  const searchParams = request.nextUrl.searchParams;\n  \n  const url = new URL(`/api/${path}`, BACKEND_URL);\n  searchParams.forEach((value, key) => {\n    url.searchParams.append(key, value);\n  });\n\n  try {\n    const response = await fetch(url.toString(), {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const data = await response.json();\n    return NextResponse.json(data, { status: response.status });\n  } catch (error) {\n    console.error('API proxy error:', error);\n    return NextResponse.json(\n      { error: 'Backend service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  const { path: pathArray } = await params;\n  const path = pathArray.join('/');\n  const url = new URL(`/api/${path}`, BACKEND_URL);\n\n  try {\n    const contentType = request.headers.get('content-type') || '';\n    \n    let body;\n    if (contentType.includes('multipart/form-data')) {\n      // For file uploads, pass the FormData directly\n      body = await request.formData();\n    } else {\n      // For JSON requests\n      body = await request.text();\n    }\n\n    const response = await fetch(url.toString(), {\n      method: 'POST',\n      headers: contentType.includes('multipart/form-data') \n        ? {} // Let fetch set the boundary for FormData\n        : {\n            'Content-Type': contentType,\n          },\n      body,\n    });\n\n    const data = await response.json();\n    return NextResponse.json(data, { status: response.status });\n  } catch (error) {\n    console.error('API proxy error:', error);\n    return NextResponse.json(\n      { error: 'Backend service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  const { path: pathArray } = await params;\n  const path = pathArray.join('/');\n  const url = new URL(`/api/${path}`, BACKEND_URL);\n\n  try {\n    const body = await request.text();\n    const response = await fetch(url.toString(), {\n      method: 'PUT',\n      headers: {\n        'Content-Type': request.headers.get('content-type') || 'application/json',\n      },\n      body,\n    });\n\n    const data = await response.json();\n    return NextResponse.json(data, { status: response.status });\n  } catch (error) {\n    console.error('API proxy error:', error);\n    return NextResponse.json(\n      { error: 'Backend service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ path: string[] }> }\n) {\n  const { path: pathArray } = await params;\n  const path = pathArray.join('/');\n  const url = new URL(`/api/${path}`, BACKEND_URL);\n\n  try {\n    const response = await fetch(url.toString(), {\n      method: 'DELETE',\n    });\n\n    const data = await response.json();\n    return NextResponse.json(data, { status: response.status });\n  } catch (error) {\n    console.error('API proxy error:', error);\n    return NextResponse.json(\n      { error: 'Backend service unavailable' },\n      { status: 503 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAExC,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAClC,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;IAEjD,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;IACpC,aAAa,OAAO,CAAC,CAAC,OAAO;QAC3B,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK;IAC/B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAClC,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;IAEpC,IAAI;QACF,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAE3D,IAAI;QACJ,IAAI,YAAY,QAAQ,CAAC,wBAAwB;YAC/C,+CAA+C;YAC/C,OAAO,MAAM,QAAQ,QAAQ;QAC/B,OAAO;YACL,oBAAoB;YACpB,OAAO,MAAM,QAAQ,IAAI;QAC3B;QAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C,QAAQ;YACR,SAAS,YAAY,QAAQ,CAAC,yBAC1B,CAAC,EAAE,0CAA0C;eAC7C;gBACE,gBAAgB;YAClB;YACJ;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAClC,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;IAEpC,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACzD;YACA;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA2C;IAEnD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM;IAClC,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,MAAM,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE;IAEpC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C,QAAQ;QACV;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ,SAAS,MAAM;QAAC;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}