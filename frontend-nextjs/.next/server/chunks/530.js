exports.id=530,exports.ids=[530],exports.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1891:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(687),a=t(7856);function l(){let{currentAnalysis:e,setCurrentAnalysis:s}=(0,a.S)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"\uD83D\uDD0D Log Analyzer"}),(0,r.jsx)("span",{className:"text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded",children:"Enterprise Edition"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,r.jsx)("button",{onClick:()=>{s(null)},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"New Analysis"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:(0,r.jsxs)("span",{className:"inline-flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full mr-2"}),"Backend Connected"]})})]})]})})})}},3917:(e,s,t)=>{Promise.resolve().then(t.bind(t,4597)),Promise.resolve().then(t.bind(t,7266))},4396:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4431:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var r=t(7413),a=t(1001),l=t.n(a);t(1135);var i=t(7266),n=t(4597);let d={title:"Log Analyzer - Enterprise Log Analysis Tool",description:"Advanced log analysis tool with enterprise structured log parsing capabilities",keywords:["log analysis","enterprise logs","monitoring","analytics"]};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${l().variable} font-sans antialiased`,children:(0,r.jsx)(i.AnalysisProvider,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"container mx-auto px-4 py-8",children:e})]})})})})}},4597:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/components/Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/components/Header.tsx","default")},6111:(e,s,t)=>{"use strict";t.d(s,{A:()=>g});var r=t(687),a=t(3210),l=t(7856),i=t(3324),n=t(9947);function d({data:e,detailed:s=!1}){if(!e||!e.error_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Error Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No error data available"})]});let t=e.error_analysis,a=t.error_types||{},l={labels:Object.keys(a),datasets:[{data:Object.values(a),backgroundColor:["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9"],borderWidth:1}]},i=t.error_trends||[],d={labels:i.map(e=>new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})),datasets:[{label:"Error Count",data:i.map(e=>e.error_count),backgroundColor:"rgba(239, 68, 68, 0.6)",borderColor:"rgba(239, 68, 68, 1)",borderWidth:2}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Error Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[t.total_errors," total errors"]})]}),(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"text-red-400 text-xl mr-3",children:"\uD83D\uDEA8"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"text-red-800 font-medium",children:[t.total_errors," errors detected"]}),(0,r.jsxs)("div",{className:"text-red-600 text-sm",children:[Object.keys(a).length," different error types identified"]})]})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[Object.keys(a).length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(n.nu,{data:l,options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Error Types Distribution"}}}})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Error Breakdown"}),Object.entries(a).sort(([,e],[,s])=>s-e).map(([e,s])=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,r.jsx)("span",{className:"text-sm font-medium capitalize",children:e.replace("_"," ")}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[s," errors"]})]},e))]})]}),s&&i.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:d,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Error Trends Over Time"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&t.top_error_messages&&t.top_error_messages.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Most Common Error Messages"}),(0,r.jsx)("div",{className:"space-y-2",children:t.top_error_messages.slice(0,10).map((e,s)=>(0,r.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-red-600",children:[e.count," occurrences"]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["#",s+1]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-700 font-mono bg-white p-2 rounded border",children:e.message.length>200?e.message.substring(0,200)+"...":e.message})]},s))})]})]})]})}function c({data:e,detailed:s=!1}){if(!e||!e.time_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Timeline Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No timeline data available"})]});let t=e.time_analysis,a=t.hourly_distribution||{},l=Array.from({length:24},(e,s)=>s),i=l.map(e=>a[e]||0),d={labels:l.map(e=>`${e.toString().padStart(2,"0")}:00`),datasets:[{label:"Log Entries",data:i,backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},c=t.daily_distribution||{},o=Object.keys(c).sort(),m=o.map(e=>c[e]),x={labels:o.map(e=>new Date(e).toLocaleDateString()),datasets:[{label:"Daily Activity",data:m,backgroundColor:"rgba(16, 185, 129, 0.6)",borderColor:"rgba(16, 185, 129, 1)",borderWidth:2,fill:!1}]},h=t.peak_hour,p=t.total_days||0,u=t.avg_entries_per_hour||0;return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Timeline Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[p," days analyzed"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Peak Hour"}),(0,r.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:null!==h?`${h.toString().padStart(2,"0")}:00`:"N/A"}),(0,r.jsx)("div",{className:"text-blue-600 text-xs",children:"Highest activity period"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Avg/Hour"}),(0,r.jsx)("div",{className:"text-green-900 text-lg font-bold",children:Math.round(u).toLocaleString()}),(0,r.jsx)("div",{className:"text-green-600 text-xs",children:"Average entries per hour"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Time Span"}),(0,r.jsxs)("div",{className:"text-purple-900 text-lg font-bold",children:[p," ",1===p?"day":"days"]}),(0,r.jsx)("div",{className:"text-purple-600 text-xs",children:"Analysis period"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:d,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Activity by Hour of Day"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&o.length>1&&(0,r.jsx)("div",{children:(0,r.jsx)(n.N1,{data:x,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Activity Over Time"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Activity Insights"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[null!==h&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDCC8"}),(0,r.jsxs)("span",{children:["Peak activity occurs at ",h.toString().padStart(2,"0"),":00"]})]}),u>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-green-600 mr-2",children:"⚡"}),(0,r.jsxs)("span",{children:["Average of ",Math.round(u)," entries per hour"]})]}),p>1&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDCC5"}),(0,r.jsxs)("span",{children:["Data spans ",p," days"]})]}),u>1e3&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83D\uDD25"}),(0,r.jsx)("span",{children:"High-volume system with significant activity"})]}),u<10&&u>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-gray-600 mr-2",children:"\uD83D\uDD0D"}),(0,r.jsx)("span",{children:"Low-volume system with minimal activity"})]})]})]})]})]})}function o({data:e,detailed:s=!1}){if(!e||!e.pattern_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Pattern Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No pattern data available"})]});let t=e.pattern_analysis,a=t.common_messages||[],l={labels:a.slice(0,10).map((e,s)=>`Message ${s+1}`),datasets:[{label:"Occurrences",data:a.slice(0,10).map(e=>e.count),backgroundColor:"rgba(139, 92, 246, 0.6)",borderColor:"rgba(139, 92, 246, 1)",borderWidth:1}]},i=t.ip_analysis||{},d=i.top_ips||[],c=t.status_patterns||{},o=c.status_distribution||{},m=c.status_categories||{};return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Pattern Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[a.length," patterns identified"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Common Messages"}),(0,r.jsx)("div",{className:"text-purple-900 text-lg font-bold",children:a.length}),(0,r.jsx)("div",{className:"text-purple-600 text-xs",children:"Recurring patterns found"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Unique IPs"}),(0,r.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:i.total_unique_ips||0}),(0,r.jsx)("div",{className:"text-blue-600 text-xs",children:"Different IP addresses"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Status Codes"}),(0,r.jsx)("div",{className:"text-green-900 text-lg font-bold",children:Object.keys(o).length}),(0,r.jsx)("div",{className:"text-green-600 text-xs",children:"Different response codes"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[a.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:l,options:{responsive:!0,indexAxis:"y",plugins:{legend:{display:!1},title:{display:!0,text:"Most Common Log Messages"},tooltip:{callbacks:{afterLabel:function(e){let s=e.dataIndex,t=a[s]?.message||"";return t.length>100?t.substring(0,100)+"...":t}}}},scales:{x:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[d.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Top IP Addresses"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"IP Address"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Requests"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.slice(0,10).map((e,s)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.ip}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.count.toLocaleString()})]},s))})]})})]}),Object.keys(m).length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"HTTP Status Categories"}),(0,r.jsx)("div",{className:"space-y-2",children:Object.entries(m).map(([e,s])=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:`w-3 h-3 rounded-full mr-3 ${"2xx"===e?"bg-green-400":"3xx"===e?"bg-blue-400":"4xx"===e?"bg-yellow-400":"5xx"===e?"bg-red-400":"bg-gray-400"}`}),(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e," Status Codes"]})]}),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[s," requests"]})]},e))})]})]}),s&&a.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Message Pattern Details"}),(0,r.jsx)("div",{className:"space-y-3",children:a.slice(0,5).map((e,s)=>(0,r.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-purple-600",children:["Pattern #",s+1]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"font-medium",children:e.count})," occurrences",(0,r.jsxs)("span",{className:"ml-2",children:["(",e.percentage,"%)"]})]})]}),(0,r.jsx)("div",{className:"text-sm text-gray-700 font-mono bg-white p-3 rounded border",children:e.message})]},s))})]}),s&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Pattern Insights"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[a.length>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDD0D"}),(0,r.jsxs)("span",{children:["Found ",a.length," recurring message patterns"]})]}),i.total_unique_ips>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83C\uDF10"}),(0,r.jsxs)("span",{children:[i.total_unique_ips," unique IP addresses detected"]})]}),Object.keys(o).length>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCCA"}),(0,r.jsxs)("span",{children:[Object.keys(o).length," different HTTP status codes"]})]}),m["2xx"]&&m["4xx"]&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-yellow-600 mr-2",children:"✅"}),(0,r.jsxs)("span",{children:["Success rate: ",(m["2xx"]/(m["2xx"]+m["4xx"]+(m["5xx"]||0))*100).toFixed(1),"%"]})]})]})]})]})]})}function m({data:e,detailed:s=!1}){if(!e||!e.performance_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Performance Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No performance data available (requires enterprise structured logs)"})]});let t=e.performance_analysis,a=t.performance_by_app||{},l=Object.keys(a),i=l.map(e=>a[e].avg_response_time),d=t.performance_by_module||{},c=Object.keys(d),o=c.map(e=>d[e].avg_response_time),m=t.total_requests||0,x=t.avg_response_time||0,h=t.p95_response_time||0,p=t.p99_response_time||0,u=t.slow_request_count||0,g=t.slow_requests||[];return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Performance Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[m.toLocaleString()," requests analyzed"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Avg Response"}),(0,r.jsxs)("div",{className:"text-green-900 text-lg font-bold",children:[x.toFixed(0),"ms"]}),(0,r.jsx)("div",{className:"text-green-600 text-xs",children:"Average response time"})]}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"95th Percentile"}),(0,r.jsxs)("div",{className:"text-blue-900 text-lg font-bold",children:[h.toFixed(0),"ms"]}),(0,r.jsx)("div",{className:"text-blue-600 text-xs",children:"95% of requests faster than"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"99th Percentile"}),(0,r.jsxs)("div",{className:"text-purple-900 text-lg font-bold",children:[p.toFixed(0),"ms"]}),(0,r.jsx)("div",{className:"text-purple-600 text-xs",children:"99% of requests faster than"})]}),(0,r.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Slow Requests"}),(0,r.jsx)("div",{className:"text-red-900 text-lg font-bold",children:u}),(0,r.jsx)("div",{className:"text-red-600 text-xs",children:"Requests > 5 seconds"})]})]}),(0,r.jsx)("div",{className:"mb-6 p-4 rounded-lg",children:x<500?(0,r.jsx)("div",{className:"bg-green-50 border border-green-200",children:(0,r.jsxs)("div",{className:"flex items-center p-4",children:[(0,r.jsx)("div",{className:"text-green-400 text-xl mr-3",children:"⚡"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-green-800 font-medium",children:"Excellent Performance"}),(0,r.jsx)("div",{className:"text-green-600 text-sm",children:"Average response time is under 500ms"})]})]})}):x<2e3?(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200",children:(0,r.jsxs)("div",{className:"flex items-center p-4",children:[(0,r.jsx)("div",{className:"text-yellow-400 text-xl mr-3",children:"⚠️"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-yellow-800 font-medium",children:"Good Performance"}),(0,r.jsx)("div",{className:"text-yellow-600 text-sm",children:"Average response time is acceptable but could be optimized"})]})]})}):(0,r.jsx)("div",{className:"bg-red-50 border border-red-200",children:(0,r.jsxs)("div",{className:"flex items-center p-4",children:[(0,r.jsx)("div",{className:"text-red-400 text-xl mr-3",children:"\uD83D\uDC0C"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-red-800 font-medium",children:"Performance Issues Detected"}),(0,r.jsx)("div",{className:"text-red-600 text-sm",children:"High response times may impact user experience"})]})]})})}),(0,r.jsxs)("div",{className:"space-y-6",children:[l.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:{labels:l,datasets:[{label:"Avg Response Time (ms)",data:i,backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Application"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})}),s&&c.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:{labels:c,datasets:[{label:"Avg Response Time (ms)",data:o,backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Module"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})}),s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[l.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Performance"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Application"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Time"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Requests"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Slow"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.map(e=>{let s=a[e];return(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm font-medium text-gray-900",children:e}),(0,r.jsxs)("td",{className:"px-4 py-2 text-sm text-gray-900",children:[s.avg_response_time.toFixed(0),"ms"]}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:s.request_count.toLocaleString()}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:s.slow_request_count})]},e)})})]})})]}),g.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Slowest Requests"}),(0,r.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:g.slice(0,10).map((e,s)=>(0,r.jsxs)("div",{className:"p-3 bg-red-50 rounded border",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-red-600",children:[e.response_time.toFixed(0),"ms"]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.app_name})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-600",children:[e.url&&(0,r.jsx)("div",{className:"font-mono",children:e.url}),e.module&&(0,r.jsxs)("div",{children:["Module: ",e.module]}),e.request_id&&(0,r.jsxs)("div",{children:["ID: ",e.request_id]})]})]},s))})]})]})]})]})}function x({data:e,detailed:s=!1}){if(!e||!e.customer_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Customer Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No customer data available (requires enterprise structured logs)"})]});let t=e.customer_analysis,a=t.top_customers||[],l=t.error_prone_customers||[],i=t.total_unique_customers||0,d={labels:a.slice(0,10).map(e=>e.customer_id.length>10?e.customer_id.substring(0,10)+"...":e.customer_id),datasets:[{label:"Activity Count",data:a.slice(0,10).map(e=>e.activity_count),backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]},c={labels:l.slice(0,10).map(e=>e.customer_id.length>10?e.customer_id.substring(0,10)+"...":e.customer_id),datasets:[{label:"Error Count",data:l.slice(0,10).map(e=>e.error_count),backgroundColor:"rgba(239, 68, 68, 0.6)",borderColor:"rgba(239, 68, 68, 1)",borderWidth:1}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Customer Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[i," unique customers"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Customers"}),(0,r.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:i.toLocaleString()}),(0,r.jsx)("div",{className:"text-blue-600 text-xs",children:"Unique customer IDs"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Active Customers"}),(0,r.jsx)("div",{className:"text-green-900 text-lg font-bold",children:a.length}),(0,r.jsx)("div",{className:"text-green-600 text-xs",children:"Customers with activity"})]}),(0,r.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Error-Prone"}),(0,r.jsx)("div",{className:"text-red-900 text-lg font-bold",children:l.length}),(0,r.jsx)("div",{className:"text-red-600 text-xs",children:"Customers with errors"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[a.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:d,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Top 10 Most Active Customers"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&l.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:c,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Customers with Most Errors"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[a.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Most Active Customers"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Customer ID"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Activity"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Errors"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Apps"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.slice(0,10).map((e,s)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.customer_id.length>15?e.customer_id.substring(0,15)+"...":e.customer_id}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.activity_count.toLocaleString()}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.error_count}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.apps_used.length})]},s))})]})})]}),l.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Customers with Errors"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Customer ID"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Error Count"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:l.slice(0,10).map((e,s)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm font-mono text-gray-900",children:e.customer_id.length>20?e.customer_id.substring(0,20)+"...":e.customer_id}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-red-600 font-medium",children:e.error_count})]},s))})]})})]})]}),s&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Customer Insights"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[i>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDC65"}),(0,r.jsxs)("span",{children:["Serving ",i," unique customers"]})]}),a.length>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCC8"}),(0,r.jsxs)("span",{children:["Most active customer: ",a[0]?.customer_id,"(",a[0]?.activity_count," activities)"]})]}),l.length>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-red-600 mr-2",children:"⚠️"}),(0,r.jsxs)("span",{children:[l.length," customers experiencing errors"]})]}),a.some(e=>e.apps_used.length>1)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDCF1"}),(0,r.jsx)("span",{children:"Some customers use multiple applications"})]}),a.some(e=>e.platforms_used&&e.platforms_used.length>1)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83C\uDF10"}),(0,r.jsx)("span",{children:"Multi-platform customer access detected"})]})]})]})]})]})}function h({data:e,detailed:s=!1}){if(!e||!e.application_analysis)return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Application Analysis"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No application data available (requires enterprise structured logs)"})]});let t=e.application_analysis,a=t.application_summary||[],l=t.total_applications||0,i={labels:a.slice(0,10).map(e=>e.app_name),datasets:[{label:"Activity Count",data:a.slice(0,10).map(e=>e.activity_count),backgroundColor:"rgba(59, 130, 246, 0.6)",borderColor:"rgba(59, 130, 246, 1)",borderWidth:1}]},d={labels:a.map(e=>e.app_name),datasets:[{data:a.map(e=>e.error_rate),backgroundColor:["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9"],borderWidth:1}]},c={labels:a.filter(e=>e.avg_response_time>0).map(e=>e.app_name),datasets:[{label:"Avg Response Time (ms)",data:a.filter(e=>e.avg_response_time>0).map(e=>e.avg_response_time),backgroundColor:"rgba(34, 197, 94, 0.6)",borderColor:"rgba(34, 197, 94, 1)",borderWidth:1}]};return(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Application Analysis"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[l," applications monitored"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Apps"}),(0,r.jsx)("div",{className:"text-blue-900 text-lg font-bold",children:l}),(0,r.jsx)("div",{className:"text-blue-600 text-xs",children:"Applications monitored"})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Most Active"}),(0,r.jsx)("div",{className:"text-green-900 text-lg font-bold",children:a[0]?.app_name||"N/A"}),(0,r.jsx)("div",{className:"text-green-600 text-xs",children:"Highest activity app"})]}),(0,r.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Highest Error Rate"}),(0,r.jsxs)("div",{className:"text-red-900 text-lg font-bold",children:[Math.max(...a.map(e=>e.error_rate)).toFixed(1),"%"]}),(0,r.jsx)("div",{className:"text-red-600 text-xs",children:"Maximum error rate"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Avg Modules"}),(0,r.jsx)("div",{className:"text-purple-900 text-lg font-bold",children:a.length>0?Math.round(a.reduce((e,s)=>e+s.module_count,0)/a.length):0}),(0,r.jsx)("div",{className:"text-purple-600 text-xs",children:"Modules per application"})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[a.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:i,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Application Activity"}},scales:{y:{beginAtZero:!0,ticks:{precision:0}}}}})}),s&&a.length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(n.nu,{data:d,options:{responsive:!0,plugins:{legend:{position:"bottom"},title:{display:!0,text:"Error Rate by Application"},tooltip:{callbacks:{label:function(e){return`${e.label}: ${e.parsed}% error rate`}}}}}})}),a.some(e=>e.avg_response_time>0)&&(0,r.jsx)("div",{children:(0,r.jsx)(n.yP,{data:c,options:{responsive:!0,plugins:{legend:{display:!1},title:{display:!0,text:"Average Response Time by Application"}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Response Time (ms)"}}}}})})]}),s&&a.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Details"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Application"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Activity"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Errors"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Error Rate"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Avg Response"}),(0,r.jsx)("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Modules"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((e,s)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-4 py-2 text-sm font-medium text-gray-900",children:e.app_name}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.activity_count.toLocaleString()}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.error_count}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,r.jsxs)("span",{className:`font-medium ${e.error_rate>10?"text-red-600":e.error_rate>5?"text-yellow-600":"text-green-600"}`,children:[e.error_rate.toFixed(1),"%"]})}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.avg_response_time>0?`${e.avg_response_time.toFixed(0)}ms`:"N/A"}),(0,r.jsx)("td",{className:"px-4 py-2 text-sm text-gray-900",children:e.module_count})]},s))})]})})]}),s&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900 mb-3",children:"Application Insights"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[l>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-blue-600 mr-2",children:"\uD83D\uDCF1"}),(0,r.jsxs)("span",{children:["Monitoring ",l," applications"]})]}),a.length>0&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-green-600 mr-2",children:"\uD83D\uDCC8"}),(0,r.jsxs)("span",{children:["Most active: ",a[0]?.app_name,"(",a[0]?.activity_count.toLocaleString()," activities)"]})]}),a.some(e=>e.error_rate>10)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-red-600 mr-2",children:"⚠️"}),(0,r.jsxs)("span",{children:[a.filter(e=>e.error_rate>10).length," ","applications have high error rates (>10%)"]})]}),a.some(e=>e.avg_response_time>2e3)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-600 mr-2",children:"\uD83D\uDC0C"}),(0,r.jsx)("span",{children:"Some applications have slow response times (>2s)"})]}),a.some(e=>e.module_count>5)&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-purple-600 mr-2",children:"\uD83D\uDD27"}),(0,r.jsx)("span",{children:"Complex applications with multiple modules detected"})]})]})]})]})]})}function p({analysisId:e}){let[s,t]=(0,a.useState)(""),[l,i]=(0,a.useState)(""),[n,d]=(0,a.useState)([]),[c,o]=(0,a.useState)(!1),[m,x]=(0,a.useState)(0),[h,p]=(0,a.useState)(1),[u,g]=(0,a.useState)(!1),j=async(t=1,r=!1)=>{if(s.trim()||l){o(!0);try{let a=new URLSearchParams({q:s,limit:"50",offset:((t-1)*50).toString()});l&&a.append("level",l);let i=await fetch(`/api/analysis/${e}/search?${a}`),n=await i.json();r?d(e=>[...e,...n.results]):d(n.results),x(n.total_results),g(n.has_more),p(t)}catch(e){console.error("Search failed:",e)}finally{o(!1)}}},y=()=>{p(1),j(1,!1)},b=e=>{if(!e)return"N/A";try{return new Date(e).toLocaleString()}catch{return e}},v=e=>{switch(e?.toUpperCase()){case"ERROR":return"text-red-600 bg-red-50";case"WARN":case"WARNING":return"text-yellow-600 bg-yellow-50";case"INFO":return"text-blue-600 bg-blue-50";default:return"text-gray-600 bg-gray-50"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Search Log Entries"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Query"}),(0,r.jsx)("input",{type:"text",value:s,onChange:e=>t(e.target.value),placeholder:"Enter search terms...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onKeyPress:e=>"Enter"===e.key&&y()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Log Level"}),(0,r.jsxs)("select",{value:l,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,r.jsx)("option",{value:"",children:"All Levels"}),(0,r.jsx)("option",{value:"ERROR",children:"Error"}),(0,r.jsx)("option",{value:"WARN",children:"Warning"}),(0,r.jsx)("option",{value:"INFO",children:"Info"}),(0,r.jsx)("option",{value:"DEBUG",children:"Debug"})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:y,disabled:c||!s.trim()&&!l,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors",children:c?"Searching...":"Search"}),(0,r.jsx)("button",{onClick:()=>{t(""),i(""),d([]),x(0),p(1),g(!1)},className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium transition-colors",children:"Clear"})]}),m>0&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:["Found ",m.toLocaleString()," results"]})]}),n.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg border",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Search Results"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:n.map((e,s)=>(0,r.jsxs)("div",{className:"p-4 hover:bg-gray-50",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-2",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500 font-mono",children:["Line ",e.line_number]}),e.level&&(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${v(e.level)}`,children:e.level}),e.timestamp&&(0,r.jsx)("span",{className:"text-xs text-gray-500",children:b(e.timestamp)})]})}),(0,r.jsx)("div",{className:"text-sm text-gray-900 font-mono bg-gray-50 p-3 rounded border overflow-x-auto",children:e.raw_line}),e.message&&e.message!==e.raw_line&&(0,r.jsxs)("div",{className:"mt-2 text-sm text-gray-700",children:[(0,r.jsx)("strong",{children:"Parsed Message:"})," ",e.message]}),e.fields&&Object.keys(e.fields).length>0&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("details",{className:"text-sm",children:[(0,r.jsxs)("summary",{className:"cursor-pointer text-gray-600 hover:text-gray-800",children:["View parsed fields (",Object.keys(e.fields).length,")"]}),(0,r.jsx)("div",{className:"mt-2 bg-gray-50 p-2 rounded text-xs font-mono",children:(0,r.jsx)("pre",{children:JSON.stringify(e.fields,null,2)})})]})})]},s))}),u&&(0,r.jsx)("div",{className:"p-4 border-t text-center",children:(0,r.jsx)("button",{onClick:()=>{j(h+1,!0)},disabled:c,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-md font-medium transition-colors",children:c?"Loading...":"Load More Results"})})]}),!c&&0===n.length&&(s.trim()||l)&&(0,r.jsxs)("div",{className:"bg-white rounded-lg p-8 text-center border",children:[(0,r.jsx)("div",{className:"text-gray-400 text-4xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Results Found"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search terms or filters to find matching log entries."})]}),0===n.length&&!s.trim()&&!l&&(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 border border-blue-200",children:[(0,r.jsx)("h4",{className:"text-md font-medium text-blue-900 mb-3",children:"Search Tips"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsx)("li",{children:"• Enter keywords to search in log messages and raw content"}),(0,r.jsx)("li",{children:"• Use the level filter to find specific types of log entries"}),(0,r.jsx)("li",{children:"• Search is case-insensitive and matches partial words"}),(0,r.jsx)("li",{children:"• Combine text search with level filters for precise results"}),(0,r.jsx)("li",{children:'• Results are paginated - use "Load More" to see additional entries'})]})]})]})}function u({data:e}){if(!e)return null;let s=[],t=[],a=e.summary?.summary;a&&("critical"===a.health_status?(s.push(`🚨 System health is CRITICAL with ${a.error_rate}% error rate`),t.push("Immediate action required: Investigate and resolve critical errors")):"warning"===a.health_status?(s.push(`⚠️ System health needs attention with ${a.error_rate}% error rate`),t.push("Monitor error trends closely and implement error reduction strategies")):"healthy"===a.health_status&&s.push(`✅ System appears healthy with low error rate (${a.error_rate}%)`),a.total_entries>1e5?s.push(`📊 High volume system with ${a.total_entries.toLocaleString()} log entries`):a.total_entries<100&&s.push(`📉 Low activity detected with only ${a.total_entries} log entries`));let l=e.errorAnalysis?.error_analysis;if(l&&l.total_errors>0){let e=Object.entries(l.error_types||{}).sort(([,e],[,s])=>s-e)[0];e&&s.push(`🔍 Most common error type: ${e[0]} (${e[1]} occurrences)`)}let i=e.performanceAnalysis?.performance_analysis;if(i&&i.total_requests>0){let e=i.avg_response_time,r=i.slow_request_count;e>2e3?(s.push(`🐌 High average response time: ${e.toFixed(0)}ms`),t.push("Consider performance optimization to reduce response times")):e<500&&s.push(`⚡ Excellent performance: Average response time is ${e.toFixed(0)}ms`),r>0&&(s.push(`⏱️ Found ${r} slow requests (>5 seconds) that need investigation`),r>.05*i.total_requests&&t.push("Investigate and optimize slow requests (>5% of requests are slow)"))}let n=e.customerAnalysis?.customer_analysis;if(n&&n.total_unique_customers>0&&(s.push(`👥 Serving ${n.total_unique_customers} unique customers`),n.error_prone_customers?.length>0)){let e=n.error_prone_customers[0];s.push(`🚨 Customer ${e.customer_id} has ${e.error_count} errors`),t.push("Provide additional support to customers experiencing frequent errors")}let d=e.applicationAnalysis?.application_analysis;if(d&&d.total_applications>1){s.push(`📱 Monitoring ${d.total_applications} different applications`);let e=d.application_summary||[];if(e.length>0){let r=e.reduce((e,s)=>s.error_rate>e.error_rate?s:e);r.error_rate>5&&(s.push(`⚠️ Application '${r.app_name}' has high error rate: ${r.error_rate}%`),r.error_rate>10&&t.push(`Investigate high error rate in application '${r.app_name}'`))}}let c=e.timeAnalysis?.time_analysis;if(c){let e=c.peak_hour;null!==e&&s.push(`📈 Peak activity occurs at ${e.toString().padStart(2,"0")}:00`);let t=c.total_days;t>1&&s.push(`📅 Data spans ${t} days`)}let o=e.patternAnalysis?.pattern_analysis;if(o){let e=o.common_messages||[];e.length>0&&s.push(`🔍 Found ${e.length} recurring message patterns`);let t=o.ip_analysis;t&&t.total_unique_ips>0&&s.push(`🌐 ${t.total_unique_ips} unique IP addresses detected`)}return a&&a.total_entries>1e4&&t.push("Consider implementing log rotation and archival for large log files"),0===s.length&&(s.push("\uD83D\uDCCA Analysis completed successfully"),s.push("\uD83D\uDD0D Review the detailed charts and tables for more insights")),0===t.length&&(t.push("Continue monitoring system health and performance"),t.push("Set up alerts for error rate thresholds")),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-xl mr-3",children:"\uD83D\uDCA1"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Key Insights"})]}),(0,r.jsx)("div",{className:"space-y-3",children:s.slice(0,8).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:e})]},s))}),s.length>8&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["And ",s.length-8," more insights available in detailed views..."]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 border",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-xl mr-3",children:"\uD83C\uDFAF"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recommendations"})]}),(0,r.jsx)("div",{className:"space-y-3",children:t.slice(0,6).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-2 h-2 bg-green-400 rounded-full mt-2 mr-3"}),(0,r.jsx)("p",{className:"text-sm text-gray-700",children:e})]},s))}),t.length>6&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-500",children:["And ",t.length-6," more recommendations..."]})]})]})}function g({analysisId:e}){let[s,t]=(0,a.useState)("overview"),[i,n]=(0,a.useState)(null),[g,j]=(0,a.useState)(!0),[y,b]=(0,a.useState)(null),{setAnalysisData:v}=(0,l.S)();return g?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading analysis data..."})]})}):y?(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"text-red-400",children:"⚠️"}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Analysis"}),(0,r.jsx)("p",{className:"text-sm text-red-700 mt-1",children:y})]})]})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Analysis Results"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:["Analysis ID: ",e]}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Refresh"})]})]}),i?.summary&&(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-blue-600 text-sm font-medium",children:"Total Entries"}),(0,r.jsx)("div",{className:"text-blue-900 text-2xl font-bold",children:i.summary.summary?.total_entries?.toLocaleString()||0})]}),(0,r.jsxs)("div",{className:"bg-red-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-red-600 text-sm font-medium",children:"Error Rate"}),(0,r.jsxs)("div",{className:"text-red-900 text-2xl font-bold",children:[i.summary.summary?.error_rate?.toFixed(1)||0,"%"]})]}),(0,r.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"Health Status"}),(0,r.jsx)("div",{className:"text-green-900 text-lg font-bold capitalize",children:i.summary.summary?.health_status||"Unknown"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"text-purple-600 text-sm font-medium",children:"Parse Success"}),(0,r.jsxs)("div",{className:"text-purple-900 text-2xl font-bold",children:[i.summary.summary?.parse_success_rate?.toFixed(1)||0,"%"]})]})]})]}),(0,r.jsx)(u,{data:i}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm",children:[(0,r.jsx)("div",{className:"border-b border-gray-200",children:(0,r.jsx)("nav",{className:"flex space-x-8 px-6",children:[{id:"overview",label:"Overview",icon:"\uD83D\uDCCA"},{id:"errors",label:"Error Analysis",icon:"\uD83D\uDEA8"},{id:"performance",label:"Performance",icon:"⚡"},{id:"customers",label:"Customers",icon:"\uD83D\uDC65"},{id:"applications",label:"Applications",icon:"\uD83D\uDCF1"},{id:"patterns",label:"Patterns",icon:"\uD83D\uDD0D"},{id:"timeline",label:"Timeline",icon:"\uD83D\uDCC8"},{id:"search",label:"Search Logs",icon:"\uD83D\uDD0E"}].map(e=>(0,r.jsxs)("button",{onClick:()=>t(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm ${s===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})}),(0,r.jsxs)("div",{className:"p-6",children:["overview"===s&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsx)(d,{data:i.errorAnalysis}),(0,r.jsx)(o,{data:i.patternAnalysis})]}),i.performanceAnalysis?.performance_analysis&&(0,r.jsx)(m,{data:i.performanceAnalysis}),(0,r.jsx)(c,{data:i.timeAnalysis})]}),"errors"===s&&(0,r.jsx)(d,{data:i.errorAnalysis,detailed:!0}),"performance"===s&&(0,r.jsx)(m,{data:i.performanceAnalysis,detailed:!0}),"customers"===s&&(0,r.jsx)(x,{data:i.customerAnalysis,detailed:!0}),"applications"===s&&(0,r.jsx)(h,{data:i.applicationAnalysis,detailed:!0}),"patterns"===s&&(0,r.jsx)(o,{data:i.patternAnalysis,detailed:!0}),"timeline"===s&&(0,r.jsx)(c,{data:i.timeAnalysis,detailed:!0}),"search"===s&&(0,r.jsx)(p,{analysisId:e})]})]})]})}i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$,i.Bs),i.t1.register(i.PP,i.kc,i.E8,i.No,i.FN,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.No,i.FN,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$),i.t1.register(i.PP,i.kc,i.E8,i.hE,i.m_,i.s$,i.Bs)},7266:(e,s,t)=>{"use strict";t.d(s,{AnalysisProvider:()=>a});var r=t(2907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalysisProvider() from the server but AnalysisProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/context/AnalysisContext.tsx","AnalysisProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAnalysis() from the server but useAnalysis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Sherwyn/Project Repos/AI Projects /log_analyzer/frontend-nextjs/src/context/AnalysisContext.tsx","useAnalysis")},7856:(e,s,t)=>{"use strict";t.d(s,{AnalysisProvider:()=>i,S:()=>n});var r=t(687),a=t(3210);let l=(0,a.createContext)(void 0);function i({children:e}){let[s,t]=(0,a.useState)(null),[i,n]=(0,a.useState)(null),[d,c]=(0,a.useState)(!1);return(0,r.jsx)(l.Provider,{value:{currentAnalysis:s,setCurrentAnalysis:t,analysisData:i,setAnalysisData:n,isLoading:d,setIsLoading:c},children:e})}function n(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAnalysis must be used within an AnalysisProvider");return e}},7948:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},9181:(e,s,t)=>{Promise.resolve().then(t.bind(t,1891)),Promise.resolve().then(t.bind(t,7856))}};