Convert the Log Analyzer application's backend from Python Flask to Node.js while maintaining all existing functionality. The frontend should remain in React as it already is. Specifically:

**Backend Migration Requirements:**
1. Rewrite the Flask API (`backend/app.py`) to Node.js using Express.js framework
2. Convert all Python log parsing modules (`backend/log_parser/`) to JavaScript/TypeScript
3. Convert the analysis engine (`backend/analysis/analyzer.py`) to JavaScript, maintaining all enterprise structured log parsing capabilities
4. Preserve all existing API endpoints and their exact functionality:
   - POST /api/upload (file upload and analysis)
   - GET /api/analysis/{id} (complete analysis results)
   - GET /api/analysis/{id}/summary, /errors, /timeline, /patterns, /anomalies
   - GET /api/analysis/{id}/performance, /customers, /applications (enterprise features)
   - GET /api/analysis/{id}/search, /export
   - GET /api/health, /api/docs
5. Maintain the same response formats and data structures
6. Keep support for all log formats: enterprise_structured, JSON, Apache, Nginx, syslog, delimited, plain_text
7. Preserve the enterprise log analysis features (performance metrics, customer analytics, application monitoring)

**Technical Requirements:**
- Use Express.js for the web framework
- Use multer for file uploads
- Implement the same log format detection and parsing logic
- Maintain the same analysis algorithms (error detection, pattern analysis, anomaly detection)
- Keep the same caching mechanism for analysis results
- Update package.json with appropriate Node.js dependencies
- Update the startup scripts to work with Node.js instead of Python

**Files to Preserve:**
- Keep the entire frontend/ directory unchanged
- Keep all startup scripts but update them for Node.js
- Keep sample_logs/ and documentation files
- Maintain the same project structure but replace Python files with JavaScript equivalents

The goal is to have a functionally identical application that uses Node.js for the backend while keeping the React frontend exactly as it is.