#!/bin/bash

# Log Analyzer Development Script
# This script provides convenient commands for development workflow

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend-node"
FRONTEND_DIR="$PROJECT_DIR/frontend-nextjs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Log Analyzer Development Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup          Set up development environment"
    echo "  start          Start both backend and frontend in development mode"
    echo "  backend        Start only backend in development mode"
    echo "  frontend       Start only frontend in development mode"
    echo "  test           Run tests"
    echo "  test-backend   Run backend tests"
    echo "  test-frontend  Run frontend tests"
    echo "  lint           Run linting for both backend and frontend"
    echo "  lint-backend   Run backend linting (flake8, black)"
    echo "  lint-frontend  Run frontend linting (eslint)"
    echo "  format         Format code for both backend and frontend"
    echo "  build          Build frontend for production"
    echo "  clean          Clean build artifacts and caches"
    echo "  logs           Show application logs"
    echo "  status         Show application status"
    echo "  stop           Stop all running services"
    echo ""
    echo "Examples:"
    echo "  $0 setup       # Set up development environment"
    echo "  $0 start       # Start development servers"
    echo "  $0 test        # Run all tests"
    echo "  $0 lint        # Run all linting"
    echo ""
}

# Function to setup development environment
setup_dev_env() {
    print_status "Setting up development environment..."

    # Setup Node.js backend environment
    print_status "Setting up Node.js backend environment..."
    cd "$BACKEND_DIR"
    npm install

    # Install development dependencies globally if needed
    if ! command -v nodemon >/dev/null 2>&1; then
        print_status "Installing nodemon globally..."
        npm install -g nodemon
    fi

    cd "$PROJECT_DIR"

    # Setup Node.js frontend environment
    print_status "Setting up Node.js frontend environment..."
    cd "$FRONTEND_DIR"
    npm install
    cd "$PROJECT_DIR"

    print_success "Development environment setup completed!"
}

# Function to start development servers
start_dev() {
    print_status "Starting development servers..."
    
    # Start backend in background
    print_status "Starting backend..."
    ./start-backend.sh &
    BACKEND_PID=$!
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend in foreground
    print_status "Starting frontend..."
    ./start-frontend.sh --dev
}

# Function to run tests
run_tests() {
    print_status "Running all tests..."
    
    # Run backend tests
    run_backend_tests
    
    # Run frontend tests
    run_frontend_tests
    
    print_success "All tests completed!"
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."

    cd "$BACKEND_DIR"

    if [ -f "jest.config.js" ] || [ -d "tests" ] || [ -d "__tests__" ]; then
        npm test
    else
        print_warning "No backend tests found. Creating basic test structure..."
        mkdir -p tests
        cat > tests/app.test.js << 'EOF'
const request = require('supertest');

// Mock the app for testing
const mockApp = {
    get: jest.fn(),
    post: jest.fn(),
    listen: jest.fn()
};

describe('Basic App Tests', () => {
    test('should be able to require app modules', () => {
        expect(() => {
            require('../src/log-parser/detector');
            require('../src/log-parser/parser');
            require('../src/analysis/analyzer');
        }).not.toThrow();
    });
});
EOF

        # Add test script to package.json if not present
        if ! grep -q '"test"' package.json; then
            npm install --save-dev jest supertest
        fi

        npm test
    fi

    cd "$PROJECT_DIR"
    print_success "Backend tests completed!"
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd "$FRONTEND_DIR"
    
    # Set CI environment to avoid interactive mode
    export CI=true
    npm test -- --coverage --watchAll=false
    
    cd "$PROJECT_DIR"
    print_success "Frontend tests completed!"
}

# Function to run linting
run_lint() {
    print_status "Running linting for all code..."
    
    run_backend_lint
    run_frontend_lint
    
    print_success "All linting completed!"
}

# Function to run backend linting
run_backend_lint() {
    print_status "Running backend linting..."

    cd "$BACKEND_DIR"

    # Check if ESLint is available
    if npm list eslint >/dev/null 2>&1 || command -v eslint >/dev/null 2>&1; then
        print_status "Running ESLint..."
        npm run lint || npx eslint . || true
    else
        print_warning "ESLint not found. Installing..."
        npm install --save-dev eslint
        npx eslint --init || true
        npm run lint || npx eslint . || true
    fi

    cd "$PROJECT_DIR"
    print_success "Backend linting completed!"
}

# Function to run frontend linting
run_frontend_lint() {
    print_status "Running frontend linting..."
    
    cd "$FRONTEND_DIR"
    
    print_status "Running ESLint..."
    npm run lint || true
    
    cd "$PROJECT_DIR"
    print_success "Frontend linting completed!"
}

# Function to format code
format_code() {
    print_status "Formatting all code..."

    # Format backend code
    print_status "Formatting backend code with prettier..."
    cd "$BACKEND_DIR"
    if npm list prettier >/dev/null 2>&1; then
        npm run format || npx prettier --write .
    else
        print_warning "Installing prettier for backend..."
        npm install --save-dev prettier
        npx prettier --write .
    fi
    cd "$PROJECT_DIR"

    # Format frontend code
    print_status "Formatting frontend code with prettier..."
    cd "$FRONTEND_DIR"
    if npm list prettier >/dev/null 2>&1; then
        npm run format || npx prettier --write src/
    else
        print_warning "Prettier not found, skipping frontend formatting"
    fi
    cd "$PROJECT_DIR"

    print_success "Code formatting completed!"
}

# Function to build for production
build_prod() {
    print_status "Building frontend for production..."
    
    cd "$FRONTEND_DIR"
    npm run build
    cd "$PROJECT_DIR"
    
    print_success "Production build completed!"
    print_status "Build output is in frontend/build/"
}

# Function to clean build artifacts
clean_artifacts() {
    print_status "Cleaning build artifacts and caches..."

    # Clean frontend
    cd "$FRONTEND_DIR"
    rm -rf build/ node_modules/.cache/ || true
    cd "$PROJECT_DIR"

    # Clean backend
    cd "$BACKEND_DIR"
    rm -rf node_modules/.cache/ coverage/ .nyc_output/ || true
    rm -rf uploads/ || true
    cd "$PROJECT_DIR"

    # Clean logs
    rm -rf logs/ || true

    print_success "Cleanup completed!"
}

# Function to show logs
show_logs() {
    print_status "Showing application logs..."
    
    if [ -f "logs/backend/flask.log" ]; then
        echo "=== Backend Logs ==="
        tail -n 20 logs/backend/flask.log
        echo ""
    fi
    
    if [ -f "logs/frontend/react.log" ]; then
        echo "=== Frontend Logs ==="
        tail -n 20 logs/frontend/react.log
        echo ""
    fi
    
    if [ ! -f "logs/backend/flask.log" ] && [ ! -f "logs/frontend/react.log" ]; then
        print_warning "No log files found. Applications may not be running."
    fi
}

# Function to show status
show_status() {
    print_status "Checking application status..."
    
    # Check backend
    if curl -s http://localhost:5000/api/health >/dev/null 2>&1; then
        print_success "Backend is running (http://localhost:5000)"
    else
        print_warning "Backend is not running"
    fi
    
    # Check frontend
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        print_success "Frontend is running (http://localhost:3000)"
    else
        print_warning "Frontend is not running"
    fi
    
    # Show process information
    echo ""
    echo "=== Process Information ==="
    
    if [ -f "logs/backend/flask.pid" ]; then
        BACKEND_PID=$(cat logs/backend/flask.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            echo "Backend PID: $BACKEND_PID (running)"
        else
            echo "Backend PID: $BACKEND_PID (not running)"
        fi
    fi
    
    if [ -f "logs/frontend/react.pid" ]; then
        FRONTEND_PID=$(cat logs/frontend/react.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            echo "Frontend PID: $FRONTEND_PID (running)"
        else
            echo "Frontend PID: $FRONTEND_PID (not running)"
        fi
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping all services..."
    ./stop.sh
}

# Main execution
case "${1:-}" in
    setup)
        setup_dev_env
        ;;
    start)
        start_dev
        ;;
    backend)
        ./start-backend.sh --dev
        ;;
    frontend)
        ./start-frontend.sh --dev
        ;;
    test)
        run_tests
        ;;
    test-backend)
        run_backend_tests
        ;;
    test-frontend)
        run_frontend_tests
        ;;
    lint)
        run_lint
        ;;
    lint-backend)
        run_backend_lint
        ;;
    lint-frontend)
        run_frontend_lint
        ;;
    format)
        format_code
        ;;
    build)
        build_prod
        ;;
    clean)
        clean_artifacts
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    stop)
        stop_services
        ;;
    help|--help|-h)
        show_usage
        ;;
    "")
        print_error "No command specified"
        show_usage
        exit 1
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
