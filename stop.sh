#!/bin/bash

# Log Analyzer Application Stop Script
# This script stops both the backend Flask API and frontend React application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$PROJECT_DIR/logs"
BACKEND_PORT=5000
FRONTEND_PORT=3000

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to stop process by PID file
stop_by_pid_file() {
    local pid_file=$1
    local service_name=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            print_status "Stopping $service_name (PID: $pid)..."
            kill $pid
            
            # Wait for process to stop
            local attempts=0
            while kill -0 $pid 2>/dev/null && [ $attempts -lt 10 ]; do
                sleep 1
                attempts=$((attempts + 1))
            done
            
            if kill -0 $pid 2>/dev/null; then
                print_warning "Process $pid didn't stop gracefully, forcing termination..."
                kill -9 $pid 2>/dev/null || true
            fi
            
            print_success "$service_name stopped"
        else
            print_warning "$service_name PID file exists but process is not running"
        fi
        
        rm -f "$pid_file"
    else
        print_warning "No PID file found for $service_name"
    fi
}

# Function to stop processes by port
stop_by_port() {
    local port=$1
    local service_name=$2
    
    print_status "Looking for processes on port $port..."
    
    # Find processes using the port
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        print_status "Found processes on port $port: $pids"
        for pid in $pids; do
            print_status "Stopping process $pid ($service_name)..."
            kill $pid 2>/dev/null || true
            
            # Wait a moment for graceful shutdown
            sleep 2
            
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                print_warning "Force killing process $pid..."
                kill -9 $pid 2>/dev/null || true
            fi
        done
        print_success "$service_name processes stopped"
    else
        print_status "No processes found on port $port"
    fi
}

# Function to stop processes by name pattern
stop_by_pattern() {
    local pattern=$1
    local service_name=$2
    
    print_status "Looking for $service_name processes..."
    
    local pids=$(pgrep -f "$pattern" 2>/dev/null || true)
    
    if [ -n "$pids" ]; then
        print_status "Found $service_name processes: $pids"
        for pid in $pids; do
            print_status "Stopping $service_name process $pid..."
            kill $pid 2>/dev/null || true
        done
        
        # Wait for graceful shutdown
        sleep 3
        
        # Force kill any remaining processes
        local remaining_pids=$(pgrep -f "$pattern" 2>/dev/null || true)
        if [ -n "$remaining_pids" ]; then
            print_warning "Force killing remaining $service_name processes: $remaining_pids"
            pkill -9 -f "$pattern" 2>/dev/null || true
        fi
        
        print_success "$service_name processes stopped"
    else
        print_status "No $service_name processes found"
    fi
}

# Function to stop backend
stop_backend() {
    print_status "Stopping backend Flask application..."
    
    # Try to stop by PID file first
    stop_by_pid_file "$LOG_DIR/backend/node.pid" "Backend Node.js"

    # Also try to stop by port
    stop_by_port $BACKEND_PORT "Backend"

    # Also try to stop by process pattern
    stop_by_pattern "node.*app.js" "Node.js"
    stop_by_pattern "nodemon.*app.js" "Nodemon"
}

# Function to stop frontend
stop_frontend() {
    print_status "Stopping frontend React application..."
    
    # Try to stop by PID file first
    stop_by_pid_file "$LOG_DIR/frontend/react.pid" "Frontend React"
    
    # Also try to stop by port
    stop_by_port $FRONTEND_PORT "Frontend"
    
    # Also try to stop by process pattern
    stop_by_pattern "node.*react-scripts.*start" "React"
    stop_by_pattern "npm.*start" "NPM Start"
}

# Function to clean up log files and PID files
cleanup_files() {
    print_status "Cleaning up files..."
    
    # Remove PID files
    rm -f "$LOG_DIR/backend/node.pid" 2>/dev/null || true
    rm -f "$LOG_DIR/frontend/react.pid" 2>/dev/null || true
    
    print_success "Cleanup completed"
}

# Function to show stop information
show_stop_info() {
    echo ""
    echo "=============================================="
    echo "🛑 Log Analyzer Application Stopped!"
    echo "=============================================="
    echo ""
    echo "All services have been stopped successfully."
    echo ""
    echo "📁 Log files are preserved at:"
    echo "   Backend:  $LOG_DIR/backend/node.log"
    echo "   Frontend: $LOG_DIR/frontend/react.log"
    echo ""
    echo "🚀 To start the application again, run: ./start.sh"
    echo "=============================================="
    echo ""
}

# Function to verify services are stopped
verify_stopped() {
    print_status "Verifying services are stopped..."
    
    local backend_running=false
    local frontend_running=false
    
    # Check backend port
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        backend_running=true
        print_warning "Backend port $BACKEND_PORT is still in use"
    fi
    
    # Check frontend port
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        frontend_running=true
        print_warning "Frontend port $FRONTEND_PORT is still in use"
    fi
    
    if [ "$backend_running" = false ] && [ "$frontend_running" = false ]; then
        print_success "All services are stopped"
        return 0
    else
        print_error "Some services may still be running"
        return 1
    fi
}

# Main execution
main() {
    echo "🛑 Log Analyzer - Stopping Application..."
    echo ""
    
    # Stop services
    stop_backend
    stop_frontend
    
    # Clean up files
    cleanup_files
    
    # Verify everything is stopped
    verify_stopped
    
    # Show stop information
    show_stop_info
}

# Handle command line arguments
case "${1:-}" in
    --force|-f)
        print_warning "Force stop mode enabled"
        # Kill all related processes forcefully
        pkill -9 -f "node.*app.js" 2>/dev/null || true
        pkill -9 -f "nodemon" 2>/dev/null || true
        pkill -9 -f "react-scripts" 2>/dev/null || true
        pkill -9 -f "npm.*start" 2>/dev/null || true
        main
        ;;
    --help|-h)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Stop the Log Analyzer application"
        echo ""
        echo "Options:"
        echo "  -f, --force    Force stop all related processes"
        echo "  -h, --help     Show this help message"
        echo ""
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
