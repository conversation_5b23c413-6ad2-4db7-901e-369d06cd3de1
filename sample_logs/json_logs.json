{"timestamp": "2025-01-12T08:00:01.123Z", "level": "INFO", "message": "Application started successfully", "service": "web-server", "version": "1.2.3"}
{"timestamp": "2025-01-12T08:00:02.456Z", "level": "INFO", "message": "Database connection established", "service": "database", "connection_pool": "primary", "host": "db-primary.internal"}
{"timestamp": "2025-01-12T08:00:03.789Z", "level": "INFO", "message": "Configuration loaded", "service": "config-manager", "config_file": "production.yml", "settings_count": 45}
{"timestamp": "2025-01-12T08:01:15.678Z", "level": "INFO", "message": "User login successful", "service": "auth", "user_id": "user_12345", "email": "<EMAIL>", "ip_address": "*************"}
{"timestamp": "2025-01-12T08:02:30.234Z", "level": "INFO", "message": "API request processed", "service": "api-gateway", "method": "GET", "endpoint": "/api/users", "response_time_ms": 145, "status_code": 200}
{"timestamp": "2025-01-12T08:03:45.890Z", "level": "WARN", "message": "High memory usage detected", "service": "monitoring", "memory_usage_percent": 85, "threshold_percent": 80, "available_mb": 512}
{"timestamp": "2025-01-12T08:04:12.123Z", "level": "ERROR", "message": "File upload failed", "service": "file-handler", "error": "File size exceeds maximum limit", "file_size_mb": 150, "max_size_mb": 100, "user_id": "user_67890"}
{"timestamp": "2025-01-12T08:05:20.789Z", "level": "INFO", "message": "User logout successful", "service": "auth", "user_id": "user_12345", "session_duration_minutes": 245}
{"timestamp": "2025-01-12T08:06:30.345Z", "level": "WARN", "message": "Database connection pool running low", "service": "database", "available_connections": 2, "total_connections": 20, "threshold": 5}
{"timestamp": "2025-01-12T08:07:45.678Z", "level": "ERROR", "message": "Database connection timeout", "service": "database", "timeout_seconds": 30, "query": "SELECT * FROM user_sessions", "connection_id": "conn_789"}
{"timestamp": "2025-01-12T08:08:15.567Z", "level": "INFO", "message": "Batch job completed", "service": "scheduler", "job_name": "daily_report_generation", "records_processed": 1250, "duration_seconds": 45}
{"timestamp": "2025-01-12T08:09:30.123Z", "level": "WARN", "message": "Slow query detected", "service": "database", "query": "SELECT * FROM large_table", "execution_time_seconds": 5.2, "threshold_seconds": 2.0}
{"timestamp": "2025-01-12T08:10:15.456Z", "level": "ERROR", "message": "Authentication failed", "service": "auth", "email": "<EMAIL>", "ip_address": "*************", "reason": "invalid_credentials"}
{"timestamp": "2025-01-12T08:11:30.012Z", "level": "INFO", "message": "Cache refresh completed", "service": "cache-manager", "entries_updated": 500, "cache_hit_rate_percent": 92.5}
{"timestamp": "2025-01-12T08:12:45.345Z", "level": "ERROR", "message": "External API call failed", "service": "payment-processor", "api_endpoint": "https://payment.service.com/charge", "error": "Connection refused", "retry_count": 1}
{"timestamp": "2025-01-12T08:13:15.234Z", "level": "INFO", "message": "Scheduled maintenance completed", "service": "maintenance", "task": "log_cleanup", "files_deleted": 25, "space_freed_mb": 1024}
{"timestamp": "2025-01-12T08:14:30.567Z", "level": "WARN", "message": "Disk space running low", "service": "monitoring", "partition": "/var/log", "available_percent": 15, "threshold_percent": 20}
{"timestamp": "2025-01-12T08:15:45.890Z", "level": "INFO", "message": "Log rotation completed", "service": "log-manager", "rotated_files": 12, "compressed_files": 8}
{"timestamp": "2025-01-12T08:16:20.123Z", "level": "ERROR", "message": "Unhandled exception", "service": "request-handler", "exception": "NullPointerException", "stack_trace": "java.lang.NullPointerException at com.example.Handler.process(Handler.java:42)", "request_id": "req_abc123"}
{"timestamp": "2025-01-12T08:17:30.789Z", "level": "INFO", "message": "Health check passed", "service": "health-monitor", "checks_passed": 8, "checks_total": 8, "response_time_ms": 23}
{"timestamp": "2025-01-12T08:18:45.012Z", "level": "WARN", "message": "Rate limit exceeded", "service": "api-gateway", "api_key": "abc123def456", "requests_per_minute": 1000, "limit_per_minute": 500}
{"timestamp": "2025-01-12T08:19:15.345Z", "level": "INFO", "message": "Webhook processed", "service": "webhook-handler", "source": "github", "event": "push", "repository": "example/repo"}
{"timestamp": "2025-01-12T08:20:30.678Z", "level": "ERROR", "message": "Email notification failed", "service": "notification", "recipient": "<EMAIL>", "error": "SMTP server unreachable", "smtp_host": "mail.example.com"}
{"timestamp": "2025-01-12T08:21:45.234Z", "level": "INFO", "message": "Background task completed", "service": "background-worker", "task": "data_cleanup", "records_cleaned": 5000, "duration_seconds": 120}
{"timestamp": "2025-01-12T08:22:15.567Z", "level": "WARN", "message": "Memory leak detected", "service": "monitoring", "module": "image_processor", "memory_growth_mb": 50, "threshold_mb": 30}
{"timestamp": "2025-01-12T08:23:30.890Z", "level": "INFO", "message": "Garbage collection completed", "service": "jvm-monitor", "memory_freed_mb": 250, "gc_duration_ms": 45, "gc_type": "G1"}
{"timestamp": "2025-01-12T08:24:45.123Z", "level": "ERROR", "message": "Configuration validation failed", "service": "config-validator", "error": "Missing required parameter", "parameter": "api_key", "config_file": "production.yml"}
{"timestamp": "2025-01-12T08:25:15.456Z", "level": "INFO", "message": "Configuration reloaded", "service": "config-manager", "config_version": "1.2.4", "changes_applied": 3}
{"timestamp": "2025-01-12T08:26:30.789Z", "level": "INFO", "message": "User registration successful", "service": "auth", "user_id": "user_54321", "email": "<EMAIL>", "registration_source": "web"}
{"timestamp": "2025-01-12T08:27:45.345Z", "level": "WARN", "message": "Unusual traffic pattern detected", "service": "security-monitor", "traffic_increase_percent": 500, "baseline_requests_per_minute": 100, "current_requests_per_minute": 600}
