2025-01-12 08:00:01,123 - INFO - Application started successfully
2025-01-12 08:00:02,456 - INFO - Database connection established
2025-01-12 08:00:03,789 - INFO - Loading configuration from config.yml
2025-01-12 08:00:05,012 - INFO - Starting HTTP server on port 8080
2025-01-12 08:00:10,345 - INFO - User authentication service initialized
2025-01-12 08:01:15,678 - INFO - Processing user login request for user: <EMAIL>
2025-01-12 08:01:16,901 - INFO - User <EMAIL> logged in successfully
2025-01-12 08:02:30,234 - INFO - Processing API request: GET /api/users
2025-01-12 08:02:31,567 - INFO - Returning 150 user records
2025-01-12 08:03:45,890 - WARN - High memory usage detected: 85% of available memory
2025-01-12 08:04:12,123 - INFO - Processing file upload request
2025-01-12 08:04:15,456 - ERROR - Failed to process file upload: File size exceeds maximum limit
2025-01-12 08:05:20,789 - INFO - Processing user logout request for user: <EMAIL>
2025-01-12 08:05:21,012 - INFO - User <EMAIL> logged out successfully
2025-01-12 08:06:30,345 - WARN - Database connection pool running low: 2 connections remaining
2025-01-12 08:07:45,678 - ERROR - Database connection timeout after 30 seconds
2025-01-12 08:07:46,901 - INFO - Attempting to reconnect to database
2025-01-12 08:07:50,234 - INFO - Database connection restored
2025-01-12 08:08:15,567 - INFO - Processing batch job: daily_report_generation
2025-01-12 08:08:45,890 - INFO - Batch job completed successfully: 1,250 records processed
2025-01-12 08:09:30,123 - WARN - Slow query detected: SELECT * FROM large_table took 5.2 seconds
2025-01-12 08:10:15,456 - ERROR - Authentication failed for user: <EMAIL>
2025-01-12 08:10:16,789 - WARN - Multiple failed login attempts detected from IP: *************
2025-01-12 08:11:30,012 - INFO - Cache refresh completed: 500 entries updated
2025-01-12 08:12:45,345 - ERROR - External API call failed: Connection refused to payment.service.com
2025-01-12 08:12:46,678 - INFO - Retrying external API call with exponential backoff
2025-01-12 08:12:50,901 - INFO - External API call succeeded on retry
2025-01-12 08:13:15,234 - INFO - Processing scheduled maintenance task
2025-01-12 08:14:30,567 - WARN - Disk space running low: 15% remaining on /var/log partition
2025-01-12 08:15:45,890 - INFO - Log rotation completed successfully
2025-01-12 08:16:20,123 - ERROR - Unhandled exception in request handler: NullPointerException
2025-01-12 08:16:21,456 - ERROR - Stack trace: java.lang.NullPointerException at com.example.Handler.process(Handler.java:42)
2025-01-12 08:17:30,789 - INFO - Health check passed: All services responding normally
2025-01-12 08:18:45,012 - WARN - Rate limit exceeded for API key: abc123def456
2025-01-12 08:19:15,345 - INFO - Processing webhook notification from external service
2025-01-12 08:20:30,678 - ERROR - Failed to send email notification: SMTP server unreachable
2025-01-12 08:20:31,901 - INFO - Email notification queued for retry
2025-01-12 08:21:45,234 - INFO - Background task completed: data_cleanup
2025-01-12 08:22:15,567 - WARN - Memory leak detected in module: image_processor
2025-01-12 08:23:30,890 - INFO - Garbage collection completed: 250MB freed
2025-01-12 08:24:45,123 - ERROR - Configuration validation failed: Missing required parameter 'api_key'
2025-01-12 08:25:15,456 - INFO - Configuration reloaded successfully
2025-01-12 08:26:30,789 - INFO - Processing user registration request
2025-01-12 08:26:32,012 - INFO - New user registered: <EMAIL>
2025-01-12 08:27:45,345 - WARN - Unusual traffic pattern detected: 500% increase in requests
2025-01-12 08:28:15,678 - ERROR - Circuit breaker opened for service: recommendation_engine
2025-01-12 08:29:30,901 - INFO - Circuit breaker closed: service recovered
2025-01-12 08:30:45,234 - INFO - Daily backup initiated
2025-01-12 08:35:20,567 - INFO - Daily backup completed successfully: 2.5GB archived
