[2025-05-06 13:43:33][info][Get Country Data][na][na][][::ffff:127.0.0.1][831147][REQUEST][common][0][200][get][/api/v1/getBookingReview][] : {"method":"get","headers":{"sessionid":"na","request_id":"831147"},"url":"https://icuracaositcommonapi.vetravel.io/api/v1/getCountryData","timeout":50000,"auth":"","params":{}}
[2025-05-06 13:43:34][info][Get Country Data][na][na][][::ffff:127.0.0.1][831147][RESPONSE][common][1202][200][get][/api/v1/getBookingReview][] : 
