[2025-03-13 10:11:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][805][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 10:51:20][error][Activities Pickup Location][][][][][][RESPONSE][travone][710][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 10:51:45][error][Activities Pickup Location][][][][][][RESPONSE][travone][727][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 10:52:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40020][][post][][] : ECONNABORTED
[2025-03-13 10:52:25][error][Activities Pickup Location][][][][][][RESPONSE][travone][40027][][post][][] : ECONNABORTED
[2025-03-13 10:52:25][error][Activities Pickup Location][][][][][][RESPONSE][travone][40023][][post][][] : ECONNABORTED
[2025-03-13 11:31:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][975][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 11:43:35][error][Activities Pickup Location][][][][][][RESPONSE][travone][857][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 11:43:44][error][Activities Pickup Location][][][][][][RESPONSE][travone][780][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 11:58:13][error][Activities Pickup Location][][][][][][RESPONSE][travone][824][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 11:58:44][error][Activities Pickup Location][][][][][][RESPONSE][travone][802][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 11:59:56][error][Activities Pickup Location][][][][][][RESPONSE][travone][707][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 13:10:42][error][Activities Pickup Location][][][][][][RESPONSE][travone][859][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 13:15:59][error][Activities Pickup Location][][][][][][RESPONSE][travone][821][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 13:21:53][error][Activities Pickup Location][][][][][][RESPONSE][travone][921][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 13:25:46][error][Activities Pickup Location][][][][][][RESPONSE][travone][740][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-13 19:28:07][error][Activities Pickup Location][][][][][][RESPONSE][travone][903][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
