[2025-05-06 13:43:17][info][Get SRP Offer][na][na][][::ffff:127.0.0.1][692911][REQUEST][cperks][0][200][post][/api/v1/activityDetails][] : {"method":"post","headers":{"content-type":"application/json","Authorization":"**************************************************************************","sessionid":"na","request_id":"692911"},"url":"https://icuracaouatofferapi.vetravel.io/getValidCoupons","timeout":10000,"auth":"","data":{"lob":"experience","lob_data":{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"","activity_id":"34181P22","itinerary_type":"","is_refundable":"yes","activity_rating":0,"base_price":0,"total_price":"41"},"customer_details":{"customer_tier":"","customer_id":"","customer_gender":"","customer_age":"","customer_dob":"","customer_anniversary":"","no_of_guests":"","booking_count":0,"payment_type":""},"activity_details":{"travel_type":"international","booking_date":"2025-05-06","travel_date":"2025-03-22","utm_source":"","utm_campaign":"","mar_channel":"","booking_channel":"Desktop"},"coupon_category":"","activity":"SRP","coupon_codes":[],"language_code":"en"}}
[2025-05-06 13:43:18][info][Get SRP Offer][na][na][][::ffff:127.0.0.1][692911][RESPONSE][cperks][854][200][post][/api/v1/activityDetails][] : {"message":"Coupons found","code":"C101","status":true,"values":{"coupon_codes":[{"coupon_code":"Experiences18","marketing_text":"Flat 20% off"},{"coupon_code":"Experiences17","marketing_text":"Flat 10%off"},{"coupon_code":"Experiences16","marketing_text":"Flat 10% off"},{"coupon_code":"HGFGHSDGD","marketing_text":"Cohort Test"}],"lob_data":["Cohort Test"]}}
[2025-05-06 13:43:28][info][Get SRP Offer][na][na][][::ffff:127.0.0.1][231083][REQUEST][cperks][0][200][post][/api/v1/checkActivityAvailability][] : {"method":"post","headers":{"content-type":"application/json","Authorization":"**************************************************************************","sessionid":"na","request_id":"231083"},"url":"https://icuracaouatofferapi.vetravel.io/getValidCoupons","timeout":10000,"auth":"","data":{"lob":"experience","lob_data":[{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"Asia/Kolkata","activity_id":"34181P22","itinerary_type":"STANDARD","is_refundable":"yes","activity_rating":0,"base_price":60.83,"total_price":60.83},{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"Asia/Kolkata","activity_id":"34181P22","itinerary_type":"STANDARD","is_refundable":"yes","activity_rating":0,"base_price":60.83,"total_price":60.83},{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"Asia/Kolkata","activity_id":"34181P22","itinerary_type":"STANDARD","is_refundable":"yes","activity_rating":0,"base_price":81.1,"total_price":81.1},{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"Asia/Kolkata","activity_id":"34181P22","itinerary_type":"STANDARD","is_refundable":"yes","activity_rating":0,"base_price":81.1,"total_price":81.1},{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"Asia/Kolkata","activity_id":"34181P22","itinerary_type":"STANDARD","is_refundable":"yes","activity_rating":0,"base_price":81.1,"total_price":81.1}],"customer_details":{"customer_tier":"","customer_id":"","customer_gender":"","customer_age":"","customer_dob":"","customer_anniversary":"","no_of_guests":"","booking_count":0,"payment_type":""},"activity_details":{"travel_type":"international","booking_date":"2025-05-06","travel_date":"","utm_source":"","utm_campaign":"","mar_channel":"","booking_channel":"Desktop"},"coupon_category":"","activity":"SRP","coupon_codes":[],"language_code":"en"}}
[2025-05-06 13:43:28][info][Get SRP Offer][na][na][][::ffff:127.0.0.1][231083][RESPONSE][cperks][645][200][post][/api/v1/checkActivityAvailability][] : {"message":"Coupons found","code":"C101","status":true,"values":{"coupon_codes":[{"coupon_code":"Discount2","marketing_text":"Discount2"},{"coupon_code":"Experiences18","marketing_text":"Flat 20% off"},{"coupon_code":"Experiences17","marketing_text":"Flat 10%off"},{"coupon_code":"Experiences16","marketing_text":"Flat 10% off"},{"coupon_code":"Experiences11","marketing_text":"Flat 50/- off"},{"coupon_code":"HGFGHSDGD","marketing_text":"Cohort Test"}],"lob_data":["Discount2","Discount2","Discount2","Discount2","Discount2"]}}
[2025-05-06 13:43:37][info][Get Convenience Fee][na][na][][::ffff:127.0.0.1][831147][REQUEST][cperks][0][200][post][/api/v1/getBookingReview][] : {"method":"post","headers":{"content-type":"application/json","Authorization":"**************************************************************************","sessionid":"na","request_id":"831147"},"url":"https://icuracaouatofferapi.vetravel.io/getConFees","timeout":10000,"auth":"","data":{"lob":"experiencecfees","lob_data":[{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"","activity_id":"34181P22","itinerary_type":"","is_refundable":"yes","activity_rating":0,"base_price":61.01249,"total_price":61.01249}],"customer_details":{"customer_tier":"","customer_id":"","customer_gender":"","customer_age":"","customer_dob":"","customer_anniversary":"","no_of_guests":"","booking_count":0,"payment_type":""},"activity_details":{"travel_type":"international","booking_date":"2025-05-06","travel_date":"2025-05-06","utm_source":"","utm_campaign":"","mar_channel":"","booking_channel":"Desktop"}}}
[2025-05-06 13:43:37][info][Get Convenience Fee][na][na][][::ffff:127.0.0.1][831147][RESPONSE][cperks][642][200][post][/api/v1/getBookingReview][] : {"status":true,"message":"Convenience fees fetched successfully","values":[{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"","activity_id":"34181P22","itinerary_type":"","is_refundable":"yes","activity_rating":0,"base_price":61.01249,"total_price":61.01249,"convenience_fees":{"rule_id":289,"fees":2,"marketing_text":null,"cfees_type":"per_booking"}}]}
[2025-05-06 13:43:37][info][Get All Valid Coupons][na][na][][::ffff:127.0.0.1][831147][REQUEST][cperks][0][200][post][/api/v1/getBookingReview][] : {"method":"post","headers":{"content-type":"application/json","Authorization":"**************************************************************************","sessionid":"na","request_id":"831147","Accept-Language":"en"},"url":"https://icuracaouatofferapi.vetravel.io/getValidCoupons","timeout":10000,"auth":"","data":{"lob":"experience","lob_data":{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"","activity_id":"34181P22","itinerary_type":"","is_refundable":"yes","activity_rating":0,"base_price":61.01249,"total_price":61.01249},"customer_details":{"customer_tier":"","customer_id":"","customer_gender":"","customer_age":"","customer_dob":"","customer_anniversary":"","no_of_guests":"","booking_count":0,"payment_type":""},"activity_details":{"travel_type":"international","booking_date":"2025-05-06","travel_date":"2025-05-06","utm_source":"","utm_campaign":"","mar_channel":"","booking_channel":"Desktop"},"coupon_category":"","language_code":"en"}}
[2025-05-06 13:43:38][info][Get All Valid Coupons][na][na][][::ffff:127.0.0.1][831147][RESPONSE][cperks][220][200][post][/api/v1/getBookingReview][] : {"message":"Coupons found","code":"C101","status":true,"values":[{"coupon_name":"Discount2","description":"<p>This is a discount offer</p>","terms_and_conditions":null,"rank":2,"coupon_code":"Discount2","marketing_text":"Discount2","success_message":null,"offer_type":"common","coupon_category":null,"reward_type":"cash","miles":null,"discount":50},{"coupon_name":"Experiences11","description":"<p>Test English</p>","terms_and_conditions":null,"rank":null,"coupon_code":"Experiences11","marketing_text":"Flat 50/- off","success_message":"Coupon Applied Successfully","offer_type":"common","coupon_category":"generic_offers","reward_type":"cash","miles":null,"discount":50},{"coupon_name":"Experiences18","description":"<p>test in English in test booking</p>","terms_and_conditions":null,"rank":null,"coupon_code":"Experiences18","marketing_text":"Flat 20% off","success_message":"Coupon Applied Successfully","offer_type":"common","coupon_category":"generic_offers","reward_type":"cash","miles":null,"discount":13},{"coupon_name":"Cohort Test","description":"<p>Cohort Test</p><p>Cohort Test</p>","terms_and_conditions":null,"rank":null,"coupon_code":"HGFGHSDGD","marketing_text":"Cohort Test","success_message":null,"offer_type":"common","coupon_category":"generic_offers","reward_type":"cash","miles":null,"discount":13},{"coupon_name":"Experiences17","description":"<p>Test in english</p>","terms_and_conditions":null,"rank":null,"coupon_code":"Experiences17","marketing_text":"Flat 10%off","success_message":"Coupon Applied Successfully","offer_type":"common","coupon_category":"generic_offers","reward_type":"cash","miles":null,"discount":7},{"coupon_name":"Experiences16","description":"<p>test in english</p>","terms_and_conditions":null,"rank":null,"coupon_code":"Experiences16","marketing_text":"Flat 10% off","success_message":"Coupon Applied Successfully","offer_type":"common","coupon_category":"generic_offers","reward_type":"cash","miles":null,"discount":0}]}
[2025-05-06 13:43:38][info][Validate Coupon][na][na][][::ffff:127.0.0.1][831147][REQUEST][cperks][0][200][post][/api/v1/getBookingReview][] : {"method":"post","headers":{"content-type":"application/json","Authorization":"**************************************************************************","sessionid":"na","request_id":"831147"},"url":"https://icuracaouatofferapi.vetravel.io/coupons/validate","timeout":10000,"auth":"","data":{"lob":"experience","lob_data":{"supplier_name":"Viator","country":"India","city":"Mumbai","state":"","area":"","activity_id":"34181P22","itinerary_type":"","is_refundable":"yes","activity_rating":0,"base_price":61.01249,"total_price":61.01249},"customer_details":{"customer_tier":"","customer_id":"","customer_gender":"","customer_age":"","customer_dob":"","customer_anniversary":"","no_of_guests":"","booking_count":0,"payment_type":""},"activity_details":{"travel_type":"international","booking_date":"2025-05-06","travel_date":"2025-05-06","utm_source":"","utm_campaign":"","mar_channel":"","booking_channel":"Desktop"},"coupon_category":"","trans_type":"accrual","coupon_code":"Discount2","language_code":""}}
[2025-05-06 13:43:38][info][Validate Coupon][na][na][][::ffff:127.0.0.1][831147][RESPONSE][cperks][218][200][post][/api/v1/getBookingReview][] : {"message":"Coupon validated successfully","code":"C101","status":true,"values":{"success_message":null,"description":"<p>This is a discount offer</p>","activity_code":"Discount2","label":null,"url":null,"config_id":null,"offer_type":"common","reward_type":"cash","rule_id":2269,"coupon_name":"Discount2","marketing_text":"Discount2","tnc":null,"coupon_code_id":1700,"coupon_code":"Discount2","coupon_type":"manual","co_brand_type":null,"paywall_instance_id":null,"product_type":null,"other_values":{},"miles":{"strike_value":0,"offer_value":0,"miles":null},"discount":50,"funding_partner_name":null,"funding_partner_percentage":null,"is_bin_based_offer":false,"netbanking_partners":[],"card_type":[],"payment_instrument":[],"upi_partners":[],"card_bank":[],"coupon_category":null,"lob":"experience"}}
