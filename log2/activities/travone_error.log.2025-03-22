[2025-03-22 11:04:23][error][Activities reviews ][][][][][][RESPONSE][travone][2082][200][post][][] : {"count":0,"status":"failed","result":{"code":"INTERNAL_SERVER_ERROR","message":"Internal server error","timestamp":"2025-03-22T05:34:23.409665077Z","trackingId":"03D24F15:85D3_0A5D0F7E:01BB_67DE4BDE_87EB0:24F28B","length":0}}
[2025-03-22 11:33:12][error][Activities reviews ][][][][][][RESPONSE][travone][1921][200][post][][] : {"count":0,"status":"failed","result":{"code":"INTERNAL_SERVER_ERROR","message":"Internal server error","timestamp":"2025-03-22T06:03:11.358467046Z","trackingId":"03D24F15:BA32_0A5D0F7E:01BB_67DE529E_8B87A:24F28B","length":0}}
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40017][][post][][] : ECONNABORTED
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40011][][post][][] : ECONNABORTED
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40106][][post][][] : ECONNABORTED
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40101][][post][][] : ECONNABORTED
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40101][][post][][] : ECONNABORTED
[2025-03-22 11:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][40105][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40009][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40013][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40015][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40018][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40018][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40019][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40091][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40091][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40091][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40090][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40089][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40089][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40089][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40086][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40085][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40085][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40082][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40080][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40076][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40056][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40035][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40019][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40014][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40009][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40006][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40003][][post][][] : ECONNABORTED
[2025-03-22 11:34:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][40003][][post][][] : ECONNABORTED
[2025-03-22 13:19:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][727][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:21:56][error][Activities Pickup Location][][][][][][RESPONSE][travone][870][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:31:34][error][Activities Pickup Location][][][][][][RESPONSE][travone][1203][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:31:37][error][Activities Pickup Location][][][][][][RESPONSE][travone][659][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:32:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][702][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:32:55][error][Activities Pickup Location][][][][][][RESPONSE][travone][972][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:33:19][error][Activities Pickup Location][][][][][][RESPONSE][travone][770][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 13:33:35][error][Activities Pickup Location][][][][][][RESPONSE][travone][742][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:18:16][error][Activities Pickup Location][][][][][][RESPONSE][travone][680][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:18:36][error][Activities Pickup Location][][][][][][RESPONSE][travone][799][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:18:40][error][Activities Pickup Location][][][][][][RESPONSE][travone][688][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:20:07][error][Activities Pickup Location][][][][][][RESPONSE][travone][763][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:24:51][error][Activities Pickup Location][][][][][][RESPONSE][travone][726][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:24:58][error][Activities Pickup Location][][][][][][RESPONSE][travone][840][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:25:02][error][Activities Pickup Location][][][][][][RESPONSE][travone][638][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:25:57][error][Activities Pickup Location][][][][][][RESPONSE][travone][744][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:26:00][error][Activities Pickup Location][][][][][][RESPONSE][travone][704][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:27:54][error][Activities Pickup Location][][][][][][RESPONSE][travone][738][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:28:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][818][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:28:39][error][Activities Pickup Location][][][][][][RESPONSE][travone][699][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:30:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][670][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:30:56][error][Activities Pickup Location][][][][][][RESPONSE][travone][979][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:33:20][error][Activities Pickup Location][][][][][][RESPONSE][travone][667][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:33:23][error][Activities Pickup Location][][][][][][RESPONSE][travone][666][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:33:38][error][Activities Pickup Location][][][][][][RESPONSE][travone][782][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:34:03][error][Activities Pickup Location][][][][][][RESPONSE][travone][713][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:35:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][677][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:35:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][872][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:36:21][error][Activities Pickup Location][][][][][][RESPONSE][travone][691][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:36:21][error][Activities Pickup Location][][][][][][RESPONSE][travone][1063][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:36:23][error][Activities Pickup Location][][][][][][RESPONSE][travone][630][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:36:25][error][Activities Pickup Location][][][][][][RESPONSE][travone][696][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:37:02][error][Activities Pickup Location][][][][][][RESPONSE][travone][732][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:37:11][error][Activities Pickup Location][][][][][][RESPONSE][travone][759][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:37:14][error][Activities Pickup Location][][][][][][RESPONSE][travone][696][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:37:18][error][Activities Pickup Location][][][][][][RESPONSE][travone][670][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:37:59][error][Activities Pickup Location][][][][][][RESPONSE][travone][811][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:39:54][error][Activities Pickup Location][][][][][][RESPONSE][travone][803][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:40:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][798][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:44:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][741][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:44:54][error][Activities Pickup Location][][][][][][RESPONSE][travone][655][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 14:45:51][error][Activities Pickup Location][][][][][][RESPONSE][travone][763][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:18:45][error][Activities Pickup Location][][][][][][RESPONSE][travone][825][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:18:46][error][Activities Pickup Location][][][][][][RESPONSE][travone][1145][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:19:13][error][Activities Pickup Location][][][][][][RESPONSE][travone][890][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:19:18][error][Activities Pickup Location][][][][][][RESPONSE][travone][686][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:20:04][error][Activities Pickup Location][][][][][][RESPONSE][travone][702][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:20:20][error][Activities Pickup Location][][][][][][RESPONSE][travone][733][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:20:22][error][Activities Pickup Location][][][][][][RESPONSE][travone][643][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:22:11][error][Activities Pickup Location][][][][][][RESPONSE][travone][764][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:23:10][error][Activities Pickup Location][][][][][][RESPONSE][travone][691][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:23:14][error][Activities Pickup Location][][][][][][RESPONSE][travone][700][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:47:20][error][Activities Pickup Location][][][][][][RESPONSE][travone][1020][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:47:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][831][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:48:22][error][Activities Pickup Location][][][][][][RESPONSE][travone][1117][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:49:04][error][Activities Pickup Location][][][][][][RESPONSE][travone][757][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:49:06][error][Activities Pickup Location][][][][][][RESPONSE][travone][689][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:49:51][error][Activities Pickup Location][][][][][][RESPONSE][travone][739][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:49:53][error][Activities Pickup Location][][][][][][RESPONSE][travone][654][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:50:17][error][Activities Pickup Location][][][][][][RESPONSE][travone][727][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:50:20][error][Activities Pickup Location][][][][][][RESPONSE][travone][720][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:50:29][error][Activities Pickup Location][][][][][][RESPONSE][travone][785][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:51:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][704][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:51:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][699][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:51:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][723][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:51:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][765][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:51:52][error][Activities Pickup Location][][][][][][RESPONSE][travone][688][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:53:49][error][Activities Pickup Location][][][][][][RESPONSE][travone][687][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][729][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][757][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][647][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:13][error][Activities Pickup Location][][][][][][RESPONSE][travone][641][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:35][error][Activities Pickup Location][][][][][][RESPONSE][travone][688][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:54:47][error][Activities Pickup Location][][][][][][RESPONSE][travone][671][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:56:34][error][Activities Pickup Location][][][][][][RESPONSE][travone][744][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:55][error][Activities Pickup Location][][][][][][RESPONSE][travone][788][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:57][error][Activities Pickup Location][][][][][][RESPONSE][travone][646][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:58][error][Activities Pickup Location][][][][][][RESPONSE][travone][739][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:58][error][Activities Pickup Location][][][][][][RESPONSE][travone][749][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:58][error][Activities Pickup Location][][][][][][RESPONSE][travone][646][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:57:59][error][Activities Pickup Location][][][][][][RESPONSE][travone][1463][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 15:58:09][error][Activities Pickup Location][][][][][][RESPONSE][travone][733][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:03][error][Activities Pickup Location][][][][][][RESPONSE][travone][760][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:04][error][Activities Pickup Location][][][][][][RESPONSE][travone][772][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:04][error][Activities Pickup Location][][][][][][RESPONSE][travone][733][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:05][error][Activities Pickup Location][][][][][][RESPONSE][travone][941][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:05][error][Activities Pickup Location][][][][][][RESPONSE][travone][868][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:06][error][Activities Pickup Location][][][][][][RESPONSE][travone][1602][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:11][error][Activities Pickup Location][][][][][][RESPONSE][travone][698][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][664][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][753][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][667][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:00:12][error][Activities Pickup Location][][][][][][RESPONSE][travone][634][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:17:38][error][Activities Pickup Location][][][][][][RESPONSE][travone][737][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:17:54][error][Activities Pickup Location][][][][][][RESPONSE][travone][735][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:17:54][error][Activities Pickup Location][][][][][][RESPONSE][travone][724][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:33:38][error][Activities Pickup Location][][][][][][RESPONSE][travone][888][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:33:50][error][Activities Pickup Location][][][][][][RESPONSE][travone][714][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:35:59][error][Activities Pickup Location][][][][][][RESPONSE][travone][1417][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:37:35][error][Activities Pickup Location][][][][][][RESPONSE][travone][919][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:37:42][error][Activities Pickup Location][][][][][][RESPONSE][travone][714][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:38:14][error][Activities Pickup Location][][][][][][RESPONSE][travone][717][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:38:24][error][Activities Pickup Location][][][][][][RESPONSE][travone][770][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:38:36][error][Activities Pickup Location][][][][][][RESPONSE][travone][745][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
[2025-03-22 16:38:44][error][Activities Pickup Location][][][][][][RESPONSE][travone][672][200][post][][] : {"count":0,"status":"failed","result":{"message":"Request failed with status code 400","name":"Error","stack":"Error: Request failed with status code 400\n    at createError (/vms/nodejs/central_activities_uat/esb.js:22525:15)\n    at settle (/vms/nodejs/central_activities_uat/esb.js:41699:12)\n    at IncomingMessage.handleStreamEnd (/vms/nodejs/central_activities_uat/esb.js:107339:11)\n    at IncomingMessage.emit (node:events:538:35)\n    at endReadableNT (node:internal/streams/readable:1345:12)\n    at processTicksAndRejections (node:internal/process/task_queues:83:21)","config":{"url":"https://api.sandbox.viator.com/partner/locations/bulk","method":"post","data":"{\"locations\":[]}","headers":{"Accept":"application/json;version=2.0","Content-Type":"application/json","Accept-Language":"en-US","exp-api-key":"625a9cf1-c1d0-442d-adc7-6afb73a32b0c","User-Agent":"axios/0.20.0","Content-Length":16},"transformRequest":[null],"transformResponse":[null],"timeout":0,"xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN","maxContentLength":-1,"maxBodyLength":-1},"length":0}}
