"""
Log Format Detection Module
Automatically detects the format of log files without manual configuration.
"""

import json
import re
import chardet
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class LogFormatDetector:
    """Detects log file formats automatically."""
    
    def __init__(self):
        self.common_patterns = {
            'apache_access': r'^\d+\.\d+\.\d+\.\d+ - - \[.*?\] ".*?" \d+ \d+',
            'nginx_access': r'^\d+\.\d+\.\d+\.\d+ - - \[.*?\] ".*?" \d+ \d+ ".*?" ".*?"',
            'syslog': r'^[A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+\w+\s+.*',
            'timestamp_prefix': r'^\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}',
            'java_log': r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,\.]\d{3}\s+\[(INFO|DEBUG|WARN|ERROR|FATAL)\]',
            'python_log': r'^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3}\s+-\s+(INFO|DEBUG|WARNING|ERROR|CRITICAL)',
        }
        
        self.log_levels = ['DEBUG', 'INFO', 'WARN', 'WARNING', 'ERROR', 'FATAL', 'CRITICAL', 'TRACE']
    
    def detect_encoding(self, file_content: bytes) -> str:
        """Detect file encoding."""
        result = chardet.detect(file_content)
        return result.get('encoding', 'utf-8')
    
    def detect_format(self, file_path: str, sample_lines: List[str] = None) -> Dict:
        """
        Detect the format of a log file.
        
        Args:
            file_path: Path to the log file
            sample_lines: Optional list of sample lines to analyze
            
        Returns:
            Dictionary containing format information
        """
        if sample_lines is None:
            with open(file_path, 'rb') as f:
                raw_content = f.read(10000)  # Read first 10KB
                encoding = self.detect_encoding(raw_content)
                
            with open(file_path, 'r', encoding=encoding) as f:
                sample_lines = [f.readline().strip() for _ in range(50) if f.readline()]
        
        # Remove empty lines
        sample_lines = [line for line in sample_lines if line.strip()]
        
        if not sample_lines:
            return {'format': 'empty', 'confidence': 1.0}
        
        # Check for JSON format
        json_confidence = self._check_json_format(sample_lines)
        if json_confidence > 0.8:
            return {
                'format': 'json',
                'confidence': json_confidence,
                'structure': self._analyze_json_structure(sample_lines)
            }
        
        # Check for structured formats
        structured_result = self._check_structured_formats(sample_lines)
        if structured_result['confidence'] > 0.7:
            return structured_result
        
        # Check for delimited formats
        delimited_result = self._check_delimited_formats(sample_lines)
        if delimited_result['confidence'] > 0.6:
            return delimited_result
        
        # Default to plain text with timestamp detection
        return self._analyze_plain_text(sample_lines)
    
    def _check_json_format(self, lines: List[str]) -> float:
        """Check if lines are in JSON format."""
        json_count = 0
        total_lines = len(lines)
        
        for line in lines:
            try:
                json.loads(line)
                json_count += 1
            except (json.JSONDecodeError, ValueError):
                continue
        
        return json_count / total_lines if total_lines > 0 else 0
    
    def _analyze_json_structure(self, lines: List[str]) -> Dict:
        """Analyze JSON log structure."""
        fields = set()
        timestamp_fields = []
        level_fields = []
        message_fields = []
        
        for line in lines[:10]:  # Analyze first 10 JSON lines
            try:
                data = json.loads(line)
                if isinstance(data, dict):
                    fields.update(data.keys())
                    
                    # Look for timestamp fields
                    for key, value in data.items():
                        if any(ts_word in key.lower() for ts_word in ['time', 'date', 'timestamp', 'ts']):
                            timestamp_fields.append(key)
                        
                        # Look for log level fields
                        if any(level_word in key.lower() for level_word in ['level', 'severity', 'priority']):
                            level_fields.append(key)
                        
                        # Look for message fields
                        if any(msg_word in key.lower() for msg_word in ['message', 'msg', 'text', 'description']):
                            message_fields.append(key)
                            
            except (json.JSONDecodeError, ValueError):
                continue
        
        return {
            'fields': list(fields),
            'timestamp_fields': list(set(timestamp_fields)),
            'level_fields': list(set(level_fields)),
            'message_fields': list(set(message_fields))
        }
    
    def _check_structured_formats(self, lines: List[str]) -> Dict:
        """Check for known structured log formats."""
        best_match = {'format': 'unknown', 'confidence': 0.0, 'pattern': None}
        
        for format_name, pattern in self.common_patterns.items():
            matches = 0
            for line in lines:
                if re.match(pattern, line):
                    matches += 1
            
            confidence = matches / len(lines) if lines else 0
            if confidence > best_match['confidence']:
                best_match = {
                    'format': format_name,
                    'confidence': confidence,
                    'pattern': pattern
                }
        
        return best_match
    
    def _check_delimited_formats(self, lines: List[str]) -> Dict:
        """Check for delimited formats (CSV, TSV, etc.)."""
        delimiters = [',', '\t', '|', ';', ' ']
        best_delimiter = None
        best_confidence = 0.0
        
        for delimiter in delimiters:
            field_counts = []
            for line in lines:
                fields = line.split(delimiter)
                field_counts.append(len(fields))
            
            if field_counts:
                # Check consistency of field count
                most_common_count = max(set(field_counts), key=field_counts.count)
                consistency = field_counts.count(most_common_count) / len(field_counts)
                
                # Must have at least 2 fields and good consistency
                if most_common_count >= 2 and consistency > best_confidence:
                    best_confidence = consistency
                    best_delimiter = delimiter
        
        if best_confidence > 0.6:
            return {
                'format': 'delimited',
                'confidence': best_confidence,
                'delimiter': best_delimiter,
                'field_count': most_common_count
            }
        
        return {'format': 'unknown', 'confidence': 0.0}
    
    def _analyze_plain_text(self, lines: List[str]) -> Dict:
        """Analyze plain text logs for patterns."""
        has_timestamps = 0
        has_log_levels = 0
        
        for line in lines:
            # Check for timestamps
            if re.search(r'\d{4}-\d{2}-\d{2}|\d{2}/\d{2}/\d{4}|\d{2}-\d{2}-\d{4}', line):
                has_timestamps += 1
            
            # Check for log levels
            if any(level in line.upper() for level in self.log_levels):
                has_log_levels += 1
        
        timestamp_ratio = has_timestamps / len(lines) if lines else 0
        level_ratio = has_log_levels / len(lines) if lines else 0
        
        return {
            'format': 'plain_text',
            'confidence': 0.5,  # Default confidence for plain text
            'has_timestamps': timestamp_ratio > 0.5,
            'has_log_levels': level_ratio > 0.3,
            'timestamp_ratio': timestamp_ratio,
            'level_ratio': level_ratio
        }
