"""
Log Parser Module
Parses log files based on detected format and extracts structured data.
"""

import json
import re
import csv
import io
from datetime import datetime
from typing import Dict, List, Optional, Any, Iterator
from .detector import LogFormatDetector


class LogEntry:
    """Represents a single log entry."""
    
    def __init__(self, raw_line: str, line_number: int):
        self.raw_line = raw_line
        self.line_number = line_number
        self.timestamp: Optional[datetime] = None
        self.level: Optional[str] = None
        self.message: Optional[str] = None
        self.fields: Dict[str, Any] = {}
        self.parsed: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert log entry to dictionary."""
        return {
            'line_number': self.line_number,
            'raw_line': self.raw_line,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'level': self.level,
            'message': self.message,
            'fields': self.fields,
            'parsed': self.parsed
        }


class LogParser:
    """Main log parser class."""
    
    def __init__(self):
        self.detector = LogFormatDetector()
        self.timestamp_patterns = [
            r'(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}(?:\.\d{3})?)',
            r'(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2}:\d{2})',
            r'(\d{2}-\d{2}-\d{4}\s+\d{2}:\d{2}:\d{2})',
            r'([A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})',
        ]
        self.level_pattern = r'\b(DEBUG|INFO|WARN|WARNING|ERROR|FATAL|CRITICAL|TRACE)\b'
    
    def parse_file(self, file_path: str, max_lines: int = None) -> Dict[str, Any]:
        """
        Parse a log file and return structured data.
        
        Args:
            file_path: Path to the log file
            max_lines: Maximum number of lines to parse (None for all)
            
        Returns:
            Dictionary containing parsed log data and metadata
        """
        # Detect format first
        format_info = self.detector.detect_format(file_path)
        
        # Read and parse the file
        entries = []
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_number = 0
            for line in f:
                line_number += 1
                if max_lines and line_number > max_lines:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                entry = self._parse_line(line, line_number, format_info)
                entries.append(entry)
        
        # Generate summary statistics
        stats = self._generate_stats(entries)
        
        return {
            'format_info': format_info,
            'entries': [entry.to_dict() for entry in entries],
            'stats': stats,
            'total_lines': line_number,
            'parsed_lines': len(entries)
        }
    
    def _parse_line(self, line: str, line_number: int, format_info: Dict) -> LogEntry:
        """Parse a single log line based on format."""
        entry = LogEntry(line, line_number)
        
        format_type = format_info.get('format', 'plain_text')
        
        if format_type == 'json':
            self._parse_json_line(entry, format_info)
        elif format_type in ['apache_access', 'nginx_access']:
            self._parse_access_log(entry, format_type)
        elif format_type == 'syslog':
            self._parse_syslog(entry)
        elif format_type == 'delimited':
            self._parse_delimited_line(entry, format_info)
        else:
            self._parse_plain_text_line(entry)
        
        return entry
    
    def _parse_json_line(self, entry: LogEntry, format_info: Dict):
        """Parse JSON log line."""
        try:
            data = json.loads(entry.raw_line)
            entry.fields = data
            entry.parsed = True
            
            # Extract common fields
            structure = format_info.get('structure', {})
            
            # Extract timestamp
            for ts_field in structure.get('timestamp_fields', []):
                if ts_field in data:
                    entry.timestamp = self._parse_timestamp(str(data[ts_field]))
                    break
            
            # Extract log level
            for level_field in structure.get('level_fields', []):
                if level_field in data:
                    entry.level = str(data[level_field]).upper()
                    break
            
            # Extract message
            for msg_field in structure.get('message_fields', []):
                if msg_field in data:
                    entry.message = str(data[msg_field])
                    break
                    
        except (json.JSONDecodeError, ValueError):
            entry.parsed = False
            self._parse_plain_text_line(entry)
    
    def _parse_access_log(self, entry: LogEntry, format_type: str):
        """Parse Apache/Nginx access logs."""
        if format_type == 'apache_access':
            # Apache Common Log Format
            pattern = r'^(\S+) \S+ \S+ \[(.*?)\] "(.*?)" (\d+) (\d+)'
        else:
            # Nginx access log
            pattern = r'^(\S+) - - \[(.*?)\] "(.*?)" (\d+) (\d+) "(.*?)" "(.*?)"'
        
        match = re.match(pattern, entry.raw_line)
        if match:
            entry.parsed = True
            groups = match.groups()
            entry.fields['ip'] = groups[0]
            entry.timestamp = self._parse_timestamp(groups[1])
            entry.fields['request'] = groups[2]
            entry.fields['status'] = int(groups[3])
            entry.fields['size'] = int(groups[4])
            
            if format_type == 'nginx_access' and len(groups) >= 7:
                entry.fields['referer'] = groups[5]
                entry.fields['user_agent'] = groups[6]
            
            # Set level based on status code
            status = entry.fields['status']
            if status >= 500:
                entry.level = 'ERROR'
            elif status >= 400:
                entry.level = 'WARN'
            else:
                entry.level = 'INFO'
        else:
            self._parse_plain_text_line(entry)
    
    def _parse_syslog(self, entry: LogEntry):
        """Parse syslog format."""
        pattern = r'^([A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.*)'
        match = re.match(pattern, entry.raw_line)
        
        if match:
            entry.parsed = True
            timestamp_str, hostname, message = match.groups()
            entry.timestamp = self._parse_timestamp(timestamp_str)
            entry.fields['hostname'] = hostname
            entry.message = message
            
            # Extract log level from message
            level_match = re.search(self.level_pattern, message, re.IGNORECASE)
            if level_match:
                entry.level = level_match.group(1).upper()
        else:
            self._parse_plain_text_line(entry)
    
    def _parse_delimited_line(self, entry: LogEntry, format_info: Dict):
        """Parse delimited (CSV/TSV) log line."""
        delimiter = format_info.get('delimiter', ',')
        fields = entry.raw_line.split(delimiter)
        
        entry.parsed = True
        entry.fields = {f'field_{i}': field.strip() for i, field in enumerate(fields)}
        
        # Try to identify timestamp and level in fields
        for field in fields:
            field = field.strip()
            if not entry.timestamp:
                entry.timestamp = self._parse_timestamp(field)
            
            if not entry.level:
                level_match = re.search(self.level_pattern, field, re.IGNORECASE)
                if level_match:
                    entry.level = level_match.group(1).upper()
    
    def _parse_plain_text_line(self, entry: LogEntry):
        """Parse plain text log line."""
        entry.parsed = True
        entry.message = entry.raw_line
        
        # Extract timestamp
        for pattern in self.timestamp_patterns:
            match = re.search(pattern, entry.raw_line)
            if match:
                entry.timestamp = self._parse_timestamp(match.group(1))
                break
        
        # Extract log level
        level_match = re.search(self.level_pattern, entry.raw_line, re.IGNORECASE)
        if level_match:
            entry.level = level_match.group(1).upper()
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string to datetime object."""
        timestamp_formats = [
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%m/%d/%Y %H:%M:%S',
            '%d-%m-%Y %H:%M:%S',
            '%b %d %H:%M:%S',
            '%d/%b/%Y:%H:%M:%S',
        ]
        
        for fmt in timestamp_formats:
            try:
                return datetime.strptime(timestamp_str.strip(), fmt)
            except ValueError:
                continue
        
        return None
    
    def _generate_stats(self, entries: List[LogEntry]) -> Dict[str, Any]:
        """Generate statistics from parsed entries."""
        total_entries = len(entries)
        parsed_entries = sum(1 for entry in entries if entry.parsed)
        
        # Count by log level
        level_counts = {}
        for entry in entries:
            if entry.level:
                level_counts[entry.level] = level_counts.get(entry.level, 0) + 1
        
        # Time range
        timestamps = [entry.timestamp for entry in entries if entry.timestamp]
        time_range = None
        if timestamps:
            time_range = {
                'start': min(timestamps).isoformat(),
                'end': max(timestamps).isoformat()
            }
        
        return {
            'total_entries': total_entries,
            'parsed_entries': parsed_entries,
            'parse_success_rate': parsed_entries / total_entries if total_entries > 0 else 0,
            'level_counts': level_counts,
            'time_range': time_range,
            'has_timestamps': len(timestamps) > 0,
            'timestamp_coverage': len(timestamps) / total_entries if total_entries > 0 else 0
        }
