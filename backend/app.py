"""
Flask Application for Log Analyzer
Main entry point for the backend API server.
"""

import os
import json
import tempfile
from datetime import datetime
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import pandas as pd

from log_parser.parser import LogParser
from analysis.analyzer import LogAnalyzer


app = Flask(__name__)
CORS(app)

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()

# Initialize components
log_parser = LogParser()
log_analyzer = LogAnalyzer()

# Store analysis results in memory (in production, use a database)
analysis_cache = {}


@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })


@app.route('/api/upload', methods=['POST'])
def upload_file():
    """
    Upload and analyze a log file.
    Returns analysis results immediately for small files,
    or a job ID for large files.
    """
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    # Get optional parameters
    max_lines = request.form.get('max_lines', type=int)
    
    try:
        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Parse the log file
        parsed_data = log_parser.parse_file(file_path, max_lines=max_lines)
        
        # Analyze the parsed data
        analysis_results = log_analyzer.analyze_logs(parsed_data)
        
        # Generate a unique ID for this analysis
        analysis_id = f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store results in cache
        analysis_cache[analysis_id] = {
            'filename': filename,
            'upload_time': datetime.now().isoformat(),
            'parsed_data': parsed_data,
            'analysis_results': analysis_results
        }
        
        # Clean up temporary file
        os.remove(file_path)
        
        return jsonify({
            'analysis_id': analysis_id,
            'filename': filename,
            'status': 'completed',
            'summary': analysis_results['summary'],
            'insights': analysis_results['insights'][:5],  # First 5 insights
            'recommendations': analysis_results['recommendations'][:5]  # First 5 recommendations
        })
        
    except Exception as e:
        # Clean up on error
        if 'file_path' in locals() and os.path.exists(file_path):
            os.remove(file_path)
        
        return jsonify({'error': f'Failed to process file: {str(e)}'}), 500


@app.route('/api/analysis/<analysis_id>', methods=['GET'])
def get_analysis(analysis_id):
    """Get complete analysis results by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    return jsonify({
        'analysis_id': analysis_id,
        'filename': cached_data['filename'],
        'upload_time': cached_data['upload_time'],
        'analysis_results': cached_data['analysis_results']
    })


@app.route('/api/analysis/<analysis_id>/summary', methods=['GET'])
def get_analysis_summary(analysis_id):
    """Get analysis summary by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    analysis_results = cached_data['analysis_results']
    
    return jsonify({
        'analysis_id': analysis_id,
        'filename': cached_data['filename'],
        'summary': analysis_results['summary'],
        'insights': analysis_results['insights'],
        'recommendations': analysis_results['recommendations']
    })


@app.route('/api/analysis/<analysis_id>/errors', methods=['GET'])
def get_error_analysis(analysis_id):
    """Get error analysis by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    return jsonify({
        'analysis_id': analysis_id,
        'error_analysis': cached_data['analysis_results']['error_analysis']
    })


@app.route('/api/analysis/<analysis_id>/patterns', methods=['GET'])
def get_pattern_analysis(analysis_id):
    """Get pattern analysis by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    return jsonify({
        'analysis_id': analysis_id,
        'pattern_analysis': cached_data['analysis_results']['pattern_analysis']
    })


@app.route('/api/analysis/<analysis_id>/timeline', methods=['GET'])
def get_time_analysis(analysis_id):
    """Get time-based analysis by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    return jsonify({
        'analysis_id': analysis_id,
        'time_analysis': cached_data['analysis_results']['time_analysis']
    })


@app.route('/api/analysis/<analysis_id>/anomalies', methods=['GET'])
def get_anomaly_analysis(analysis_id):
    """Get anomaly detection results by ID."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    cached_data = analysis_cache[analysis_id]
    return jsonify({
        'analysis_id': analysis_id,
        'anomaly_detection': cached_data['analysis_results']['anomaly_detection']
    })


@app.route('/api/analysis/<analysis_id>/search', methods=['GET'])
def search_logs(analysis_id):
    """Search through log entries."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    query = request.args.get('q', '').lower()
    level = request.args.get('level', '').upper()
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    
    cached_data = analysis_cache[analysis_id]
    entries = cached_data['parsed_data']['entries']
    
    # Filter entries
    filtered_entries = []
    for entry in entries:
        # Text search
        if query and query not in entry.get('raw_line', '').lower():
            continue
        
        # Level filter
        if level and entry.get('level') != level:
            continue
        
        # Date range filter
        if start_date or end_date:
            entry_timestamp = entry.get('timestamp')
            if entry_timestamp:
                try:
                    entry_date = datetime.fromisoformat(entry_timestamp.replace('Z', '+00:00'))
                    if start_date and entry_date < datetime.fromisoformat(start_date):
                        continue
                    if end_date and entry_date > datetime.fromisoformat(end_date):
                        continue
                except ValueError:
                    continue
        
        filtered_entries.append(entry)
    
    # Pagination
    total = len(filtered_entries)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_entries = filtered_entries[start_idx:end_idx]
    
    return jsonify({
        'analysis_id': analysis_id,
        'entries': paginated_entries,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        },
        'filters': {
            'query': query,
            'level': level,
            'start_date': start_date,
            'end_date': end_date
        }
    })


@app.route('/api/analysis/<analysis_id>/export', methods=['GET'])
def export_analysis(analysis_id):
    """Export analysis results as JSON or CSV."""
    if analysis_id not in analysis_cache:
        return jsonify({'error': 'Analysis not found'}), 404
    
    format_type = request.args.get('format', 'json').lower()
    cached_data = analysis_cache[analysis_id]
    
    if format_type == 'csv':
        # Export log entries as CSV
        entries = cached_data['parsed_data']['entries']
        df = pd.DataFrame(entries)
        
        # Create temporary CSV file
        csv_filename = f"{analysis_id}_logs.csv"
        csv_path = os.path.join(app.config['UPLOAD_FOLDER'], csv_filename)
        df.to_csv(csv_path, index=False)
        
        return send_file(csv_path, as_attachment=True, download_name=csv_filename)
    
    else:
        # Export as JSON
        json_filename = f"{analysis_id}_analysis.json"
        json_path = os.path.join(app.config['UPLOAD_FOLDER'], json_filename)
        
        with open(json_path, 'w') as f:
            json.dump(cached_data['analysis_results'], f, indent=2)
        
        return send_file(json_path, as_attachment=True, download_name=json_filename)


@app.route('/api/analyses', methods=['GET'])
def list_analyses():
    """List all cached analyses."""
    analyses = []
    for analysis_id, data in analysis_cache.items():
        analyses.append({
            'analysis_id': analysis_id,
            'filename': data['filename'],
            'upload_time': data['upload_time'],
            'total_entries': data['analysis_results']['summary']['total_entries'],
            'health_status': data['analysis_results']['summary']['health_status']
        })
    
    return jsonify({'analyses': analyses})


@app.errorhandler(413)
def file_too_large(error):
    """Handle file too large error."""
    return jsonify({'error': 'File too large. Maximum size is 100MB.'}), 413


@app.errorhandler(500)
def internal_error(error):
    """Handle internal server errors."""
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
