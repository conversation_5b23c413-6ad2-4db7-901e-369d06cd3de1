"""
Log Analysis Engine
Provides intelligent analysis of parsed log data including error detection,
pattern recognition, anomaly detection, and trend analysis.
"""

import re
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
# Removed numpy and sklearn dependencies for simplified deployment


class LogAnalyzer:
    """Main log analysis engine."""
    
    def __init__(self):
        self.error_keywords = [
            'error', 'exception', 'fail', 'crash', 'abort', 'timeout',
            'refused', 'denied', 'invalid', 'corrupt', 'missing',
            'unauthorized', 'forbidden', 'not found', 'unavailable'
        ]
        
        self.warning_keywords = [
            'warn', 'warning', 'deprecated', 'slow', 'retry',
            'fallback', 'degraded', 'limited', 'throttle'
        ]
        
        self.performance_keywords = [
            'slow', 'timeout', 'latency', 'delay', 'queue',
            'memory', 'cpu', 'disk', 'network', 'bandwidth'
        ]
    
    def analyze_logs(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of parsed log data.
        
        Args:
            parsed_data: Output from LogParser.parse_file()
            
        Returns:
            Dictionary containing analysis results
        """
        entries = parsed_data['entries']
        stats = parsed_data['stats']
        
        analysis_results = {
            'summary': self._generate_summary(entries, stats),
            'error_analysis': self._analyze_errors(entries),
            'pattern_analysis': self._analyze_patterns(entries),
            'time_analysis': self._analyze_time_patterns(entries),
            'anomaly_detection': self._detect_anomalies(entries),
            'performance_analysis': self._analyze_performance(entries),
            'customer_analysis': self._analyze_customers(entries),
            'application_analysis': self._analyze_applications(entries),
            'recommendations': [],
            'insights': []
        }
        
        # Generate insights and recommendations
        analysis_results['insights'] = self._generate_insights(analysis_results, stats)
        analysis_results['recommendations'] = self._generate_recommendations(analysis_results, stats)
        
        return analysis_results
    
    def _generate_summary(self, entries: List[Dict], stats: Dict) -> Dict[str, Any]:
        """Generate high-level summary of log analysis."""
        total_entries = len(entries)
        error_count = sum(1 for entry in entries if entry.get('level') == 'ERROR')
        warning_count = sum(1 for entry in entries if entry.get('level') in ['WARN', 'WARNING'])
        
        # Calculate error rate
        error_rate = (error_count / total_entries * 100) if total_entries > 0 else 0
        warning_rate = (warning_count / total_entries * 100) if total_entries > 0 else 0
        
        # Determine overall health
        if error_rate > 10:
            health_status = 'Critical'
        elif error_rate > 5 or warning_rate > 20:
            health_status = 'Warning'
        elif error_rate > 1 or warning_rate > 10:
            health_status = 'Caution'
        else:
            health_status = 'Healthy'
        
        return {
            'total_entries': total_entries,
            'error_count': error_count,
            'warning_count': warning_count,
            'error_rate': round(error_rate, 2),
            'warning_rate': round(warning_rate, 2),
            'health_status': health_status,
            'time_span': stats.get('time_range'),
            'most_common_level': max(stats.get('level_counts', {}), 
                                   key=stats.get('level_counts', {}).get, default='INFO')
        }
    
    def _analyze_errors(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze error patterns and frequencies."""
        error_entries = [entry for entry in entries if entry.get('level') == 'ERROR']
        
        if not error_entries:
            return {
                'total_errors': 0,
                'error_patterns': [],
                'error_timeline': [],
                'top_error_messages': []
            }
        
        # Extract error messages and patterns
        error_messages = []
        error_timeline = []
        
        for entry in error_entries:
            message = entry.get('message', entry.get('raw_line', ''))
            error_messages.append(message)
            
            if entry.get('timestamp'):
                error_timeline.append({
                    'timestamp': entry['timestamp'],
                    'message': message[:100] + '...' if len(message) > 100 else message
                })
        
        # Find common error patterns
        error_patterns = self._find_error_patterns(error_messages)
        
        # Get top error messages
        message_counts = Counter(error_messages)
        top_error_messages = [
            {'message': msg[:200] + '...' if len(msg) > 200 else msg, 'count': count}
            for msg, count in message_counts.most_common(10)
        ]
        
        return {
            'total_errors': len(error_entries),
            'error_patterns': error_patterns,
            'error_timeline': sorted(error_timeline, key=lambda x: x['timestamp'])[-50:],  # Last 50 errors
            'top_error_messages': top_error_messages
        }
    
    def _find_error_patterns(self, error_messages: List[str]) -> List[Dict[str, Any]]:
        """Find common patterns in error messages."""
        patterns = []
        
        # Common error patterns to look for
        pattern_regexes = [
            (r'(\w+Exception)', 'Exception Type'),
            (r'(HTTP \d{3})', 'HTTP Status Code'),
            (r'(timeout|timed out)', 'Timeout Errors'),
            (r'(connection \w+)', 'Connection Issues'),
            (r'(file not found|no such file)', 'File Not Found'),
            (r'(permission denied|access denied)', 'Permission Issues'),
            (r'(out of memory|memory)', 'Memory Issues'),
            (r'(database|sql|query)', 'Database Issues'),
        ]
        
        for pattern, description in pattern_regexes:
            matches = []
            for message in error_messages:
                match = re.search(pattern, message, re.IGNORECASE)
                if match:
                    matches.append(match.group(1))
            
            if matches:
                match_counts = Counter(matches)
                patterns.append({
                    'pattern': description,
                    'count': len(matches),
                    'percentage': round(len(matches) / len(error_messages) * 100, 2),
                    'examples': list(match_counts.keys())[:5]
                })
        
        return sorted(patterns, key=lambda x: x['count'], reverse=True)
    
    def _analyze_patterns(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze general patterns in log data."""
        # Analyze by log level
        level_distribution = Counter(entry.get('level', 'UNKNOWN') for entry in entries)
        
        # Analyze message patterns using TF-IDF
        messages = [entry.get('message', entry.get('raw_line', '')) for entry in entries]
        
        # Find frequent terms
        frequent_terms = self._find_frequent_terms(messages)
        
        # Analyze IP patterns (for access logs)
        ip_patterns = self._analyze_ip_patterns(entries)
        
        # Analyze status code patterns (for access logs)
        status_patterns = self._analyze_status_patterns(entries)
        
        return {
            'level_distribution': dict(level_distribution),
            'frequent_terms': frequent_terms,
            'ip_patterns': ip_patterns,
            'status_patterns': status_patterns
        }
    
    def _find_frequent_terms(self, messages: List[str], max_features: int = 50) -> List[Dict[str, Any]]:
        """Find frequently occurring terms in log messages."""
        if not messages:
            return []
        
        try:
            # Use TF-IDF to find important terms
            vectorizer = TfidfVectorizer(
                max_features=max_features,
                stop_words='english',
                ngram_range=(1, 2),
                min_df=2
            )
            
            tfidf_matrix = vectorizer.fit_transform(messages)
            feature_names = vectorizer.get_feature_names_out()
            
            # Calculate average TF-IDF scores
            mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
            
            # Create list of terms with scores
            terms_with_scores = [
                {'term': feature_names[i], 'score': float(mean_scores[i])}
                for i in range(len(feature_names))
            ]
            
            return sorted(terms_with_scores, key=lambda x: x['score'], reverse=True)[:20]
            
        except Exception:
            # Fallback to simple word counting
            all_words = ' '.join(messages).lower().split()
            word_counts = Counter(all_words)
            return [
                {'term': word, 'count': count}
                for word, count in word_counts.most_common(20)
                if len(word) > 3
            ]
    
    def _analyze_ip_patterns(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze IP address patterns in logs."""
        ips = []
        for entry in entries:
            fields = entry.get('fields', {})
            if 'ip' in fields:
                ips.append(fields['ip'])
            else:
                # Try to extract IP from raw line
                ip_match = re.search(r'\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b', 
                                   entry.get('raw_line', ''))
                if ip_match:
                    ips.append(ip_match.group(1))
        
        if not ips:
            return {'total_unique_ips': 0, 'top_ips': []}
        
        ip_counts = Counter(ips)
        return {
            'total_unique_ips': len(ip_counts),
            'top_ips': [
                {'ip': ip, 'count': count}
                for ip, count in ip_counts.most_common(10)
            ]
        }
    
    def _analyze_status_patterns(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze HTTP status code patterns."""
        status_codes = []
        for entry in entries:
            fields = entry.get('fields', {})
            if 'status' in fields:
                status_codes.append(fields['status'])
        
        if not status_codes:
            return {'status_distribution': {}}
        
        status_counts = Counter(status_codes)
        
        # Categorize status codes
        categories = {
            '2xx': sum(count for status, count in status_counts.items() if 200 <= status < 300),
            '3xx': sum(count for status, count in status_counts.items() if 300 <= status < 400),
            '4xx': sum(count for status, count in status_counts.items() if 400 <= status < 500),
            '5xx': sum(count for status, count in status_counts.items() if 500 <= status < 600),
        }
        
        return {
            'status_distribution': dict(status_counts),
            'status_categories': categories
        }

    def _analyze_time_patterns(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze temporal patterns in log data."""
        timestamps = []
        for entry in entries:
            if entry.get('timestamp'):
                try:
                    ts = datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00'))
                    timestamps.append(ts)
                except ValueError:
                    continue

        if not timestamps:
            return {'hourly_distribution': {}, 'daily_distribution': {}}

        # Analyze hourly patterns
        hourly_counts = defaultdict(int)
        daily_counts = defaultdict(int)

        for ts in timestamps:
            hourly_counts[ts.hour] += 1
            daily_counts[ts.strftime('%Y-%m-%d')] += 1

        # Find peak hours
        peak_hour = max(hourly_counts, key=hourly_counts.get) if hourly_counts else 0

        return {
            'hourly_distribution': dict(hourly_counts),
            'daily_distribution': dict(daily_counts),
            'peak_hour': peak_hour,
            'total_days': len(daily_counts),
            'avg_entries_per_hour': sum(hourly_counts.values()) / 24 if hourly_counts else 0
        }

    def _detect_anomalies(self, entries: List[Dict]) -> Dict[str, Any]:
        """Detect anomalies in log data."""
        anomalies = []

        # Detect error spikes
        error_spikes = self._detect_error_spikes(entries)
        if error_spikes:
            anomalies.extend(error_spikes)

        # Detect unusual patterns
        unusual_patterns = self._detect_unusual_patterns(entries)
        if unusual_patterns:
            anomalies.extend(unusual_patterns)

        return {
            'total_anomalies': len(anomalies),
            'anomalies': anomalies[:20]  # Return top 20 anomalies
        }

    def _detect_error_spikes(self, entries: List[Dict]) -> List[Dict[str, Any]]:
        """Detect spikes in error rates."""
        # Group entries by time windows (e.g., 5-minute intervals)
        time_windows = defaultdict(list)

        for entry in entries:
            if entry.get('timestamp'):
                try:
                    ts = datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00'))
                    # Round to 5-minute intervals
                    window = ts.replace(minute=(ts.minute // 5) * 5, second=0, microsecond=0)
                    time_windows[window].append(entry)
                except ValueError:
                    continue

        spikes = []
        error_rates = []

        for window, window_entries in time_windows.items():
            total = len(window_entries)
            errors = sum(1 for e in window_entries if e.get('level') == 'ERROR')
            error_rate = errors / total if total > 0 else 0
            error_rates.append(error_rate)

            # Detect spike (error rate > 2 standard deviations above mean)
            if len(error_rates) > 10:  # Need some history
                mean_rate = np.mean(error_rates[:-1])
                std_rate = np.std(error_rates[:-1])

                if error_rate > mean_rate + 2 * std_rate and errors > 5:
                    spikes.append({
                        'type': 'error_spike',
                        'timestamp': window.isoformat(),
                        'description': f'Error spike detected: {errors} errors in 5 minutes ({error_rate:.1%} error rate)',
                        'severity': 'high' if error_rate > 0.5 else 'medium'
                    })

        return spikes

    def _detect_unusual_patterns(self, entries: List[Dict]) -> List[Dict[str, Any]]:
        """Detect unusual patterns in log messages."""
        patterns = []

        # Look for repeated error messages in short time spans
        recent_errors = defaultdict(list)

        for entry in entries:
            if entry.get('level') == 'ERROR' and entry.get('timestamp'):
                message = entry.get('message', entry.get('raw_line', ''))[:100]
                try:
                    ts = datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00'))
                    recent_errors[message].append(ts)
                except ValueError:
                    continue

        for message, timestamps in recent_errors.items():
            if len(timestamps) >= 10:  # 10 or more occurrences
                # Check if they occurred within a short time span
                timestamps.sort()
                time_span = timestamps[-1] - timestamps[0]

                if time_span < timedelta(minutes=30):  # Within 30 minutes
                    patterns.append({
                        'type': 'repeated_error',
                        'description': f'Repeated error: "{message}" occurred {len(timestamps)} times in {time_span}',
                        'severity': 'high',
                        'count': len(timestamps)
                    })

        return patterns

    def _generate_insights(self, analysis: Dict[str, Any], stats: Dict) -> List[str]:
        """Generate human-readable insights from analysis."""
        insights = []
        summary = analysis['summary']

        # Health status insights
        if summary['health_status'] == 'Critical':
            insights.append(f"🚨 Critical: High error rate of {summary['error_rate']}% detected. Immediate attention required.")
        elif summary['health_status'] == 'Warning':
            insights.append(f"⚠️ Warning: Elevated error rate of {summary['error_rate']}% or warning rate of {summary['warning_rate']}%.")
        elif summary['health_status'] == 'Healthy':
            insights.append(f"✅ System appears healthy with low error rate of {summary['error_rate']}%.")

        # Error analysis insights
        error_analysis = analysis['error_analysis']
        if error_analysis['total_errors'] > 0:
            insights.append(f"Found {error_analysis['total_errors']} errors in the logs.")

            if error_analysis['error_patterns']:
                top_pattern = error_analysis['error_patterns'][0]
                insights.append(f"Most common error pattern: {top_pattern['pattern']} ({top_pattern['count']} occurrences)")

        # Time pattern insights
        time_analysis = analysis['time_analysis']
        if time_analysis.get('peak_hour') is not None:
            insights.append(f"Peak activity occurs at hour {time_analysis['peak_hour']}:00.")

        # Anomaly insights
        anomaly_analysis = analysis['anomaly_detection']
        if anomaly_analysis['total_anomalies'] > 0:
            insights.append(f"⚠️ Detected {anomaly_analysis['total_anomalies']} anomalies that require attention.")

        # Performance insights
        if 'performance_analysis' in analysis:
            perf_analysis = analysis['performance_analysis']
            if perf_analysis['total_requests'] > 0:
                avg_time = perf_analysis['avg_response_time']
                slow_count = perf_analysis['slow_request_count']

                if avg_time > 2000:
                    insights.append(f"🐌 High average response time: {avg_time}ms. Consider performance optimization.")
                elif avg_time < 500:
                    insights.append(f"⚡ Excellent performance: Average response time is {avg_time}ms.")

                if slow_count > 0:
                    insights.append(f"⏱️ Found {slow_count} slow requests (>5 seconds) that need investigation.")

        # Customer insights
        if 'customer_analysis' in analysis:
            customer_analysis = analysis['customer_analysis']
            if customer_analysis['total_unique_customers'] > 0:
                insights.append(f"👥 Serving {customer_analysis['total_unique_customers']} unique customers.")

                if customer_analysis['error_prone_customers']:
                    top_error_customer = customer_analysis['error_prone_customers'][0]
                    insights.append(f"🚨 Customer {top_error_customer['customer_id']} has {top_error_customer['error_count']} errors.")

        # Application insights
        if 'application_analysis' in analysis:
            app_analysis = analysis['application_analysis']
            if app_analysis['total_applications'] > 1:
                insights.append(f"📱 Monitoring {app_analysis['total_applications']} different applications.")

                # Find app with highest error rate
                apps = app_analysis['application_summary']
                if apps:
                    highest_error_app = max(apps, key=lambda x: x['error_rate'])
                    if highest_error_app['error_rate'] > 5:
                        insights.append(f"⚠️ Application '{highest_error_app['app_name']}' has high error rate: {highest_error_app['error_rate']}%")

        return insights

    def _generate_recommendations(self, analysis: Dict[str, Any], stats: Dict) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        summary = analysis['summary']

        # Error rate recommendations
        if summary['error_rate'] > 10:
            recommendations.append("Immediate action required: Investigate and resolve critical errors.")
            recommendations.append("Consider implementing error monitoring and alerting.")
        elif summary['error_rate'] > 1:
            recommendations.append("Monitor error trends and investigate recurring issues.")

        # Pattern-based recommendations
        error_analysis = analysis['error_analysis']
        for pattern in error_analysis.get('error_patterns', [])[:3]:
            if 'timeout' in pattern['pattern'].lower():
                recommendations.append("Consider increasing timeout values or optimizing slow operations.")
            elif 'connection' in pattern['pattern'].lower():
                recommendations.append("Review network connectivity and connection pool settings.")
            elif 'memory' in pattern['pattern'].lower():
                recommendations.append("Investigate memory usage and consider increasing memory allocation.")
            elif 'database' in pattern['pattern'].lower():
                recommendations.append("Review database performance and query optimization.")

        # Anomaly recommendations
        anomaly_analysis = analysis['anomaly_detection']
        if anomaly_analysis['total_anomalies'] > 0:
            recommendations.append("Investigate detected anomalies for potential issues.")
            recommendations.append("Consider setting up automated anomaly detection and alerting.")

        # General recommendations
        if not stats.get('has_timestamps'):
            recommendations.append("Consider adding timestamps to logs for better analysis.")

        if summary['total_entries'] > 10000:
            recommendations.append("Consider implementing log rotation and archival for large log files.")

        # Performance recommendations
        if 'performance_analysis' in analysis:
            perf_analysis = analysis['performance_analysis']
            if perf_analysis['avg_response_time'] > 2000:
                recommendations.append("Optimize application performance to reduce response times.")

            if perf_analysis['slow_request_count'] > perf_analysis['total_requests'] * 0.05:
                recommendations.append("Investigate and optimize slow requests (>5% of requests are slow).")

        # Customer recommendations
        if 'customer_analysis' in analysis:
            customer_analysis = analysis['customer_analysis']
            if customer_analysis['error_prone_customers']:
                recommendations.append("Provide additional support to customers experiencing frequent errors.")

        # Application recommendations
        if 'application_analysis' in analysis:
            app_analysis = analysis['application_analysis']
            for app in app_analysis['application_summary']:
                if app['error_rate'] > 10:
                    recommendations.append(f"Investigate high error rate in application '{app['app_name']}'.")

        return recommendations

    def _analyze_performance(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze performance metrics from enterprise structured logs."""
        response_times = []
        slow_requests = []
        performance_by_app = defaultdict(list)
        performance_by_module = defaultdict(list)

        for entry in entries:
            fields = entry.get('fields', {})
            response_time = fields.get('response_time', 0)

            if isinstance(response_time, (int, float)) and response_time > 0:
                response_times.append(response_time)

                app_name = fields.get('app_name', 'unknown')
                module = fields.get('module', 'unknown')

                performance_by_app[app_name].append(response_time)
                performance_by_module[module].append(response_time)

                # Flag slow requests (>5 seconds)
                if response_time > 5000:
                    slow_requests.append({
                        'response_time': response_time,
                        'app_name': app_name,
                        'module': module,
                        'request_id': fields.get('request_id', ''),
                        'timestamp': entry.get('timestamp'),
                        'url': fields.get('origin_url', '')
                    })

        if not response_times:
            return {
                'total_requests': 0,
                'avg_response_time': 0,
                'slow_requests': [],
                'performance_by_app': {},
                'performance_by_module': {}
            }

        # Calculate statistics
        avg_response_time = sum(response_times) / len(response_times)
        p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)] if response_times else 0
        p99_response_time = sorted(response_times)[int(len(response_times) * 0.99)] if response_times else 0

        # App performance summary
        app_performance = {}
        for app, times in performance_by_app.items():
            app_performance[app] = {
                'avg_response_time': sum(times) / len(times),
                'request_count': len(times),
                'slow_request_count': sum(1 for t in times if t > 5000)
            }

        # Module performance summary
        module_performance = {}
        for module, times in performance_by_module.items():
            module_performance[module] = {
                'avg_response_time': sum(times) / len(times),
                'request_count': len(times),
                'slow_request_count': sum(1 for t in times if t > 5000)
            }

        return {
            'total_requests': len(response_times),
            'avg_response_time': round(avg_response_time, 2),
            'p95_response_time': round(p95_response_time, 2),
            'p99_response_time': round(p99_response_time, 2),
            'slow_requests': slow_requests[:20],  # Top 20 slowest
            'slow_request_count': len(slow_requests),
            'performance_by_app': app_performance,
            'performance_by_module': module_performance
        }

    def _analyze_customers(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze customer activity patterns from enterprise structured logs."""
        customer_activity = defaultdict(int)
        customer_errors = defaultdict(int)
        customer_apps = defaultdict(set)
        customer_platforms = defaultdict(set)

        for entry in entries:
            fields = entry.get('fields', {})
            customer_id = fields.get('customer_id', '')

            if customer_id and customer_id != 'na' and customer_id != '':
                customer_activity[customer_id] += 1

                if entry.get('level') == 'ERROR':
                    customer_errors[customer_id] += 1

                app_name = fields.get('app_name', '')
                if app_name:
                    customer_apps[customer_id].add(app_name)

                platform = fields.get('platform', '')
                if platform and platform != 'na':
                    customer_platforms[customer_id].add(platform)

        # Top active customers
        top_customers = sorted(customer_activity.items(), key=lambda x: x[1], reverse=True)[:10]

        # Customers with most errors
        error_prone_customers = sorted(customer_errors.items(), key=lambda x: x[1], reverse=True)[:10]

        return {
            'total_unique_customers': len(customer_activity),
            'top_customers': [
                {
                    'customer_id': cid,
                    'activity_count': count,
                    'error_count': customer_errors.get(cid, 0),
                    'apps_used': list(customer_apps.get(cid, set())),
                    'platforms_used': list(customer_platforms.get(cid, set()))
                }
                for cid, count in top_customers
            ],
            'error_prone_customers': [
                {'customer_id': cid, 'error_count': count}
                for cid, count in error_prone_customers
            ]
        }

    def _analyze_applications(self, entries: List[Dict]) -> Dict[str, Any]:
        """Analyze application usage and performance patterns."""
        app_activity = defaultdict(int)
        app_errors = defaultdict(int)
        app_modules = defaultdict(set)
        app_response_times = defaultdict(list)

        for entry in entries:
            fields = entry.get('fields', {})
            app_name = fields.get('app_name', 'unknown')

            app_activity[app_name] += 1

            if entry.get('level') == 'ERROR':
                app_errors[app_name] += 1

            module = fields.get('module', '')
            if module:
                app_modules[app_name].add(module)

            response_time = fields.get('response_time', 0)
            if isinstance(response_time, (int, float)) and response_time > 0:
                app_response_times[app_name].append(response_time)

        # Application summary
        app_summary = []
        for app_name, activity_count in sorted(app_activity.items(), key=lambda x: x[1], reverse=True):
            error_count = app_errors.get(app_name, 0)
            error_rate = (error_count / activity_count * 100) if activity_count > 0 else 0

            response_times = app_response_times.get(app_name, [])
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0

            app_summary.append({
                'app_name': app_name,
                'activity_count': activity_count,
                'error_count': error_count,
                'error_rate': round(error_rate, 2),
                'avg_response_time': round(avg_response_time, 2),
                'modules': list(app_modules.get(app_name, set())),
                'module_count': len(app_modules.get(app_name, set()))
            })

        return {
            'total_applications': len(app_activity),
            'application_summary': app_summary
        }
