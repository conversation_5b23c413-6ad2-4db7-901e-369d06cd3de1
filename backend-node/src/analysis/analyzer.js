/**
 * Log Analysis Engine
 * Provides intelligent analysis of parsed log data including error detection,
 * pattern recognition, anomaly detection, and trend analysis.
 */

const moment = require('moment');
const _ = require('lodash');
const RouteAnalyzer = require('./route-analyzer');

class LogAnalyzer {
    constructor() {
        this.errorKeywords = [
            'error', 'exception', 'fail', 'crash', 'abort', 'timeout',
            'refused', 'denied', 'invalid', 'corrupt', 'missing',
            'unauthorized', 'forbidden', 'not found', 'unavailable'
        ];

        this.warningKeywords = [
            'warn', 'warning', 'deprecated', 'slow', 'retry',
            'fallback', 'degraded', 'limited', 'throttle'
        ];

        this.performanceKeywords = [
            'slow', 'timeout', 'latency', 'delay', 'queue',
            'memory', 'cpu', 'disk', 'network', 'bandwidth'
        ];

        this.routeAnalyzer = new RouteAnalyzer();
    }

    /**
     * Perform comprehensive analysis of parsed log data
     */
    analyzeLogs(parsedData) {
        const entries = parsedData.entries;
        const stats = parsedData.stats;

        const analysisResults = {
            summary: this._generateSummary(entries, stats),
            error_analysis: this._analyzeErrors(entries),
            pattern_analysis: this._analyzePatterns(entries),
            time_analysis: this._analyzeTimePatterns(entries),
            anomaly_detection: this._detectAnomalies(entries),
            performance_analysis: this._analyzePerformance(entries),
            customer_analysis: this._analyzeCustomers(entries),
            application_analysis: this._analyzeApplications(entries),
            route_analysis: this.routeAnalyzer.analyzeRoutes(entries),
            recommendations: [],
            insights: []
        };

        // Generate insights and recommendations
        analysisResults.insights = this._generateInsights(analysisResults, stats);
        analysisResults.recommendations = this._generateRecommendations(analysisResults, stats);

        return analysisResults;
    }

    /**
     * Generate summary statistics
     */
    _generateSummary(entries, stats) {
        const totalEntries = entries.length;
        const errorCount = entries.filter(entry => 
            entry.level === 'ERROR' || this._containsErrorKeywords(entry.message || '')
        ).length;
        
        const warningCount = entries.filter(entry => 
            entry.level === 'WARN' || entry.level === 'WARNING' || 
            this._containsWarningKeywords(entry.message || '')
        ).length;

        const errorRate = totalEntries > 0 ? (errorCount / totalEntries) * 100 : 0;
        const warningRate = totalEntries > 0 ? (warningCount / totalEntries) * 100 : 0;

        // Determine health status
        let healthStatus = 'healthy';
        if (errorRate > 10) {
            healthStatus = 'critical';
        } else if (errorRate > 5 || warningRate > 20) {
            healthStatus = 'warning';
        } else if (errorRate > 1 || warningRate > 10) {
            healthStatus = 'degraded';
        }

        // Time range analysis
        const timestamps = entries
            .map(entry => entry.timestamp)
            .filter(ts => ts)
            .map(ts => new Date(ts))
            .sort((a, b) => a - b);

        const timeRange = timestamps.length > 0 ? {
            start: timestamps[0].toISOString(),
            end: timestamps[timestamps.length - 1].toISOString(),
            duration_hours: timestamps.length > 0 ? 
                (timestamps[timestamps.length - 1] - timestamps[0]) / (1000 * 60 * 60) : 0
        } : null;

        return {
            total_entries: totalEntries,
            error_count: errorCount,
            warning_count: warningCount,
            error_rate: Math.round(errorRate * 100) / 100,
            warning_rate: Math.round(warningRate * 100) / 100,
            health_status: healthStatus,
            time_range: timeRange,
            parsed_successfully: stats.parsed_entries || 0,
            parse_success_rate: Math.round((stats.parse_success_rate || 0) * 10000) / 100
        };
    }

    /**
     * Analyze error patterns and trends
     */
    _analyzeErrors(entries) {
        const errorEntries = entries.filter(entry => 
            entry.level === 'ERROR' || this._containsErrorKeywords(entry.message || '')
        );

        if (errorEntries.length === 0) {
            return {
                total_errors: 0,
                error_types: {},
                error_trends: [],
                top_error_messages: []
            };
        }

        // Categorize errors
        const errorTypes = {};
        const errorMessages = {};

        errorEntries.forEach(entry => {
            const message = entry.message || '';
            
            // Categorize by keywords
            let category = 'other';
            if (message.toLowerCase().includes('timeout')) category = 'timeout';
            else if (message.toLowerCase().includes('connection')) category = 'connection';
            else if (message.toLowerCase().includes('permission') || message.toLowerCase().includes('unauthorized')) category = 'permission';
            else if (message.toLowerCase().includes('not found') || message.toLowerCase().includes('404')) category = 'not_found';
            else if (message.toLowerCase().includes('server') || message.toLowerCase().includes('500')) category = 'server_error';

            errorTypes[category] = (errorTypes[category] || 0) + 1;
            errorMessages[message] = (errorMessages[message] || 0) + 1;
        });

        // Get top error messages
        const topErrorMessages = Object.entries(errorMessages)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([message, count]) => ({ message, count }));

        // Analyze error trends over time
        const errorTrends = this._analyzeErrorTrends(errorEntries);

        return {
            total_errors: errorEntries.length,
            error_types: errorTypes,
            error_trends: errorTrends,
            top_error_messages: topErrorMessages
        };
    }

    /**
     * Analyze error trends over time
     */
    _analyzeErrorTrends(errorEntries) {
        const trends = [];
        
        // Group errors by hour
        const errorsByHour = {};
        
        errorEntries.forEach(entry => {
            if (entry.timestamp) {
                const hour = moment(entry.timestamp).format('YYYY-MM-DD HH:00:00');
                errorsByHour[hour] = (errorsByHour[hour] || 0) + 1;
            }
        });

        // Convert to trend data
        Object.entries(errorsByHour)
            .sort(([a], [b]) => a.localeCompare(b))
            .forEach(([hour, count]) => {
                trends.push({
                    timestamp: hour,
                    error_count: count
                });
            });

        return trends;
    }

    /**
     * Analyze patterns in log data
     */
    _analyzePatterns(entries) {
        const patterns = {
            common_messages: this._findCommonMessages(entries),
            ip_analysis: this._analyzeIpPatterns(entries),
            status_patterns: this._analyzeStatusPatterns(entries)
        };

        return patterns;
    }

    /**
     * Find common message patterns
     */
    _findCommonMessages(entries) {
        const messageCounts = {};
        
        entries.forEach(entry => {
            const message = entry.message || '';
            if (message.length > 10) { // Ignore very short messages
                messageCounts[message] = (messageCounts[message] || 0) + 1;
            }
        });

        return Object.entries(messageCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 20)
            .map(([message, count]) => ({
                message: message.substring(0, 200), // Truncate long messages
                count,
                percentage: Math.round((count / entries.length) * 10000) / 100
            }));
    }

    /**
     * Analyze IP address patterns
     */
    _analyzeIpPatterns(entries) {
        const ips = [];
        
        entries.forEach(entry => {
            const fields = entry.fields || {};
            if (fields.ip) {
                ips.push(fields.ip);
            } else if (fields.client_ip) {
                ips.push(fields.client_ip);
            } else {
                // Try to extract IP from raw line
                const ipMatch = (entry.raw_line || '').match(/\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b/);
                if (ipMatch) {
                    ips.push(ipMatch[1]);
                }
            }
        });

        if (ips.length === 0) {
            return { total_unique_ips: 0, top_ips: [] };
        }

        const ipCounts = _.countBy(ips);
        const topIps = Object.entries(ipCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([ip, count]) => ({ ip, count }));

        return {
            total_unique_ips: Object.keys(ipCounts).length,
            top_ips: topIps
        };
    }

    /**
     * Analyze HTTP status code patterns
     */
    _analyzeStatusPatterns(entries) {
        const statusCodes = [];
        
        entries.forEach(entry => {
            const fields = entry.fields || {};
            if (fields.status) {
                statusCodes.push(fields.status);
            }
        });

        if (statusCodes.length === 0) {
            return { status_distribution: {} };
        }

        const statusCounts = _.countBy(statusCodes);
        
        // Categorize status codes
        const categories = {
            '2xx': 0,
            '3xx': 0,
            '4xx': 0,
            '5xx': 0
        };

        Object.entries(statusCounts).forEach(([status, count]) => {
            const statusNum = parseInt(status);
            if (statusNum >= 200 && statusNum < 300) categories['2xx'] += count;
            else if (statusNum >= 300 && statusNum < 400) categories['3xx'] += count;
            else if (statusNum >= 400 && statusNum < 500) categories['4xx'] += count;
            else if (statusNum >= 500 && statusNum < 600) categories['5xx'] += count;
        });

        return {
            status_distribution: statusCounts,
            status_categories: categories
        };
    }

    /**
     * Analyze temporal patterns in log data
     */
    _analyzeTimePatterns(entries) {
        const timestamps = entries
            .map(entry => entry.timestamp)
            .filter(ts => ts)
            .map(ts => moment(ts));

        if (timestamps.length === 0) {
            return {
                hourly_distribution: {},
                daily_distribution: {},
                peak_hour: null,
                total_days: 0,
                avg_entries_per_hour: 0
            };
        }

        // Hourly distribution
        const hourlyCounts = {};
        const dailyCounts = {};

        timestamps.forEach(ts => {
            const hour = ts.hour();
            const day = ts.format('YYYY-MM-DD');
            
            hourlyCounts[hour] = (hourlyCounts[hour] || 0) + 1;
            dailyCounts[day] = (dailyCounts[day] || 0) + 1;
        });

        // Find peak hour
        const peakHour = Object.entries(hourlyCounts)
            .sort(([,a], [,b]) => b - a)[0]?.[0] || null;

        return {
            hourly_distribution: hourlyCounts,
            daily_distribution: dailyCounts,
            peak_hour: peakHour ? parseInt(peakHour) : null,
            total_days: Object.keys(dailyCounts).length,
            avg_entries_per_hour: Object.keys(hourlyCounts).length > 0 ? 
                Object.values(hourlyCounts).reduce((a, b) => a + b, 0) / 24 : 0
        };
    }

    /**
     * Detect anomalies in log data
     */
    _detectAnomalies(entries) {
        const anomalies = [];

        // Detect error spikes
        const errorSpikes = this._detectErrorSpikes(entries);
        if (errorSpikes.length > 0) {
            anomalies.push(...errorSpikes);
        }

        // Detect unusual patterns
        const unusualPatterns = this._detectUnusualPatterns(entries);
        if (unusualPatterns.length > 0) {
            anomalies.push(...unusualPatterns);
        }

        return {
            total_anomalies: anomalies.length,
            anomalies: anomalies.slice(0, 20) // Return top 20 anomalies
        };
    }

    /**
     * Detect spikes in error rates
     */
    _detectErrorSpikes(entries) {
        const spikes = [];
        
        // Group entries by 5-minute intervals
        const timeWindows = {};

        entries.forEach(entry => {
            if (entry.timestamp) {
                const ts = moment(entry.timestamp);
                const windowKey = ts.clone().startOf('minute').subtract(ts.minute() % 5, 'minutes').toISOString();
                
                if (!timeWindows[windowKey]) {
                    timeWindows[windowKey] = { total: 0, errors: 0 };
                }
                
                timeWindows[windowKey].total++;
                if (entry.level === 'ERROR' || this._containsErrorKeywords(entry.message || '')) {
                    timeWindows[windowKey].errors++;
                }
            }
        });

        // Calculate error rates and detect spikes
        const errorRates = Object.entries(timeWindows).map(([timestamp, data]) => ({
            timestamp,
            error_rate: data.total > 0 ? data.errors / data.total : 0,
            total_entries: data.total,
            error_count: data.errors
        }));

        // Find average error rate
        const avgErrorRate = errorRates.length > 0 ? 
            errorRates.reduce((sum, item) => sum + item.error_rate, 0) / errorRates.length : 0;

        // Detect spikes (error rate > 3x average and > 10%)
        errorRates.forEach(item => {
            if (item.error_rate > avgErrorRate * 3 && item.error_rate > 0.1) {
                spikes.push({
                    type: 'error_spike',
                    timestamp: item.timestamp,
                    error_rate: Math.round(item.error_rate * 10000) / 100,
                    severity: item.error_rate > 0.5 ? 'high' : 'medium'
                });
            }
        });

        return spikes;
    }

    /**
     * Detect unusual patterns
     */
    _detectUnusualPatterns(entries) {
        const patterns = [];
        
        // Detect unusual message patterns (very rare messages)
        const messageCounts = {};
        entries.forEach(entry => {
            const message = entry.message || '';
            if (message.length > 20) {
                messageCounts[message] = (messageCounts[message] || 0) + 1;
            }
        });

        const totalMessages = entries.length;
        const rareThreshold = Math.max(1, totalMessages * 0.001); // 0.1% threshold

        Object.entries(messageCounts).forEach(([message, count]) => {
            if (count <= rareThreshold && count === 1) {
                patterns.push({
                    type: 'rare_message',
                    message: message.substring(0, 100),
                    count: count,
                    severity: 'low'
                });
            }
        });

        return patterns.slice(0, 10); // Limit to 10 unusual patterns
    }

    /**
     * Helper method to check if text contains error keywords
     */
    _containsErrorKeywords(text) {
        const lowerText = text.toLowerCase();
        return this.errorKeywords.some(keyword => lowerText.includes(keyword));
    }

    /**
     * Helper method to check if text contains warning keywords
     */
    _containsWarningKeywords(text) {
        const lowerText = text.toLowerCase();
        return this.warningKeywords.some(keyword => lowerText.includes(keyword));
    }

    /**
     * Analyze performance metrics from enterprise structured logs
     */
    _analyzePerformance(entries) {
        const responseTimes = [];
        const slowRequests = [];
        const performanceByApp = {};
        const performanceByModule = {};

        entries.forEach(entry => {
            const fields = entry.fields || {};
            const responseTime = fields.response_time;

            if (typeof responseTime === 'number' && responseTime > 0) {
                responseTimes.push(responseTime);

                const appName = fields.app_name || 'unknown';
                const module = fields.module || 'unknown';

                if (!performanceByApp[appName]) performanceByApp[appName] = [];
                if (!performanceByModule[module]) performanceByModule[module] = [];

                performanceByApp[appName].push(responseTime);
                performanceByModule[module].push(responseTime);

                // Flag slow requests (>5 seconds)
                if (responseTime > 5000) {
                    slowRequests.push({
                        response_time: responseTime,
                        app_name: appName,
                        module: module,
                        request_id: fields.request_id || '',
                        timestamp: entry.timestamp,
                        url: fields.origin_url || ''
                    });
                }
            }
        });

        if (responseTimes.length === 0) {
            return {
                total_requests: 0,
                avg_response_time: 0,
                slow_requests: [],
                performance_by_app: {},
                performance_by_module: {}
            };
        }

        // Calculate statistics
        const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
        const sortedTimes = responseTimes.sort((a, b) => a - b);
        const p95ResponseTime = sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0;
        const p99ResponseTime = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;

        // App performance summary
        const appPerformance = {};
        Object.entries(performanceByApp).forEach(([app, times]) => {
            appPerformance[app] = {
                avg_response_time: times.reduce((a, b) => a + b, 0) / times.length,
                request_count: times.length,
                slow_request_count: times.filter(t => t > 5000).length
            };
        });

        // Module performance summary
        const modulePerformance = {};
        Object.entries(performanceByModule).forEach(([module, times]) => {
            modulePerformance[module] = {
                avg_response_time: times.reduce((a, b) => a + b, 0) / times.length,
                request_count: times.length,
                slow_request_count: times.filter(t => t > 5000).length
            };
        });

        return {
            total_requests: responseTimes.length,
            avg_response_time: Math.round(avgResponseTime * 100) / 100,
            p95_response_time: Math.round(p95ResponseTime * 100) / 100,
            p99_response_time: Math.round(p99ResponseTime * 100) / 100,
            slow_requests: slowRequests.slice(0, 20), // Top 20 slowest
            slow_request_count: slowRequests.length,
            performance_by_app: appPerformance,
            performance_by_module: modulePerformance
        };
    }

    /**
     * Analyze customer activity patterns from enterprise structured logs
     */
    _analyzeCustomers(entries) {
        const customerActivity = {};
        const customerErrors = {};
        const customerApps = {};
        const customerPlatforms = {};

        entries.forEach(entry => {
            const fields = entry.fields || {};
            const customerId = fields.customer_id;

            if (customerId && customerId !== 'na' && customerId !== '') {
                customerActivity[customerId] = (customerActivity[customerId] || 0) + 1;

                if (entry.level === 'ERROR') {
                    customerErrors[customerId] = (customerErrors[customerId] || 0) + 1;
                }

                const appName = fields.app_name;
                if (appName) {
                    if (!customerApps[customerId]) customerApps[customerId] = new Set();
                    customerApps[customerId].add(appName);
                }

                const platform = fields.platform;
                if (platform && platform !== 'na') {
                    if (!customerPlatforms[customerId]) customerPlatforms[customerId] = new Set();
                    customerPlatforms[customerId].add(platform);
                }
            }
        });

        // Top active customers
        const topCustomers = Object.entries(customerActivity)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([customerId, count]) => ({
                customer_id: customerId,
                activity_count: count,
                error_count: customerErrors[customerId] || 0,
                apps_used: customerApps[customerId] ? Array.from(customerApps[customerId]) : [],
                platforms_used: customerPlatforms[customerId] ? Array.from(customerPlatforms[customerId]) : []
            }));

        // Customers with most errors
        const errorProneCustomers = Object.entries(customerErrors)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([customerId, count]) => ({ customer_id: customerId, error_count: count }));

        return {
            total_unique_customers: Object.keys(customerActivity).length,
            top_customers: topCustomers,
            error_prone_customers: errorProneCustomers
        };
    }

    /**
     * Analyze application usage and performance patterns
     */
    _analyzeApplications(entries) {
        const appActivity = {};
        const appErrors = {};
        const appModules = {};
        const appResponseTimes = {};

        entries.forEach(entry => {
            const fields = entry.fields || {};
            const appName = fields.app_name || 'unknown';

            appActivity[appName] = (appActivity[appName] || 0) + 1;

            if (entry.level === 'ERROR') {
                appErrors[appName] = (appErrors[appName] || 0) + 1;
            }

            const module = fields.module;
            if (module) {
                if (!appModules[appName]) appModules[appName] = new Set();
                appModules[appName].add(module);
            }

            const responseTime = fields.response_time;
            if (typeof responseTime === 'number' && responseTime > 0) {
                if (!appResponseTimes[appName]) appResponseTimes[appName] = [];
                appResponseTimes[appName].push(responseTime);
            }
        });

        // Application summary
        const appSummary = Object.entries(appActivity)
            .sort(([,a], [,b]) => b - a)
            .map(([appName, activityCount]) => {
                const errorCount = appErrors[appName] || 0;
                const errorRate = activityCount > 0 ? (errorCount / activityCount * 100) : 0;

                const responseTimes = appResponseTimes[appName] || [];
                const avgResponseTime = responseTimes.length > 0 ?
                    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0;

                return {
                    app_name: appName,
                    activity_count: activityCount,
                    error_count: errorCount,
                    error_rate: Math.round(errorRate * 100) / 100,
                    avg_response_time: Math.round(avgResponseTime * 100) / 100,
                    modules: appModules[appName] ? Array.from(appModules[appName]) : [],
                    module_count: appModules[appName] ? appModules[appName].size : 0
                };
            });

        return {
            total_applications: Object.keys(appActivity).length,
            application_summary: appSummary
        };
    }

    /**
     * Generate human-readable insights from analysis
     */
    _generateInsights(analysis, stats) {
        const insights = [];
        const summary = analysis.summary;

        // Basic health insights
        if (summary.health_status === 'critical') {
            insights.push(`🚨 System health is CRITICAL with ${summary.error_rate}% error rate.`);
        } else if (summary.health_status === 'warning') {
            insights.push(`⚠️ System health needs attention with ${summary.error_rate}% error rate.`);
        } else if (summary.health_status === 'healthy') {
            insights.push(`✅ System appears healthy with low error rate (${summary.error_rate}%).`);
        }

        // Volume insights
        if (summary.total_entries > 100000) {
            insights.push(`📊 High volume system with ${summary.total_entries.toLocaleString()} log entries.`);
        } else if (summary.total_entries < 100) {
            insights.push(`📉 Low activity detected with only ${summary.total_entries} log entries.`);
        }

        // Error analysis insights
        const errorAnalysis = analysis.error_analysis;
        if (errorAnalysis.total_errors > 0) {
            const topErrorType = Object.entries(errorAnalysis.error_types)
                .sort(([,a], [,b]) => b - a)[0];
            if (topErrorType) {
                insights.push(`🔍 Most common error type: ${topErrorType[0]} (${topErrorType[1]} occurrences).`);
            }
        }

        // Performance insights
        if (analysis.performance_analysis) {
            const perfAnalysis = analysis.performance_analysis;
            if (perfAnalysis.total_requests > 0) {
                const avgTime = perfAnalysis.avg_response_time;
                const slowCount = perfAnalysis.slow_request_count;

                if (avgTime > 2000) {
                    insights.push(`🐌 High average response time: ${avgTime}ms. Consider performance optimization.`);
                } else if (avgTime < 500) {
                    insights.push(`⚡ Excellent performance: Average response time is ${avgTime}ms.`);
                }

                if (slowCount > 0) {
                    insights.push(`⏱️ Found ${slowCount} slow requests (>5 seconds) that need investigation.`);
                }
            }
        }

        // Customer insights
        if (analysis.customer_analysis) {
            const customerAnalysis = analysis.customer_analysis;
            if (customerAnalysis.total_unique_customers > 0) {
                insights.push(`👥 Serving ${customerAnalysis.total_unique_customers} unique customers.`);

                if (customerAnalysis.error_prone_customers.length > 0) {
                    const topErrorCustomer = customerAnalysis.error_prone_customers[0];
                    insights.push(`🚨 Customer ${topErrorCustomer.customer_id} has ${topErrorCustomer.error_count} errors.`);
                }
            }
        }

        // Application insights
        if (analysis.application_analysis) {
            const appAnalysis = analysis.application_analysis;
            if (appAnalysis.total_applications > 1) {
                insights.push(`📱 Monitoring ${appAnalysis.total_applications} different applications.`);

                // Find app with highest error rate
                const apps = appAnalysis.application_summary;
                if (apps.length > 0) {
                    const highestErrorApp = apps.reduce((max, app) =>
                        app.error_rate > max.error_rate ? app : max
                    );
                    if (highestErrorApp.error_rate > 5) {
                        insights.push(`⚠️ Application '${highestErrorApp.app_name}' has high error rate: ${highestErrorApp.error_rate}%`);
                    }
                }
            }
        }

        return insights;
    }

    /**
     * Generate actionable recommendations
     */
    _generateRecommendations(analysis, stats) {
        const recommendations = [];
        const summary = analysis.summary;

        // Error rate recommendations
        if (summary.error_rate > 10) {
            recommendations.push("Immediate action required: Investigate and resolve critical errors causing high error rate.");
        } else if (summary.error_rate > 5) {
            recommendations.push("Monitor error trends closely and implement error reduction strategies.");
        }

        // Performance recommendations
        if (analysis.performance_analysis) {
            const perfAnalysis = analysis.performance_analysis;
            if (perfAnalysis.avg_response_time > 2000) {
                recommendations.push("Optimize application performance to reduce response times.");
            }

            if (perfAnalysis.slow_request_count > perfAnalysis.total_requests * 0.05) {
                recommendations.push("Investigate and optimize slow requests (>5% of requests are slow).");
            }
        }

        // Customer recommendations
        if (analysis.customer_analysis) {
            const customerAnalysis = analysis.customer_analysis;
            if (customerAnalysis.error_prone_customers.length > 0) {
                recommendations.push("Provide additional support to customers experiencing frequent errors.");
            }
        }

        // Application recommendations
        if (analysis.application_analysis) {
            const appAnalysis = analysis.application_analysis;
            appAnalysis.application_summary.forEach(app => {
                if (app.error_rate > 10) {
                    recommendations.push(`Investigate high error rate in application '${app.app_name}'.`);
                }
            });
        }

        if (summary.total_entries > 10000) {
            recommendations.push("Consider implementing log rotation and archival for large log files.");
        }

        return recommendations;
    }
}

module.exports = LogAnalyzer;
