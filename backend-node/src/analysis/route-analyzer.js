/**
 * Route Analysis Engine
 * Analyzes API routes and endpoints from enterprise structured logs
 * Extracts method and originUrl fields for route-specific metrics
 */

const _ = require('lodash');
const moment = require('moment');

class RouteAnalyzer {
    constructor() {
        this.routePatterns = {
            // Common API patterns to normalize
            idPattern: /\/\d+/g,
            uuidPattern: /\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi,
            hashPattern: /\/[a-f0-9]{32,}/gi,
            timestampPattern: /\/\d{10,13}/g
        };
    }

    /**
     * Analyze routes from parsed log entries
     */
    analyzeRoutes(entries) {
        const routeMetrics = {};
        const routeErrors = {};
        const routeCustomers = {};
        const routeResponseTimes = {};
        const routeTimeline = {};

        entries.forEach(entry => {
            const fields = entry.fields || {};
            const method = fields.method;
            const originUrl = fields.origin_url || fields.url;
            const responseTime = fields.response_time;
            const status = fields.status;
            const customerId = fields.customer_id;
            const timestamp = entry.timestamp;

            if (method && originUrl) {
                const route = this.normalizeRoute(method, originUrl);
                
                // Initialize route metrics
                if (!routeMetrics[route]) {
                    routeMetrics[route] = {
                        method: method,
                        pattern: this.extractRoutePattern(originUrl),
                        total_requests: 0,
                        unique_customers: new Set(),
                        response_times: [],
                        status_codes: {},
                        error_count: 0,
                        first_seen: timestamp,
                        last_seen: timestamp
                    };
                }

                const routeData = routeMetrics[route];
                
                // Update basic metrics
                routeData.total_requests++;
                
                if (timestamp) {
                    if (!routeData.first_seen || new Date(timestamp) < new Date(routeData.first_seen)) {
                        routeData.first_seen = timestamp;
                    }
                    if (!routeData.last_seen || new Date(timestamp) > new Date(routeData.last_seen)) {
                        routeData.last_seen = timestamp;
                    }
                }

                // Track customers
                if (customerId && customerId !== 'na' && customerId !== '') {
                    routeData.unique_customers.add(customerId);
                }

                // Track response times
                if (typeof responseTime === 'number' && responseTime > 0) {
                    routeData.response_times.push(responseTime);
                }

                // Track status codes
                if (status) {
                    routeData.status_codes[status] = (routeData.status_codes[status] || 0) + 1;
                    
                    // Count errors (4xx and 5xx)
                    const statusNum = parseInt(status);
                    if (statusNum >= 400) {
                        routeData.error_count++;
                    }
                }

                // Track timeline data
                if (timestamp) {
                    const hour = moment(timestamp).format('YYYY-MM-DD HH:00:00');
                    if (!routeTimeline[route]) {
                        routeTimeline[route] = {};
                    }
                    routeTimeline[route][hour] = (routeTimeline[route][hour] || 0) + 1;
                }
            }
        });

        // Process and calculate final metrics
        const processedRoutes = {};
        Object.entries(routeMetrics).forEach(([route, data]) => {
            const responseTimes = data.response_times;
            const totalRequests = data.total_requests;
            const errorCount = data.error_count;

            processedRoutes[route] = {
                method: data.method,
                pattern: data.pattern,
                total_requests: totalRequests,
                unique_customers: data.unique_customers.size,
                error_count: errorCount,
                error_rate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
                
                // Response time metrics
                avg_response_time: responseTimes.length > 0 ? 
                    responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length : 0,
                min_response_time: responseTimes.length > 0 ? Math.min(...responseTimes) : 0,
                max_response_time: responseTimes.length > 0 ? Math.max(...responseTimes) : 0,
                p95_response_time: responseTimes.length > 0 ? 
                    this.calculatePercentile(responseTimes, 95) : 0,
                p99_response_time: responseTimes.length > 0 ? 
                    this.calculatePercentile(responseTimes, 99) : 0,
                
                // Status code distribution
                status_codes: data.status_codes,
                
                // Time range
                first_seen: data.first_seen,
                last_seen: data.last_seen,
                
                // Timeline data
                timeline: routeTimeline[route] || {}
            };
        });

        return {
            routes: processedRoutes,
            summary: this.generateRouteSummary(processedRoutes),
            top_routes: this.getTopRoutes(processedRoutes),
            slow_routes: this.getSlowRoutes(processedRoutes),
            error_prone_routes: this.getErrorProneRoutes(processedRoutes)
        };
    }

    /**
     * Normalize route by combining method and URL pattern
     */
    normalizeRoute(method, url) {
        const normalizedUrl = this.extractRoutePattern(url);
        return `${method.toUpperCase()} ${normalizedUrl}`;
    }

    /**
     * Extract route pattern by replacing dynamic segments
     */
    extractRoutePattern(url) {
        if (!url) return '/';
        
        let pattern = url;
        
        // Remove query parameters
        pattern = pattern.split('?')[0];
        
        // Replace common dynamic segments
        pattern = pattern.replace(this.routePatterns.idPattern, '/{id}');
        pattern = pattern.replace(this.routePatterns.uuidPattern, '/{uuid}');
        pattern = pattern.replace(this.routePatterns.hashPattern, '/{hash}');
        pattern = pattern.replace(this.routePatterns.timestampPattern, '/{timestamp}');
        
        // Ensure it starts with /
        if (!pattern.startsWith('/')) {
            pattern = '/' + pattern;
        }
        
        return pattern;
    }

    /**
     * Calculate percentile for response times
     */
    calculatePercentile(values, percentile) {
        const sorted = values.slice().sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[Math.max(0, index)] || 0;
    }

    /**
     * Generate summary statistics for all routes
     */
    generateRouteSummary(routes) {
        const routeArray = Object.values(routes);
        
        if (routeArray.length === 0) {
            return {
                total_routes: 0,
                total_requests: 0,
                avg_response_time: 0,
                total_errors: 0,
                avg_error_rate: 0
            };
        }

        const totalRequests = routeArray.reduce((sum, route) => sum + route.total_requests, 0);
        const totalErrors = routeArray.reduce((sum, route) => sum + route.error_count, 0);
        const avgResponseTime = routeArray.reduce((sum, route) => 
            sum + (route.avg_response_time * route.total_requests), 0) / totalRequests;

        return {
            total_routes: routeArray.length,
            total_requests: totalRequests,
            avg_response_time: avgResponseTime,
            total_errors: totalErrors,
            avg_error_rate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
            unique_customers: Math.max(...routeArray.map(r => r.unique_customers))
        };
    }

    /**
     * Get top routes by request volume
     */
    getTopRoutes(routes, limit = 10) {
        return Object.entries(routes)
            .sort(([,a], [,b]) => b.total_requests - a.total_requests)
            .slice(0, limit)
            .map(([route, data]) => ({ route, ...data }));
    }

    /**
     * Get slowest routes by average response time
     */
    getSlowRoutes(routes, limit = 10) {
        return Object.entries(routes)
            .filter(([,data]) => data.avg_response_time > 0)
            .sort(([,a], [,b]) => b.avg_response_time - a.avg_response_time)
            .slice(0, limit)
            .map(([route, data]) => ({ route, ...data }));
    }

    /**
     * Get routes with highest error rates
     */
    getErrorProneRoutes(routes, limit = 10) {
        return Object.entries(routes)
            .filter(([,data]) => data.error_count > 0)
            .sort(([,a], [,b]) => b.error_rate - a.error_rate)
            .slice(0, limit)
            .map(([route, data]) => ({ route, ...data }));
    }

    /**
     * Compare routes across multiple analyses
     */
    compareRoutes(routeAnalyses) {
        const allRoutes = new Set();
        
        // Collect all unique routes
        routeAnalyses.forEach(analysis => {
            Object.keys(analysis.routes || {}).forEach(route => {
                allRoutes.add(route);
            });
        });

        const comparison = {};
        
        allRoutes.forEach(route => {
            comparison[route] = routeAnalyses.map((analysis, index) => {
                const routeData = analysis.routes[route];
                return {
                    file_index: index,
                    exists: !!routeData,
                    total_requests: routeData?.total_requests || 0,
                    avg_response_time: routeData?.avg_response_time || 0,
                    error_rate: routeData?.error_rate || 0,
                    unique_customers: routeData?.unique_customers || 0
                };
            });
        });

        return comparison;
    }
}

module.exports = RouteAnalyzer;
