/**
 * Log Parser Module
 * Parses log files based on detected format and extracts structured data.
 */

const fs = require('fs');
const { promisify } = require('util');
const moment = require('moment');
const LogFormatDetector = require('./detector');

const readFile = promisify(fs.readFile);

/**
 * Represents a single log entry
 */
class LogEntry {
    constructor(rawLine, lineNumber) {
        this.rawLine = rawLine;
        this.lineNumber = lineNumber;
        this.timestamp = null;
        this.level = null;
        this.message = null;
        this.fields = {};
        this.parsed = false;
    }

    /**
     * Convert log entry to dictionary
     */
    toDict() {
        return {
            line_number: this.lineNumber,
            raw_line: this.rawLine,
            timestamp: this.timestamp ? this.timestamp.toISOString() : null,
            level: this.level,
            message: this.message,
            fields: this.fields,
            parsed: this.parsed
        };
    }
}

/**
 * Main log parser class
 */
class LogParser {
    constructor() {
        this.detector = new LogFormatDetector();
        this.timestampPatterns = [
            /(\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}(?:\.\d{3})?)/,
            /(\d{2}\/\d{2}\/\d{4}\s+\d{2}:\d{2}:\d{2})/,
            /(\d{2}-\d{2}-\d{4}\s+\d{2}:\d{2}:\d{2})/,
            /([A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})/
        ];
        this.logLevels = ['DEBUG', 'INFO', 'WARN', 'WARNING', 'ERROR', 'FATAL', 'CRITICAL', 'TRACE'];
    }

    /**
     * Parse a log file and return structured data
     */
    async parseFile(filePath, maxLines = null) {
        try {
            const buffer = await readFile(filePath);
            const content = buffer.toString('utf8');
            const lines = content.split('\n').filter(line => line.trim());

            // Limit lines if specified
            const linesToProcess = maxLines ? lines.slice(0, maxLines) : lines;

            // Detect format
            const formatInfo = await this.detector.detectFormat(filePath, linesToProcess.slice(0, 50));

            // Parse entries
            const entries = [];
            let parsedCount = 0;

            for (let i = 0; i < linesToProcess.length; i++) {
                const entry = new LogEntry(linesToProcess[i], i + 1);
                this.parseEntry(entry, formatInfo);
                entries.push(entry);
                
                if (entry.parsed) {
                    parsedCount++;
                }
            }

            // Calculate statistics
            const stats = this.calculateStats(entries, formatInfo);

            return {
                format_info: formatInfo,
                entries: entries.map(entry => entry.toDict()),
                total_lines: linesToProcess.length,
                parsed_lines: parsedCount,
                stats: stats
            };

        } catch (error) {
            console.error('Error parsing file:', error);
            throw new Error(`Failed to parse file: ${error.message}`);
        }
    }

    /**
     * Parse a single log entry based on format
     */
    parseEntry(entry, formatInfo) {
        const formatType = formatInfo.format;

        try {
            if (formatType === 'json') {
                this.parseJsonLine(entry, formatInfo);
            } else if (formatType === 'enterprise_structured') {
                this.parseEnterpriseStructuredLine(entry, formatInfo);
            } else if (['apache_access', 'nginx_access'].includes(formatType)) {
                this.parseAccessLog(entry, formatType);
            } else if (formatType === 'syslog') {
                this.parseSyslog(entry);
            } else if (formatType === 'delimited') {
                this.parseDelimitedLine(entry, formatInfo);
            } else {
                this.parsePlainTextLine(entry);
            }
        } catch (error) {
            console.error(`Error parsing line ${entry.lineNumber}:`, error);
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse JSON log line
     */
    parseJsonLine(entry, formatInfo) {
        try {
            const jsonData = JSON.parse(entry.rawLine);
            entry.parsed = true;

            // Extract common fields
            entry.timestamp = this.parseTimestamp(
                jsonData.timestamp || jsonData.time || jsonData['@timestamp'] || jsonData.ts
            );
            entry.level = this.extractLogLevel(
                jsonData.level || jsonData.severity || jsonData.loglevel
            );
            entry.message = jsonData.message || jsonData.msg || jsonData.text || '';

            // Store all fields
            entry.fields = { ...jsonData };

        } catch (error) {
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse enterprise structured log line with bracketed fields
     */
    parseEnterpriseStructuredLine(entry, formatInfo) {
        try {
            // Split on ' : ' to separate structured fields from raw data
            const parts = entry.rawLine.split(' : ');
            if (parts.length !== 2) {
                entry.parsed = false;
                return;
            }

            const [structuredPart, rawData] = parts;

            // Extract bracketed fields
            const bracketPattern = /\[(.*?)\]/g;
            const fields = [];
            let match;

            while ((match = bracketPattern.exec(structuredPart)) !== null) {
                fields.push(match[1]);
            }

            if (fields.length >= 14) {
                entry.parsed = true;

                // Parse timestamp (first field)
                if (fields[0]) {
                    entry.timestamp = this.parseTimestamp(fields[0]);
                }

                // Parse log level (second field)
                if (fields[1]) {
                    entry.level = fields[1].toUpperCase();
                }

                // Store all structured fields
                entry.fields = {
                    timestamp_raw: fields[0] || '',
                    level: fields[1] || '',
                    module: fields[2] || '',
                    session_id: fields[3] || '',
                    platform: fields[4] || '',
                    customer_id: fields[5] || '',
                    client_ip: fields[6] || '',
                    request_id: fields[7] || '',
                    audit_type: fields[8] || '',
                    app_name: fields[9] || '',
                    response_time: this.parseResponseTime(fields[10]),
                    status: this.parseStatusCode(fields[11]),
                    method: fields[12] || '',
                    origin_url: fields[13] || '',
                    extra_data: fields[14] || ''
                };

                // Store raw data as message
                entry.message = rawData.trim();

                // Try to parse raw data as JSON for additional insights
                try {
                    const jsonData = JSON.parse(rawData.trim());
                    entry.fields.parsed_json = jsonData;
                } catch (e) {
                    // Raw data is not JSON, keep as string
                }

            } else {
                entry.parsed = false;
                this.parsePlainTextLine(entry);
            }

        } catch (error) {
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse response time string to integer (milliseconds)
     */
    parseResponseTime(responseTimeStr) {
        try {
            return responseTimeStr && /^\d+$/.test(responseTimeStr) ? parseInt(responseTimeStr) : 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Parse status code string to integer
     */
    parseStatusCode(statusStr) {
        try {
            return statusStr && /^\d+$/.test(statusStr) ? parseInt(statusStr) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Parse access log (Apache/Nginx)
     */
    parseAccessLog(entry, formatType) {
        // Simplified access log parsing
        const accessPattern = /^(\S+) \S+ \S+ \[(.*?)\] "(\S+) (\S+) (\S+)" (\d+) (\d+)/;
        const match = entry.rawLine.match(accessPattern);

        if (match) {
            entry.parsed = true;
            entry.fields = {
                ip: match[1],
                timestamp_raw: match[2],
                method: match[3],
                url: match[4],
                protocol: match[5],
                status: parseInt(match[6]),
                size: parseInt(match[7])
            };
            entry.timestamp = this.parseTimestamp(match[2]);
            entry.message = `${match[3]} ${match[4]} ${match[6]}`;
        } else {
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse syslog format
     */
    parseSyslog(entry) {
        const syslogPattern = /^([A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+(.*)/;
        const match = entry.rawLine.match(syslogPattern);

        if (match) {
            entry.parsed = true;
            entry.timestamp = this.parseTimestamp(match[1]);
            entry.fields = {
                hostname: match[2],
                process: match[3].split(':')[0] || '',
                timestamp_raw: match[1]
            };
            entry.message = match[3];
            entry.level = this.extractLogLevel(entry.message);
        } else {
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse delimited format (CSV, TSV, etc.)
     */
    parseDelimitedLine(entry, formatInfo) {
        const delimiter = formatInfo.delimiter || ',';
        const fields = entry.rawLine.split(delimiter);

        if (fields.length > 1) {
            entry.parsed = true;
            entry.fields = {};
            
            fields.forEach((field, index) => {
                entry.fields[`field_${index}`] = field.trim();
            });

            // Try to extract timestamp and level from first few fields
            for (let i = 0; i < Math.min(3, fields.length); i++) {
                const field = fields[i].trim();
                if (!entry.timestamp) {
                    entry.timestamp = this.parseTimestamp(field);
                }
                if (!entry.level) {
                    entry.level = this.extractLogLevel(field);
                }
            }

            entry.message = entry.rawLine;
        } else {
            entry.parsed = false;
            this.parsePlainTextLine(entry);
        }
    }

    /**
     * Parse plain text line (fallback)
     */
    parsePlainTextLine(entry) {
        entry.parsed = true;
        entry.message = entry.rawLine;
        entry.timestamp = this.parseTimestamp(entry.rawLine);
        entry.level = this.extractLogLevel(entry.rawLine);
        
        // Extract any key-value pairs
        const kvPattern = /(\w+)[:=]\s*([^\s,]+)/g;
        let match;
        while ((match = kvPattern.exec(entry.rawLine)) !== null) {
            entry.fields[match[1]] = match[2];
        }
    }

    /**
     * Parse timestamp from string
     */
    parseTimestamp(timestampStr) {
        if (!timestampStr) return null;

        try {
            // Try moment.js parsing with various formats
            const formats = [
                'YYYY-MM-DD HH:mm:ss',
                'YYYY-MM-DD HH:mm:ss.SSS',
                'YYYY-MM-DDTHH:mm:ss',
                'YYYY-MM-DDTHH:mm:ss.SSS',
                'DD/MMM/YYYY:HH:mm:ss',
                'MMM DD HH:mm:ss',
                'MM/DD/YYYY HH:mm:ss',
                'DD-MM-YYYY HH:mm:ss'
            ];

            for (const format of formats) {
                const parsed = moment(timestampStr, format, true);
                if (parsed.isValid()) {
                    return parsed.toDate();
                }
            }

            // Try ISO string parsing
            const isoDate = new Date(timestampStr);
            if (!isNaN(isoDate.getTime())) {
                return isoDate;
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Extract log level from text
     */
    extractLogLevel(text) {
        if (!text) return null;

        const upperText = text.toString().toUpperCase();
        for (const level of this.logLevels) {
            if (upperText.includes(level)) {
                return level;
            }
        }
        return null;
    }

    /**
     * Calculate parsing statistics
     */
    calculateStats(entries, formatInfo) {
        const totalEntries = entries.length;
        const parsedEntries = entries.filter(entry => entry.parsed).length;
        const parseSuccessRate = totalEntries > 0 ? parsedEntries / totalEntries : 0;

        const levelCounts = {};
        const timestampCounts = { with_timestamp: 0, without_timestamp: 0 };

        entries.forEach(entry => {
            if (entry.level) {
                levelCounts[entry.level] = (levelCounts[entry.level] || 0) + 1;
            }
            
            if (entry.timestamp) {
                timestampCounts.with_timestamp++;
            } else {
                timestampCounts.without_timestamp++;
            }
        });

        return {
            total_entries: totalEntries,
            parsed_entries: parsedEntries,
            parse_success_rate: parseSuccessRate,
            detected_format: formatInfo.format,
            format_confidence: formatInfo.confidence,
            level_distribution: levelCounts,
            timestamp_coverage: timestampCounts
        };
    }
}

module.exports = { LogParser, LogEntry };
