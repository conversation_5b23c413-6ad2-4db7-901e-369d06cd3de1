/**
 * Log Format Detection Module
 * Automatically detects the format of log files without manual configuration.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);

class LogFormatDetector {
    constructor() {
        this.commonPatterns = {
            apache_access: /^\d+\.\d+\.\d+\.\d+ - - \[.*?\] ".*?" \d+ \d+/,
            nginx_access: /^\d+\.\d+\.\d+\.\d+ - - \[.*?\] ".*?" \d+ \d+ ".*?" ".*?"/,
            syslog: /^[A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+\w+\s+.*/,
            timestamp_prefix: /^\d{4}-\d{2}-\d{2}[\s\T]\d{2}:\d{2}:\d{2}/,
            java_log: /^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}[,\.]\d{3}\s+\[(INFO|DEBUG|WARN|ERROR|FATAL)\]/,
            python_log: /^\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2},\d{3}\s+-\s+(INFO|DEBUG|WARNING|ERROR|CRITICAL)/,
            enterprise_structured: /^\[\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\[.*?\]\s*:\s*.*/
        };
        
        this.logLevels = ['DEBUG', 'INFO', 'WARN', 'WARNING', 'ERROR', 'FATAL', 'CRITICAL', 'TRACE'];
    }

    /**
     * Detect file encoding (simplified for Node.js)
     */
    detectEncoding(buffer) {
        // Simple encoding detection - in production, you might want to use a library like 'iconv-lite'
        // Check for BOM
        if (buffer.length >= 3 && buffer[0] === 0xEF && buffer[1] === 0xBB && buffer[2] === 0xBF) {
            return 'utf8';
        }
        // Default to utf8
        return 'utf8';
    }

    /**
     * Detect the format of a log file
     */
    async detectFormat(filePath, sampleLines = null) {
        try {
            let lines = sampleLines;
            
            if (!lines) {
                const buffer = await readFile(filePath);
                const encoding = this.detectEncoding(buffer);
                const content = buffer.toString(encoding);
                lines = content.split('\n').slice(0, 50).filter(line => line.trim());
            }

            if (!lines || lines.length === 0) {
                return { format: 'unknown', confidence: 0.0 };
            }

            // Check for enterprise structured format first (higher priority)
            const enterpriseResult = this.checkEnterpriseStructuredFormat(lines);
            if (enterpriseResult.confidence > 0.8) {
                return enterpriseResult;
            }

            // Check for structured formats
            const structuredResult = this.checkStructuredFormats(lines);
            if (structuredResult.confidence > 0.7) {
                return structuredResult;
            }

            // Check for JSON format
            const jsonResult = this.checkJsonFormat(lines);
            if (jsonResult.confidence > 0.8) {
                return jsonResult;
            }

            // Check for common log formats
            const commonResult = this.checkCommonFormats(lines);
            if (commonResult.confidence > 0.6) {
                return commonResult;
            }

            // Check for delimited formats
            const delimitedResult = this.checkDelimitedFormats(lines);
            if (delimitedResult.confidence > 0.5) {
                return delimitedResult;
            }

            // Default to plain text
            return {
                format: 'plain_text',
                confidence: 0.3,
                pattern: null
            };

        } catch (error) {
            console.error('Error detecting log format:', error);
            return { format: 'unknown', confidence: 0.0, error: error.message };
        }
    }

    /**
     * Check for enterprise structured log format with bracketed fields
     */
    checkEnterpriseStructuredFormat(lines) {
        if (!lines || lines.length === 0) {
            return { format: 'unknown', confidence: 0.0 };
        }

        // Pattern for enterprise structured logs: [field1][field2]...[fieldN] : data
        const enterprisePattern = /^\[\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\](?:\[.*?\]){13,15}\s*:\s*.*/;
        
        let matches = 0;
        let fieldStructure = {};

        for (const line of lines) {
            if (enterprisePattern.test(line)) {
                matches++;
                
                // Extract bracketed fields for structure analysis
                const bracketPattern = /\[(.*?)\]/g;
                const fields = [];
                let match;
                
                const beforeColon = line.split(' : ')[0];
                while ((match = bracketPattern.exec(beforeColon)) !== null) {
                    fields.push(match[1]);
                }

                if (fields.length >= 14) {
                    fieldStructure = {
                        timestamp: fields[0] || null,
                        level: fields[1] || null,
                        module: fields[2] || null,
                        session_id: fields[3] || null,
                        platform: fields[4] || null,
                        customer_id: fields[5] || null,
                        client_ip: fields[6] || null,
                        request_id: fields[7] || null,
                        audit_type: fields[8] || null,
                        app_name: fields[9] || null,
                        response_time: fields[10] || null,
                        status: fields[11] || null,
                        method: fields[12] || null,
                        origin_url: fields[13] || null,
                        extra_data: fields[14] || null
                    };
                }
            }
        }

        const confidence = lines.length > 0 ? matches / lines.length : 0;

        if (confidence > 0.8) {
            return {
                format: 'enterprise_structured',
                confidence: confidence,
                pattern: enterprisePattern.source,
                fieldStructure: fieldStructure,
                fieldCount: Object.keys(fieldStructure).length
            };
        }

        return { format: 'unknown', confidence: confidence };
    }

    /**
     * Check for JSON format
     */
    checkJsonFormat(lines) {
        let validJsonLines = 0;
        
        for (const line of lines) {
            try {
                JSON.parse(line.trim());
                validJsonLines++;
            } catch (e) {
                // Not valid JSON
            }
        }

        const confidence = lines.length > 0 ? validJsonLines / lines.length : 0;
        
        if (confidence > 0.8) {
            return {
                format: 'json',
                confidence: confidence,
                pattern: null
            };
        }

        return { format: 'unknown', confidence: confidence };
    }

    /**
     * Check for common log formats
     */
    checkCommonFormats(lines) {
        const results = {};
        
        for (const [formatName, pattern] of Object.entries(this.commonPatterns)) {
            let matches = 0;
            
            for (const line of lines) {
                if (pattern.test(line)) {
                    matches++;
                }
            }
            
            const confidence = lines.length > 0 ? matches / lines.length : 0;
            results[formatName] = { confidence, pattern: pattern.source };
        }

        // Find the best match
        let bestMatch = { format: 'unknown', confidence: 0.0 };
        
        for (const [formatName, result] of Object.entries(results)) {
            if (result.confidence > bestMatch.confidence) {
                bestMatch = {
                    format: formatName,
                    confidence: result.confidence,
                    pattern: result.pattern
                };
            }
        }

        return bestMatch;
    }

    /**
     * Check for structured formats
     */
    checkStructuredFormats(lines) {
        // Check for key-value patterns
        const kvPatterns = [
            /\w+=[^\s]+/g,  // key=value
            /\w+:\s*[^\s]+/g,  // key: value
            /\w+\s*=\s*"[^"]*"/g  // key="value"
        ];

        let structuredLines = 0;
        
        for (const line of lines) {
            for (const pattern of kvPatterns) {
                const matches = line.match(pattern);
                if (matches && matches.length >= 3) {
                    structuredLines++;
                    break;
                }
            }
        }

        const confidence = lines.length > 0 ? structuredLines / lines.length : 0;
        
        if (confidence > 0.7) {
            return {
                format: 'structured',
                confidence: confidence,
                pattern: 'key-value pairs'
            };
        }

        return { format: 'unknown', confidence: confidence };
    }

    /**
     * Check for delimited formats (CSV, TSV, etc.)
     */
    checkDelimitedFormats(lines) {
        const delimiters = [',', '\t', '|', ';'];
        const results = {};

        for (const delimiter of delimiters) {
            let consistentFieldCount = true;
            let fieldCounts = [];
            
            for (const line of lines.slice(0, 10)) { // Check first 10 lines
                const fields = line.split(delimiter);
                fieldCounts.push(fields.length);
            }

            // Check if field counts are consistent
            if (fieldCounts.length > 0) {
                const firstCount = fieldCounts[0];
                consistentFieldCount = fieldCounts.every(count => count === firstCount) && firstCount > 1;
            }

            if (consistentFieldCount && fieldCounts.length > 0) {
                results[delimiter] = {
                    confidence: 0.8,
                    fieldCount: fieldCounts[0]
                };
            }
        }

        // Find the best delimiter
        let bestDelimiter = null;
        let bestResult = { confidence: 0.0 };

        for (const [delimiter, result] of Object.entries(results)) {
            if (result.confidence > bestResult.confidence) {
                bestDelimiter = delimiter;
                bestResult = result;
            }
        }

        if (bestDelimiter) {
            return {
                format: 'delimited',
                confidence: bestResult.confidence,
                delimiter: bestDelimiter,
                fieldCount: bestResult.fieldCount
            };
        }

        return { format: 'unknown', confidence: 0.0 };
    }
}

module.exports = LogFormatDetector;
