/**
 * Express.js Application for Log Analyzer
 * Main entry point for the backend API server.
 */

const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');
const compression = require('compression');
const helmet = require('helmet');
const morgan = require('morgan');

const { LogParser } = require('./src/log-parser/parser');
const LogAnalyzer = require('./src/analysis/analyzer');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}-${file.originalname}`;
        cb(null, uniqueName);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB max file size
    },
    fileFilter: (req, file, cb) => {
        // Accept all file types for log analysis
        cb(null, true);
    }
});

// Initialize components
const logParser = new LogParser();
const logAnalyzer = new LogAnalyzer();

// Store analysis results in memory (in production, use a database)
const analysisCache = new Map();

// Cleanup old files and cache entries periodically
setInterval(() => {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Clean up cache
    for (const [key, value] of analysisCache.entries()) {
        if (now - value.timestamp > maxAge) {
            analysisCache.delete(key);
        }
    }

    // Clean up uploaded files
    const uploadDir = path.join(__dirname, 'uploads');
    if (fs.existsSync(uploadDir)) {
        fs.readdir(uploadDir, (err, files) => {
            if (err) return;
            
            files.forEach(file => {
                const filePath = path.join(uploadDir, file);
                fs.stat(filePath, (err, stats) => {
                    if (err) return;
                    if (now - stats.mtime.getTime() > maxAge) {
                        fs.unlink(filePath, () => {});
                    }
                });
            });
        });
    }
}, 60 * 60 * 1000); // Run every hour

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        services: {
            log_parser: 'operational',
            analyzer: 'operational',
            cache: `${analysisCache.size} analyses cached`
        }
    });
});

// Upload and analyze log file
app.post('/api/upload', upload.single('file'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const filePath = req.file.path;
        const analysisId = uuidv4();

        // Parse the log file
        console.log(`Starting analysis for file: ${req.file.originalname}`);
        const parsedData = await logParser.parseFile(filePath);

        // Analyze the parsed data
        const analysisResults = logAnalyzer.analyzeLogs(parsedData);

        // Cache the results
        analysisCache.set(analysisId, {
            analysis_results: analysisResults,
            entries: parsedData.entries,
            file_info: {
                original_name: req.file.originalname,
                size: req.file.size,
                upload_time: new Date().toISOString()
            },
            timestamp: Date.now()
        });

        // Clean up the uploaded file
        fs.unlink(filePath, (err) => {
            if (err) console.error('Error deleting uploaded file:', err);
        });

        res.json({
            analysis_id: analysisId,
            summary: analysisResults.summary,
            file_info: {
                name: req.file.originalname,
                size: req.file.size,
                format: parsedData.format_info?.format || 'unknown'
            }
        });

    } catch (error) {
        console.error('Error processing file:', error);
        
        // Clean up file on error
        if (req.file && req.file.path) {
            fs.unlink(req.file.path, () => {});
        }

        res.status(500).json({
            error: 'Failed to process file',
            details: error.message
        });
    }
});

// Get complete analysis results
app.get('/api/analysis/:analysisId', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        analysis_results: cachedData.analysis_results,
        file_info: cachedData.file_info
    });
});

// Get analysis summary
app.get('/api/analysis/:analysisId/summary', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        summary: cachedData.analysis_results.summary
    });
});

// Get error analysis
app.get('/api/analysis/:analysisId/errors', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        error_analysis: cachedData.analysis_results.error_analysis
    });
});

// Get timeline analysis
app.get('/api/analysis/:analysisId/timeline', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        time_analysis: cachedData.analysis_results.time_analysis
    });
});

// Get pattern analysis
app.get('/api/analysis/:analysisId/patterns', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        pattern_analysis: cachedData.analysis_results.pattern_analysis
    });
});

// Get anomaly detection results
app.get('/api/analysis/:analysisId/anomalies', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        anomaly_detection: cachedData.analysis_results.anomaly_detection
    });
});

// Get performance analysis (enterprise logs)
app.get('/api/analysis/:analysisId/performance', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        performance_analysis: cachedData.analysis_results.performance_analysis || {}
    });
});

// Get customer analysis (enterprise logs)
app.get('/api/analysis/:analysisId/customers', (req, res) => {
    const { analysisId } = req.params;
    
    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        customer_analysis: cachedData.analysis_results.customer_analysis || {}
    });
});

// Get application analysis (enterprise logs)
app.get('/api/analysis/:analysisId/applications', (req, res) => {
    const { analysisId } = req.params;

    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    res.json({
        analysis_id: analysisId,
        application_analysis: cachedData.analysis_results.application_analysis || {}
    });
});

// Search through log entries
app.get('/api/analysis/:analysisId/search', (req, res) => {
    const { analysisId } = req.params;
    const { q, level, start_time, end_time, limit = 100, offset = 0 } = req.query;

    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);
    let entries = cachedData.entries || [];

    // Apply filters
    if (q) {
        const searchTerm = q.toLowerCase();
        entries = entries.filter(entry =>
            (entry.message && entry.message.toLowerCase().includes(searchTerm)) ||
            (entry.raw_line && entry.raw_line.toLowerCase().includes(searchTerm))
        );
    }

    if (level) {
        entries = entries.filter(entry => entry.level === level.toUpperCase());
    }

    if (start_time) {
        const startTime = new Date(start_time);
        entries = entries.filter(entry =>
            entry.timestamp && new Date(entry.timestamp) >= startTime
        );
    }

    if (end_time) {
        const endTime = new Date(end_time);
        entries = entries.filter(entry =>
            entry.timestamp && new Date(entry.timestamp) <= endTime
        );
    }

    // Apply pagination
    const totalResults = entries.length;
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedEntries = entries.slice(startIndex, endIndex);

    res.json({
        analysis_id: analysisId,
        results: paginatedEntries,
        total_results: totalResults,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: endIndex < totalResults
    });
});

// Export analysis results
app.get('/api/analysis/:analysisId/export', (req, res) => {
    const { analysisId } = req.params;
    const { format = 'json' } = req.query;

    if (!analysisCache.has(analysisId)) {
        return res.status(404).json({ error: 'Analysis not found' });
    }

    const cachedData = analysisCache.get(analysisId);

    if (format.toLowerCase() === 'json') {
        res.json(cachedData.analysis_results);
    } else if (format.toLowerCase() === 'csv') {
        // Convert to CSV format
        const entries = cachedData.entries.slice(0, 1000); // Limit to 1000 entries

        let csvContent = 'timestamp,level,message,source\n';
        entries.forEach(entry => {
            const timestamp = entry.timestamp || '';
            const level = entry.level || '';
            const message = (entry.message || '').replace(/"/g, '""'); // Escape quotes
            const source = entry.fields?.module || entry.fields?.app_name || '';

            csvContent += `"${timestamp}","${level}","${message}","${source}"\n`;
        });

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=analysis_${analysisId}.csv`);
        res.send(csvContent);
    } else {
        res.status(400).json({ error: 'Unsupported format' });
    }
});

// API documentation endpoint
app.get('/api/docs', (req, res) => {
    const docs = {
        title: 'Log Analyzer API',
        version: '1.0.0',
        description: 'API for analyzing log files with enterprise structured format support',
        endpoints: {
            'POST /api/upload': 'Upload and analyze log files',
            'GET /api/analysis/{id}': 'Get complete analysis results',
            'GET /api/analysis/{id}/summary': 'Get analysis summary',
            'GET /api/analysis/{id}/errors': 'Get error analysis',
            'GET /api/analysis/{id}/timeline': 'Get timeline analysis',
            'GET /api/analysis/{id}/patterns': 'Get pattern analysis',
            'GET /api/analysis/{id}/anomalies': 'Get anomaly detection results',
            'GET /api/analysis/{id}/performance': 'Get performance analysis (enterprise logs)',
            'GET /api/analysis/{id}/customers': 'Get customer analysis (enterprise logs)',
            'GET /api/analysis/{id}/applications': 'Get application analysis (enterprise logs)',
            'GET /api/analysis/{id}/search': 'Search through log entries',
            'GET /api/analysis/{id}/export': 'Export analysis results',
            'GET /api/health': 'Health check endpoint',
            'GET /api/docs': 'This documentation'
        },
        supported_formats: [
            'enterprise_structured',
            'json',
            'apache_access',
            'nginx_access',
            'syslog',
            'delimited',
            'plain_text'
        ]
    };
    res.json(docs);
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Start the server
app.listen(PORT, () => {
    console.log(`🚀 Log Analyzer backend server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📋 API docs: http://localhost:${PORT}/api/docs`);
});
