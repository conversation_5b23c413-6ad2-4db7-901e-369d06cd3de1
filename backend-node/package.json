{"name": "log-analyzer-backend", "version": "1.0.0", "description": "Node.js backend for Log Analyzer application with enterprise structured log parsing capabilities", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "keywords": ["log-analyzer", "log-parsing", "enterprise-logs", "analytics", "monitoring", "express", "nodejs"], "author": "Log Analyzer Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "compression": "^1.7.4", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "winston": "^3.11.0", "mime-types": "^2.1.35", "iconv-lite": "^0.6.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.0.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/log-analyzer.git"}, "bugs": {"url": "https://github.com/your-org/log-analyzer/issues"}, "homepage": "https://github.com/your-org/log-analyzer#readme"}