{"name": "log-analyzer-frontend", "version": "1.0.0", "description": "Frontend for Log Analyzer Application", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.15.0", "axios": "^1.5.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "react-dropzone": "^14.2.3", "react-table": "^7.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-icons": "^4.11.0", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.198"}, "proxy": "http://localhost:5000"}