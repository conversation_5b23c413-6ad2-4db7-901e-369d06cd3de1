import React from 'react';
import { FiActivity, FiAlertTriangle, FiCheckCircle, FiXCircle } from 'react-icons/fi';

const SummaryCards = ({ summary }) => {
  const getHealthStatusColor = (status) => {
    switch (status) {
      case 'Healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Caution':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Warning':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Critical':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getHealthIcon = (status) => {
    switch (status) {
      case 'Healthy':
        return <FiCheckCircle className="h-5 w-5" />;
      case 'Caution':
      case 'Warning':
        return <FiAlertTriangle className="h-5 w-5" />;
      case 'Critical':
        return <FiXCircle className="h-5 w-5" />;
      default:
        return <FiActivity className="h-5 w-5" />;
    }
  };

  const cards = [
    {
      title: 'Total Entries',
      value: summary.total_entries?.toLocaleString() || '0',
      icon: <FiActivity className="h-6 w-6 text-blue-600" />,
      bgColor: 'bg-blue-50',
      description: 'Log entries processed'
    },
    {
      title: 'Error Rate',
      value: `${summary.error_rate || 0}%`,
      icon: <FiXCircle className="h-6 w-6 text-red-600" />,
      bgColor: 'bg-red-50',
      description: `${summary.error_count || 0} errors found`,
      trend: summary.error_rate > 5 ? 'high' : summary.error_rate > 1 ? 'medium' : 'low'
    },
    {
      title: 'Warning Rate',
      value: `${summary.warning_rate || 0}%`,
      icon: <FiAlertTriangle className="h-6 w-6 text-yellow-600" />,
      bgColor: 'bg-yellow-50',
      description: `${summary.warning_count || 0} warnings found`,
      trend: summary.warning_rate > 20 ? 'high' : summary.warning_rate > 10 ? 'medium' : 'low'
    },
    {
      title: 'System Health',
      value: summary.health_status || 'Unknown',
      icon: getHealthIcon(summary.health_status),
      bgColor: 'bg-gray-50',
      description: 'Overall system status',
      customColor: getHealthStatusColor(summary.health_status)
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {cards.map((card, index) => (
        <div key={index} className="bg-white rounded-lg shadow-lg p-6 card-hover">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">
                {card.title}
              </p>
              <div className="flex items-center space-x-2">
                {card.customColor ? (
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${card.customColor}`}>
                    {card.icon}
                    <span className="ml-1">{card.value}</span>
                  </span>
                ) : (
                  <p className="text-2xl font-bold text-gray-900">
                    {card.value}
                  </p>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {card.description}
              </p>
              
              {/* Trend indicator */}
              {card.trend && (
                <div className="mt-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    card.trend === 'high' 
                      ? 'bg-red-100 text-red-800'
                      : card.trend === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {card.trend === 'high' ? '⚠️ High' : card.trend === 'medium' ? '⚡ Medium' : '✅ Low'}
                  </span>
                </div>
              )}
            </div>
            
            {!card.customColor && (
              <div className={`p-3 rounded-lg ${card.bgColor}`}>
                {card.icon}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SummaryCards;
