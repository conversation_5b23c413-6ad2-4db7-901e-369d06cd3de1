import React, { useState, useEffect } from 'react';
import { useAnalysis } from '../context/AnalysisContext';
import SummaryCards from './SummaryCards';
import ErrorAnalysisChart from './charts/ErrorAnalysisChart';
import Timeline<PERSON>hart from './charts/TimelineChart';
import PatternAnalysisChart from './charts/PatternAnalysisChart';
import PerformanceChart from './charts/PerformanceChart';
import CustomerChart from './charts/CustomerChart';
import ApplicationChart from './charts/ApplicationChart';
import LogSearch from './LogSearch';
import InsightsPanel from './InsightsPanel';
import { FiDownload, FiRefreshCw } from 'react-icons/fi';

const Dashboard = ({ analysisId }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [analysisData, setAnalysisData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const {
    getAnalysis,
    getAnalysisSummary,
    getErrorAnalysis,
    getTimeAnalysis,
    getPatternAnalysis,
    exportAnalysis
  } = useAnalysis();

  useEffect(() => {
    loadAnalysisData();
  }, [analysisId]);

  const loadAnalysisData = async () => {
    if (!analysisId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const [summary, errorAnalysis, timeAnalysis, patternAnalysis] = await Promise.all([
        getAnalysisSummary(analysisId),
        getErrorAnalysis(analysisId),
        getTimeAnalysis(analysisId),
        getPatternAnalysis(analysisId)
      ]);

      // Try to get enterprise-specific analysis data
      let performanceAnalysis = {};
      let customerAnalysis = {};
      let applicationAnalysis = {};

      try {
        const response = await fetch(`/api/analysis/${analysisId}/performance`);
        if (response.ok) {
          const data = await response.json();
          performanceAnalysis = data;
        }
      } catch (e) {
        console.log('Performance analysis not available');
      }

      try {
        const response = await fetch(`/api/analysis/${analysisId}/customers`);
        if (response.ok) {
          const data = await response.json();
          customerAnalysis = data;
        }
      } catch (e) {
        console.log('Customer analysis not available');
      }

      try {
        const response = await fetch(`/api/analysis/${analysisId}/applications`);
        if (response.ok) {
          const data = await response.json();
          applicationAnalysis = data;
        }
      } catch (e) {
        console.log('Application analysis not available');
      }

      setAnalysisData({
        summary,
        errorAnalysis,
        timeAnalysis,
        patternAnalysis,
        performanceAnalysis,
        customerAnalysis,
        applicationAnalysis
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format) => {
    try {
      await exportAnalysis(analysisId, format);
    } catch (err) {
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="loading-spinner"></div>
        <span className="ml-3 text-gray-600">Loading analysis...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-medium mb-2">Error Loading Analysis</h3>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={loadAnalysisData}
          className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <FiRefreshCw className="h-4 w-4" />
          <span>Retry</span>
        </button>
      </div>
    );
  }

  if (!analysisData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No analysis data available</p>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'errors', label: 'Error Analysis', icon: '🚨' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'customers', label: 'Customers', icon: '👥' },
    { id: 'applications', label: 'Applications', icon: '📱' },
    { id: 'patterns', label: 'Patterns', icon: '🔍' },
    { id: 'timeline', label: 'Timeline', icon: '📈' },
    { id: 'search', label: 'Search Logs', icon: '🔎' }
  ];

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Analysis Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            File: {analysisData.summary.filename} • 
            {analysisData.summary.summary.total_entries.toLocaleString()} entries
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleExport('json')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FiDownload className="h-4 w-4" />
            <span>Export JSON</span>
          </button>
          
          <button
            onClick={() => handleExport('csv')}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <FiDownload className="h-4 w-4" />
            <span>Export CSV</span>
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <SummaryCards summary={analysisData.summary.summary} />

      {/* Insights Panel */}
      <InsightsPanel 
        insights={analysisData.summary.insights}
        recommendations={analysisData.summary.recommendations}
      />

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <ErrorAnalysisChart data={analysisData.errorAnalysis} />
                <PatternAnalysisChart data={analysisData.patternAnalysis} />
              </div>
              {analysisData.performanceAnalysis?.performance_analysis && (
                <PerformanceChart data={analysisData.performanceAnalysis} />
              )}
              <TimelineChart data={analysisData.timeAnalysis} />
            </div>
          )}

          {activeTab === 'errors' && (
            <ErrorAnalysisChart data={analysisData.errorAnalysis} detailed={true} />
          )}

          {activeTab === 'performance' && (
            <PerformanceChart data={analysisData.performanceAnalysis} detailed={true} />
          )}

          {activeTab === 'customers' && (
            <CustomerChart data={analysisData.customerAnalysis} detailed={true} />
          )}

          {activeTab === 'applications' && (
            <ApplicationChart data={analysisData.applicationAnalysis} detailed={true} />
          )}

          {activeTab === 'patterns' && (
            <PatternAnalysisChart data={analysisData.patternAnalysis} detailed={true} />
          )}

          {activeTab === 'timeline' && (
            <TimelineChart data={analysisData.timeAnalysis} detailed={true} />
          )}

          {activeTab === 'search' && (
            <LogSearch analysisId={analysisId} />
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
