import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar, Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const PerformanceChart = ({ data, detailed = false }) => {
  if (!data || !data.performance_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Analysis</h3>
        <p className="text-gray-500">No performance data available</p>
      </div>
    );
  }

  const performanceData = data.performance_analysis;

  // Application performance chart
  const appPerformance = performanceData.performance_by_app || {};
  const appNames = Object.keys(appPerformance);
  
  const appChartData = {
    labels: appNames,
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: appNames.map(app => appPerformance[app].avg_response_time),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Request Count',
        data: appNames.map(app => appPerformance[app].request_count),
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
        yAxisID: 'y1',
      },
    ],
  };

  const appChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Performance by Application',
      },
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Response Time (ms)',
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Request Count',
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  // Module performance chart
  const modulePerformance = performanceData.performance_by_module || {};
  const moduleNames = Object.keys(modulePerformance).slice(0, 10); // Top 10 modules
  
  const moduleChartData = {
    labels: moduleNames,
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: moduleNames.map(module => modulePerformance[module].avg_response_time),
        backgroundColor: 'rgba(139, 92, 246, 0.6)',
        borderColor: 'rgba(139, 92, 246, 1)',
        borderWidth: 1,
      },
    ],
  };

  const moduleChartOptions = {
    responsive: true,
    indexAxis: 'y',
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Performance by Module (Top 10)',
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)',
        },
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Performance Analysis</h3>
        <div className="text-sm text-gray-500">
          {performanceData.total_requests?.toLocaleString()} total requests
        </div>
      </div>

      {/* Performance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Avg Response Time</div>
          <div className="text-blue-900 text-lg font-bold">
            {performanceData.avg_response_time?.toFixed(0)}ms
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">95th Percentile</div>
          <div className="text-green-900 text-lg font-bold">
            {performanceData.p95_response_time?.toFixed(0)}ms
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="text-yellow-600 text-sm font-medium">99th Percentile</div>
          <div className="text-yellow-900 text-lg font-bold">
            {performanceData.p99_response_time?.toFixed(0)}ms
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Slow Requests</div>
          <div className="text-red-900 text-lg font-bold">
            {performanceData.slow_request_count || 0}
          </div>
          <div className="text-red-600 text-xs">
            {performanceData.total_requests > 0 
              ? ((performanceData.slow_request_count || 0) / performanceData.total_requests * 100).toFixed(1)
              : 0}% of total
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Application Performance */}
        {appNames.length > 0 && (
          <div>
            <Bar data={appChartData} options={appChartOptions} />
          </div>
        )}

        {/* Module Performance */}
        {detailed && moduleNames.length > 0 && (
          <div>
            <Bar data={moduleChartData} options={moduleChartOptions} />
          </div>
        )}

        {/* Slow Requests Table */}
        {detailed && performanceData.slow_requests && performanceData.slow_requests.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Slowest Requests</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Response Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Application
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Module
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      URL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {performanceData.slow_requests.slice(0, 10).map((request, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                        {request.response_time}ms
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.app_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.module}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {request.url}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.timestamp ? new Date(request.timestamp).toLocaleString() : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceChart;
