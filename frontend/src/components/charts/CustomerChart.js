import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const CustomerChart = ({ data, detailed = false }) => {
  if (!data || !data.customer_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Analysis</h3>
        <p className="text-gray-500">No customer data available</p>
      </div>
    );
  }

  const customerData = data.customer_analysis;

  // Top customers activity chart
  const topCustomers = customerData.top_customers || [];
  const customerChartData = {
    labels: topCustomers.slice(0, 10).map(customer => 
      customer.customer_id.length > 15 
        ? customer.customer_id.substring(0, 15) + '...'
        : customer.customer_id
    ),
    datasets: [
      {
        label: 'Activity Count',
        data: topCustomers.slice(0, 10).map(customer => customer.activity_count),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Error Count',
        data: topCustomers.slice(0, 10).map(customer => customer.error_count),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
      },
    ],
  };

  const customerChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Top 10 Most Active Customers',
      },
      tooltip: {
        callbacks: {
          afterLabel: function(context) {
            const customer = topCustomers[context.dataIndex];
            return [
              `Apps: ${customer.apps_used.join(', ')}`,
              `Platforms: ${customer.platforms_used.join(', ')}`
            ];
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Customer platform distribution
  const platformCounts = {};
  topCustomers.forEach(customer => {
    customer.platforms_used.forEach(platform => {
      platformCounts[platform] = (platformCounts[platform] || 0) + 1;
    });
  });

  const platformChartData = {
    labels: Object.keys(platformCounts),
    datasets: [
      {
        data: Object.values(platformCounts),
        backgroundColor: [
          '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
          '#ec4899', '#06b6d4', '#84cc16', '#f97316', '#6366f1'
        ],
        borderWidth: 1,
      },
    ],
  };

  const platformChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Customer Platform Distribution',
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Customer Analysis</h3>
        <div className="text-sm text-gray-500">
          {customerData.total_unique_customers?.toLocaleString()} unique customers
        </div>
      </div>

      {/* Customer Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Customers</div>
          <div className="text-blue-900 text-lg font-bold">
            {customerData.total_unique_customers?.toLocaleString() || 0}
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Most Active</div>
          <div className="text-green-900 text-lg font-bold">
            {topCustomers.length > 0 ? topCustomers[0].customer_id.substring(0, 10) + '...' : 'N/A'}
          </div>
          <div className="text-green-600 text-xs">
            {topCustomers.length > 0 ? `${topCustomers[0].activity_count} activities` : ''}
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Error-Prone Customers</div>
          <div className="text-red-900 text-lg font-bold">
            {customerData.error_prone_customers?.length || 0}
          </div>
          <div className="text-red-600 text-xs">
            customers with errors
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Top Customers Activity */}
        {topCustomers.length > 0 && (
          <div>
            <Bar data={customerChartData} options={customerChartOptions} />
          </div>
        )}

        {/* Platform Distribution */}
        {detailed && Object.keys(platformCounts).length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <Doughnut data={platformChartData} options={platformChartOptions} />
            </div>
            
            <div className="space-y-3">
              <h4 className="text-md font-medium text-gray-900">Platform Breakdown</h4>
              {Object.entries(platformCounts)
                .sort(([,a], [,b]) => b - a)
                .map(([platform, count]) => (
                  <div key={platform} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">{platform}</span>
                    <span className="text-sm text-gray-600">{count} customers</span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Error-Prone Customers Table */}
        {detailed && customerData.error_prone_customers && customerData.error_prone_customers.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Customers with Most Errors</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Error Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Activity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Error Rate
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {customerData.error_prone_customers.slice(0, 10).map((customer, index) => {
                    const customerDetail = topCustomers.find(c => c.customer_id === customer.customer_id);
                    const errorRate = customerDetail 
                      ? (customer.error_count / customerDetail.activity_count * 100).toFixed(1)
                      : 'N/A';
                    
                    return (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {customer.customer_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                          {customer.error_count}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {customerDetail?.activity_count || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {errorRate}%
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Top Customer Details */}
        {detailed && topCustomers.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Top Customer Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {topCustomers.slice(0, 6).map((customer, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="font-medium text-gray-900 mb-2">
                    {customer.customer_id}
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>Activity: {customer.activity_count} requests</div>
                    <div>Errors: {customer.error_count}</div>
                    <div>Apps: {customer.apps_used.join(', ') || 'None'}</div>
                    <div>Platforms: {customer.platforms_used.join(', ') || 'None'}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerChart;
