import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const ApplicationChart = ({ data, detailed = false }) => {
  if (!data || !data.application_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Analysis</h3>
        <p className="text-gray-500">No application data available</p>
      </div>
    );
  }

  const appData = data.application_analysis;
  const applications = appData.application_summary || [];

  // Application activity chart
  const appChartData = {
    labels: applications.slice(0, 10).map(app => app.app_name),
    datasets: [
      {
        label: 'Activity Count',
        data: applications.slice(0, 10).map(app => app.activity_count),
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
        yAxisID: 'y',
      },
      {
        label: 'Error Rate (%)',
        data: applications.slice(0, 10).map(app => app.error_rate),
        backgroundColor: 'rgba(239, 68, 68, 0.6)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 1,
        yAxisID: 'y1',
      },
    ],
  };

  const appChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Application Activity and Error Rates',
      },
    },
    scales: {
      y: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: 'Activity Count',
        },
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        title: {
          display: true,
          text: 'Error Rate (%)',
        },
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  // Application performance chart
  const perfChartData = {
    labels: applications.slice(0, 10).map(app => app.app_name),
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: applications.slice(0, 10).map(app => app.avg_response_time),
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
    ],
  };

  const perfChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Average Response Time by Application',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Response Time (ms)',
        },
      },
    },
  };

  // Application distribution pie chart
  const distributionData = {
    labels: applications.slice(0, 8).map(app => app.app_name),
    datasets: [
      {
        data: applications.slice(0, 8).map(app => app.activity_count),
        backgroundColor: [
          '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
          '#ec4899', '#06b6d4', '#84cc16'
        ],
        borderWidth: 1,
      },
    ],
  };

  const distributionOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Application Usage Distribution',
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Application Analysis</h3>
        <div className="text-sm text-gray-500">
          {appData.total_applications} applications monitored
        </div>
      </div>

      {/* Application Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Total Applications</div>
          <div className="text-blue-900 text-lg font-bold">
            {appData.total_applications || 0}
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Most Active</div>
          <div className="text-green-900 text-lg font-bold">
            {applications.length > 0 ? applications[0].app_name : 'N/A'}
          </div>
          <div className="text-green-600 text-xs">
            {applications.length > 0 ? `${applications[0].activity_count} requests` : ''}
          </div>
        </div>
        
        <div className="bg-yellow-50 rounded-lg p-4">
          <div className="text-yellow-600 text-sm font-medium">Fastest App</div>
          <div className="text-yellow-900 text-lg font-bold">
            {applications.length > 0 
              ? applications.reduce((fastest, app) => 
                  app.avg_response_time < fastest.avg_response_time ? app : fastest
                ).app_name
              : 'N/A'
            }
          </div>
          <div className="text-yellow-600 text-xs">
            {applications.length > 0 
              ? `${applications.reduce((fastest, app) => 
                  app.avg_response_time < fastest.avg_response_time ? app : fastest
                ).avg_response_time.toFixed(0)}ms avg`
              : ''
            }
          </div>
        </div>
        
        <div className="bg-red-50 rounded-lg p-4">
          <div className="text-red-600 text-sm font-medium">Highest Error Rate</div>
          <div className="text-red-900 text-lg font-bold">
            {applications.length > 0 
              ? applications.reduce((highest, app) => 
                  app.error_rate > highest.error_rate ? app : highest
                ).app_name
              : 'N/A'
            }
          </div>
          <div className="text-red-600 text-xs">
            {applications.length > 0 
              ? `${applications.reduce((highest, app) => 
                  app.error_rate > highest.error_rate ? app : highest
                ).error_rate}% error rate`
              : ''
            }
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Activity and Error Rates */}
        {applications.length > 0 && (
          <div>
            <Bar data={appChartData} options={appChartOptions} />
          </div>
        )}

        {/* Performance and Distribution */}
        {detailed && applications.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <Bar data={perfChartData} options={perfChartOptions} />
            </div>
            <div>
              <Doughnut data={distributionData} options={distributionOptions} />
            </div>
          </div>
        )}

        {/* Application Details Table */}
        {detailed && applications.length > 0 && (
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Application Details</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Application
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Activity Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Error Count
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Error Rate
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg Response Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Modules
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {applications.map((app, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {app.app_name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {app.activity_count.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                        {app.error_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          app.error_rate > 10 
                            ? 'bg-red-100 text-red-800'
                            : app.error_rate > 5
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {app.error_rate}%
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {app.avg_response_time.toFixed(0)}ms
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <span className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">
                          {app.module_count} modules
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationChart;
