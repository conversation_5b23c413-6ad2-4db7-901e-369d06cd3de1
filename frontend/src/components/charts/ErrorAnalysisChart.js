import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const ErrorAnalysisChart = ({ data, detailed = false }) => {
  if (!data || !data.error_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Error Analysis</h3>
        <p className="text-gray-500">No error data available</p>
      </div>
    );
  }

  const errorAnalysis = data.error_analysis;

  // Error patterns chart data
  const errorPatternsData = {
    labels: errorAnalysis.error_patterns?.slice(0, 10).map(p => p.pattern) || [],
    datasets: [
      {
        label: 'Error Count',
        data: errorAnalysis.error_patterns?.slice(0, 10).map(p => p.count) || [],
        backgroundColor: [
          '#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e',
          '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899', '#f43f5e'
        ],
        borderColor: [
          '#dc2626', '#ea580c', '#ca8a04', '#65a30d', '#16a34a',
          '#0891b2', '#2563eb', '#7c3aed', '#db2777', '#e11d48'
        ],
        borderWidth: 1,
      },
    ],
  };

  const errorPatternsOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Top Error Patterns',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const pattern = errorAnalysis.error_patterns[context.dataIndex];
            return `${pattern.pattern}: ${pattern.count} occurrences (${pattern.percentage}%)`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Error timeline data (if available)
  const timelineData = errorAnalysis.error_timeline || [];
  const hourlyErrors = {};
  
  timelineData.forEach(error => {
    if (error.timestamp) {
      const hour = new Date(error.timestamp).getHours();
      hourlyErrors[hour] = (hourlyErrors[hour] || 0) + 1;
    }
  });

  const timelineChartData = {
    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
    datasets: [
      {
        label: 'Errors per Hour',
        data: Array.from({length: 24}, (_, i) => hourlyErrors[i] || 0),
        backgroundColor: 'rgba(239, 68, 68, 0.2)',
        borderColor: 'rgba(239, 68, 68, 1)',
        borderWidth: 2,
        fill: true,
      },
    ],
  };

  const timelineOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Error Distribution by Hour',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Error Analysis</h3>
        <div className="text-sm text-gray-500">
          {errorAnalysis.total_errors} total errors
        </div>
      </div>

      {errorAnalysis.total_errors === 0 ? (
        <div className="text-center py-8">
          <div className="text-green-500 text-4xl mb-2">✅</div>
          <p className="text-gray-600">No errors found in the logs!</p>
          <p className="text-sm text-gray-500 mt-1">Your system appears to be running smoothly.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Error Patterns Chart */}
          {errorAnalysis.error_patterns && errorAnalysis.error_patterns.length > 0 && (
            <div>
              <Bar data={errorPatternsData} options={errorPatternsOptions} />
            </div>
          )}

          {/* Error Timeline */}
          {detailed && timelineData.length > 0 && (
            <div>
              <Bar data={timelineChartData} options={timelineOptions} />
            </div>
          )}

          {/* Top Error Messages */}
          {detailed && errorAnalysis.top_error_messages && errorAnalysis.top_error_messages.length > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Most Common Error Messages</h4>
              <div className="space-y-2">
                {errorAnalysis.top_error_messages.slice(0, 5).map((error, index) => (
                  <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-red-800 font-mono flex-1 mr-4">
                        {error.message}
                      </p>
                      <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                        {error.count}x
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Errors Timeline */}
          {detailed && errorAnalysis.error_timeline && errorAnalysis.error_timeline.length > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Recent Errors</h4>
              <div className="max-h-64 overflow-y-auto space-y-2">
                {errorAnalysis.error_timeline.slice(-10).map((error, index) => (
                  <div key={index} className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="text-sm text-gray-800 font-mono">
                          {error.message}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(error.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ErrorAnalysisChart;
