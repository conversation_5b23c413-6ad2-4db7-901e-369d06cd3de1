import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const TimelineChart = ({ data, detailed = false }) => {
  if (!data || !data.time_analysis) {
    return (
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline Analysis</h3>
        <p className="text-gray-500">No timeline data available</p>
      </div>
    );
  }

  const timeAnalysis = data.time_analysis;

  // Hourly distribution chart
  const hourlyData = timeAnalysis.hourly_distribution || {};
  const hourlyChartData = {
    labels: Array.from({length: 24}, (_, i) => `${i}:00`),
    datasets: [
      {
        label: 'Log Entries',
        data: Array.from({length: 24}, (_, i) => hourlyData[i] || 0),
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
      },
    ],
  };

  const hourlyOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Activity by Hour of Day',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.parsed.y} entries at ${context.label}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Daily distribution chart (if detailed view)
  const dailyData = timeAnalysis.daily_distribution || {};
  const dailyDates = Object.keys(dailyData).sort();
  
  const dailyChartData = {
    labels: dailyDates.map(date => {
      const d = new Date(date);
      return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }),
    datasets: [
      {
        label: 'Daily Entries',
        data: dailyDates.map(date => dailyData[date]),
        backgroundColor: 'rgba(16, 185, 129, 0.6)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
    ],
  };

  const dailyOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Daily Activity Distribution',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      },
    },
  };

  // Peak activity insights
  const peakHour = timeAnalysis.peak_hour;
  const avgEntriesPerHour = timeAnalysis.avg_entries_per_hour || 0;
  const totalDays = timeAnalysis.total_days || 1;

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Timeline Analysis</h3>
        <div className="text-sm text-gray-500">
          {totalDays} day{totalDays !== 1 ? 's' : ''} of data
        </div>
      </div>

      {/* Key Insights */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4">
          <div className="text-blue-600 text-sm font-medium">Peak Hour</div>
          <div className="text-blue-900 text-lg font-bold">
            {peakHour !== null ? `${peakHour}:00` : 'N/A'}
          </div>
          <div className="text-blue-600 text-xs">
            Highest activity period
          </div>
        </div>
        
        <div className="bg-green-50 rounded-lg p-4">
          <div className="text-green-600 text-sm font-medium">Avg/Hour</div>
          <div className="text-green-900 text-lg font-bold">
            {Math.round(avgEntriesPerHour).toLocaleString()}
          </div>
          <div className="text-green-600 text-xs">
            Average entries per hour
          </div>
        </div>
        
        <div className="bg-purple-50 rounded-lg p-4">
          <div className="text-purple-600 text-sm font-medium">Total Days</div>
          <div className="text-purple-900 text-lg font-bold">
            {totalDays}
          </div>
          <div className="text-purple-600 text-xs">
            Days in log data
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="space-y-6">
        {/* Hourly Distribution */}
        <div>
          <Line data={hourlyChartData} options={hourlyOptions} />
        </div>

        {/* Daily Distribution (detailed view only) */}
        {detailed && dailyDates.length > 1 && (
          <div>
            <Bar data={dailyChartData} options={dailyOptions} />
          </div>
        )}

        {/* Activity Patterns Insights */}
        {detailed && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">Activity Patterns</h4>
            <div className="space-y-2 text-sm text-gray-600">
              {peakHour !== null && (
                <p>
                  • Peak activity occurs at <strong>{peakHour}:00</strong> with{' '}
                  <strong>{hourlyData[peakHour]?.toLocaleString()}</strong> entries
                </p>
              )}
              
              {avgEntriesPerHour > 0 && (
                <p>
                  • Average of <strong>{Math.round(avgEntriesPerHour).toLocaleString()}</strong>{' '}
                  entries per hour across all days
                </p>
              )}
              
              {/* Identify quiet periods */}
              {Object.keys(hourlyData).length > 0 && (
                <p>
                  • Quietest period: <strong>
                    {Object.entries(hourlyData)
                      .sort(([,a], [,b]) => a - b)[0]?.[0]}:00
                  </strong> with{' '}
                  <strong>
                    {Object.entries(hourlyData)
                      .sort(([,a], [,b]) => a - b)[0]?.[1]?.toLocaleString() || 0}
                  </strong> entries
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TimelineChart;
