import React, { createContext, useContext, useState } from 'react';
import axios from 'axios';

const AnalysisContext = createContext();

export const useAnalysis = () => {
  const context = useContext(AnalysisContext);
  if (!context) {
    throw new Error('useAnalysis must be used within an AnalysisProvider');
  }
  return context;
};

export const AnalysisProvider = ({ children }) => {
  const [analyses, setAnalyses] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const uploadFile = async (file, maxLines = null) => {
    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      if (maxLines) {
        formData.append('max_lines', maxLines);
      }

      const response = await axios.post(`${API_BASE_URL}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const analysisData = response.data;
      setAnalyses(prev => ({
        ...prev,
        [analysisData.analysis_id]: analysisData
      }));

      return analysisData;
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to upload file';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getAnalysis = async (analysisId) => {
    if (analyses[analysisId]) {
      return analyses[analysisId];
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}`);
      const analysisData = response.data;
      
      setAnalyses(prev => ({
        ...prev,
        [analysisId]: analysisData
      }));

      return analysisData;
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to fetch analysis';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getAnalysisSummary = async (analysisId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/summary`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch summary');
    }
  };

  const getErrorAnalysis = async (analysisId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/errors`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch error analysis');
    }
  };

  const getPatternAnalysis = async (analysisId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/patterns`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch pattern analysis');
    }
  };

  const getTimeAnalysis = async (analysisId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/timeline`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch time analysis');
    }
  };

  const getAnomalyAnalysis = async (analysisId) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/anomalies`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch anomaly analysis');
    }
  };

  const searchLogs = async (analysisId, filters = {}) => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          params.append(key, value);
        }
      });

      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/search?${params}`);
      return response.data;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to search logs');
    }
  };

  const exportAnalysis = async (analysisId, format = 'json') => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analysis/${analysisId}/export`, {
        params: { format },
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `analysis_${analysisId}.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to export analysis');
    }
  };

  const listAnalyses = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/analyses`);
      return response.data.analyses;
    } catch (err) {
      throw new Error(err.response?.data?.error || 'Failed to fetch analyses');
    }
  };

  const value = {
    analyses,
    loading,
    error,
    uploadFile,
    getAnalysis,
    getAnalysisSummary,
    getErrorAnalysis,
    getPatternAnalysis,
    getTimeAnalysis,
    getAnomalyAnalysis,
    searchLogs,
    exportAnalysis,
    listAnalyses,
    clearError: () => setError(null)
  };

  return (
    <AnalysisContext.Provider value={value}>
      {children}
    </AnalysisContext.Provider>
  );
};
