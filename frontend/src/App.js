import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import FileUpload from './components/FileUpload';
import Dashboard from './components/Dashboard';
import AnalysisView from './components/AnalysisView';
import { AnalysisProvider } from './context/AnalysisContext';

function App() {
  const [currentAnalysis, setCurrentAnalysis] = useState(null);

  return (
    <AnalysisProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <main className="container mx-auto px-4 py-8">
            <Routes>
              <Route 
                path="/" 
                element={
                  currentAnalysis ? (
                    <Dashboard analysisId={currentAnalysis} />
                  ) : (
                    <FileUpload onAnalysisComplete={setCurrentAnalysis} />
                  )
                } 
              />
              <Route 
                path="/analysis/:analysisId" 
                element={<AnalysisView />} 
              />
              <Route 
                path="/upload" 
                element={<FileUpload onAnalysisComplete={setCurrentAnalysis} />} 
              />
            </Routes>
          </main>
        </div>
      </Router>
    </AnalysisProvider>
  );
}

export default App;
