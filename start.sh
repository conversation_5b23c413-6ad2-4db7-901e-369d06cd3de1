#!/bin/bash

# Log Analyzer Application Startup Script
# This script starts both the backend Flask API and frontend React application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5000
FRONTEND_PORT=3000
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend-node"
FRONTEND_DIR="$PROJECT_DIR/frontend"
LOG_DIR="$PROJECT_DIR/logs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
is_port_available() {
    ! lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within $max_attempts seconds"
    return 1
}

# Function to setup Node.js backend environment
setup_backend_env() {
    print_status "Setting up Node.js backend environment..."

    cd "$BACKEND_DIR"

    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        npm install
    else
        print_status "Backend dependencies already installed"
    fi

    print_success "Backend environment ready"
    cd "$PROJECT_DIR"
}

# Function to setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."
    
    cd "$FRONTEND_DIR"
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
    else
        print_status "Node.js dependencies already installed"
    fi
    
    print_success "Node.js environment ready"
    cd "$PROJECT_DIR"
}

# Function to start backend
start_backend() {
    print_status "Starting backend Node.js application..."

    if ! is_port_available $BACKEND_PORT; then
        print_warning "Port $BACKEND_PORT is already in use. Attempting to kill existing process..."
        pkill -f "node.*app.js" || true
        sleep 2
    fi

    cd "$BACKEND_DIR"

    # Set environment variables
    export NODE_ENV=development
    export PORT=$BACKEND_PORT

    # Create logs directory if it doesn't exist
    mkdir -p "$LOG_DIR/backend"

    # Start Node.js application in background
    nohup node app.js > "$LOG_DIR/backend/node.log" 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > "$LOG_DIR/backend/node.pid"

    print_success "Backend started with PID $BACKEND_PID"

    # Wait for backend to be ready
    if wait_for_service "http://localhost:$BACKEND_PORT/api/health" "Backend API"; then
        print_success "Backend API is running at http://localhost:$BACKEND_PORT"
    else
        print_error "Failed to start backend"
        return 1
    fi

    cd "$PROJECT_DIR"
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend React application..."
    
    if ! is_port_available $FRONTEND_PORT; then
        print_warning "Port $FRONTEND_PORT is already in use. Attempting to kill existing process..."
        pkill -f "node.*react-scripts.*start" || true
        sleep 2
    fi
    
    cd "$FRONTEND_DIR"
    
    # Create logs directory if it doesn't exist
    mkdir -p "$LOG_DIR/frontend"
    
    # Set environment variables
    export REACT_APP_API_URL=http://localhost:$BACKEND_PORT
    export BROWSER=none  # Don't auto-open browser
    
    # Start React application in background
    nohup npm start > "$LOG_DIR/frontend/react.log" 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$LOG_DIR/frontend/react.pid"
    
    print_success "Frontend started with PID $FRONTEND_PID"
    
    # Wait for frontend to be ready
    if wait_for_service "http://localhost:$FRONTEND_PORT" "Frontend Application"; then
        print_success "Frontend application is running at http://localhost:$FRONTEND_PORT"
    else
        print_error "Failed to start frontend"
        return 1
    fi
    
    cd "$PROJECT_DIR"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 14 or higher."
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check curl (for health checks)
    if ! command_exists curl; then
        print_warning "curl is not installed. Health checks may not work properly."
    fi
    
    print_success "All prerequisites are available"
}

# Function to display startup information
show_startup_info() {
    echo ""
    echo "=============================================="
    echo "🚀 Log Analyzer Application Started!"
    echo "=============================================="
    echo ""
    echo "📊 Frontend (React):  http://localhost:$FRONTEND_PORT"
    echo "🔧 Backend API:       http://localhost:$BACKEND_PORT"
    echo "📋 API Documentation: http://localhost:$BACKEND_PORT/api/docs"
    echo "❤️  Health Check:     http://localhost:$BACKEND_PORT/api/health"
    echo ""
    echo "📁 Log files:"
    echo "   Backend:  $LOG_DIR/backend/flask.log"
    echo "   Frontend: $LOG_DIR/frontend/react.log"
    echo ""
    echo "🛑 To stop the application, run: ./stop.sh"
    echo "=============================================="
    echo ""
}

# Function to cleanup on exit
cleanup() {
    print_status "Cleaning up..."
    
    if [ -f "$LOG_DIR/backend/node.pid" ]; then
        BACKEND_PID=$(cat "$LOG_DIR/backend/node.pid")
        kill $BACKEND_PID 2>/dev/null || true
        rm -f "$LOG_DIR/backend/node.pid"
    fi
    
    if [ -f "$LOG_DIR/frontend/react.pid" ]; then
        FRONTEND_PID=$(cat "$LOG_DIR/frontend/react.pid")
        kill $FRONTEND_PID 2>/dev/null || true
        rm -f "$LOG_DIR/frontend/react.pid"
    fi
}

# Main execution
main() {
    echo "🔍 Log Analyzer - Starting Application..."
    echo ""
    
    # Set up cleanup trap
    trap cleanup EXIT
    
    # Check prerequisites
    check_prerequisites
    
    # Setup environments
    setup_backend_env
    setup_node_env
    
    # Start services
    start_backend
    start_frontend
    
    # Show startup information
    show_startup_info
    
    # Keep script running
    print_status "Application is running. Press Ctrl+C to stop."
    
    # Wait for user interrupt
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
