# Log Analyzer User Guide

## Getting Started

### What is Log Analyzer?
Log Analyzer is an intelligent web-based application that automatically analyzes log files and provides insights without requiring manual configuration. It can handle various log formats and presents results through an intuitive, non-technical interface.

### Key Features
- **Zero-configuration setup**: Works out of the box with any log format
- **Intelligent parsing**: Automatically detects JSON, plain text, and structured logs
- **Visual analytics**: Charts and graphs showing patterns and trends
- **Error detection**: Highlights errors, warnings, and anomalies
- **Easy search**: Find specific log entries without technical knowledge
- **Export capabilities**: Download results as JSON or CSV

## Using the Application

### 1. Uploading Log Files

1. **Access the Application**: Open your web browser and navigate to the Log Analyzer URL
2. **Upload Interface**: You'll see a drag-and-drop area on the main page
3. **Select Files**: Either drag your log file onto the area or click to browse and select
4. **Supported Formats**: .log, .txt, .json files up to 100MB
5. **Advanced Options**: Click "Advanced Options" to limit the number of lines processed
6. **Start Analysis**: Click "Analyze Log File" to begin processing

### 2. Understanding the Dashboard

After uploading, you'll see the analysis dashboard with several sections:

#### Summary Cards
- **Total Entries**: Number of log entries processed
- **Error Rate**: Percentage of entries that are errors
- **Warning Rate**: Percentage of entries that are warnings  
- **System Health**: Overall health status (Healthy, Caution, Warning, Critical)

#### Key Insights Panel
- **Insights**: Important findings about your log data
- **Recommendations**: Actionable suggestions for improvement

#### Analysis Tabs
- **Overview**: High-level charts and summaries
- **Error Analysis**: Detailed error patterns and trends
- **Patterns**: Log level distribution and frequent terms
- **Timeline**: Activity patterns over time
- **Search Logs**: Find specific entries

### 3. Error Analysis

The Error Analysis section helps you understand:
- **Error Patterns**: Common types of errors
- **Error Timeline**: When errors occurred
- **Top Error Messages**: Most frequent error messages
- **Error Distribution**: Errors by hour of day

**What to Look For:**
- Spikes in error activity
- Recurring error patterns
- Time-based error trends
- Specific error messages that need attention

### 4. Pattern Analysis

This section shows:
- **Log Level Distribution**: Breakdown of INFO, WARN, ERROR, etc.
- **Frequent Terms**: Most common words/phrases in logs
- **IP Address Patterns**: Top IP addresses (for access logs)
- **HTTP Status Codes**: Response code distribution (for web logs)

**Useful Insights:**
- Unusual log level distributions
- Unexpected frequent terms
- Suspicious IP activity
- High error response rates

### 5. Timeline Analysis

Understand activity patterns:
- **Hourly Distribution**: Activity by hour of day
- **Daily Distribution**: Activity across multiple days
- **Peak Hours**: When your system is busiest
- **Quiet Periods**: Low activity times

**Applications:**
- Capacity planning
- Maintenance scheduling
- Performance optimization
- Anomaly detection

### 6. Searching Logs

Use the search functionality to:
- **Text Search**: Find entries containing specific words
- **Level Filter**: Show only errors, warnings, etc.
- **Date Range**: Filter by time period
- **Pagination**: Browse through large result sets

**Search Tips:**
- Use specific error messages to find related issues
- Filter by ERROR level to focus on problems
- Use date ranges to investigate specific incidents
- Combine filters for precise results

### 7. Exporting Results

You can export your analysis in two formats:
- **JSON**: Complete analysis data for further processing
- **CSV**: Log entries in spreadsheet format

**Export Uses:**
- Share results with team members
- Import into other analysis tools
- Create reports for management
- Archive analysis results

## Understanding Log Formats

### Supported Formats

#### JSON Logs
```json
{"timestamp": "2025-01-12T08:00:01.123Z", "level": "INFO", "message": "User login successful"}
```

#### Plain Text Logs
```
2025-01-12 08:00:01,123 - INFO - User login successful
```

#### Apache/Nginx Access Logs
```
************* - - [12/Jan/2025:08:00:15 +0000] "GET /api/health HTTP/1.1" 200 45
```

#### Syslog Format
```
Jan 12 08:00:01 server01 application: User login successful
```

### Automatic Detection
The application automatically:
- Detects the log format
- Extracts timestamps, log levels, and messages
- Identifies structured fields
- Handles mixed formats within the same file

## Health Status Indicators

### Healthy ✅
- Error rate < 1%
- Warning rate < 10%
- System operating normally

### Caution ⚡
- Error rate 1-5%
- Warning rate 10-20%
- Minor issues detected

### Warning ⚠️
- Error rate 5-10%
- Warning rate > 20%
- Significant issues requiring attention

### Critical 🚨
- Error rate > 10%
- Severe issues requiring immediate action

## Best Practices

### For Better Analysis
1. **Include Timestamps**: Ensure logs have timestamps for timeline analysis
2. **Use Log Levels**: Implement proper INFO, WARN, ERROR levels
3. **Structured Logging**: Use JSON format when possible
4. **Consistent Format**: Maintain consistent log format across services

### For Performance
1. **File Size**: Keep files under 100MB for faster processing
2. **Line Limits**: Use the max_lines option for large files
3. **Regular Analysis**: Analyze logs regularly to catch issues early

### For Security
1. **Sensitive Data**: Ensure logs don't contain passwords or personal data
2. **Access Control**: Implement proper access controls in production
3. **Data Retention**: Consider data retention policies for log analysis

## Troubleshooting

### Common Issues

**File Upload Fails**
- Check file size (max 100MB)
- Verify file format (.log, .txt, .json)
- Ensure stable internet connection

**No Analysis Results**
- Verify file contains log data
- Check if file is empty or corrupted
- Try with a smaller sample file

**Slow Performance**
- Use max_lines option for large files
- Check available system resources
- Consider breaking large files into smaller chunks

**Missing Timestamps**
- Some analysis features require timestamps
- Consider adding timestamps to your logging system
- Manual timestamp extraction may be needed

### Getting Help
If you encounter issues:
1. Check the browser console for error messages
2. Verify the backend server is running
3. Review the API documentation
4. Contact your system administrator
