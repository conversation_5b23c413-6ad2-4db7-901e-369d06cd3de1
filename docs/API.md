# Log Analyzer API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
Currently, no authentication is required for API endpoints.

## Endpoints

### Health Check
Check if the API server is running.

**GET** `/health`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-12T10:30:00.000Z",
  "version": "1.0.0"
}
```

### Upload Log File
Upload and analyze a log file.

**POST** `/upload`

**Parameters:**
- `file` (required): Log file to upload
- `max_lines` (optional): Maximum number of lines to process

**Response:**
```json
{
  "analysis_id": "sample.log_20250112_103000",
  "filename": "sample.log",
  "status": "completed",
  "summary": {
    "total_entries": 1000,
    "error_count": 25,
    "warning_count": 50,
    "error_rate": 2.5,
    "warning_rate": 5.0,
    "health_status": "Caution"
  },
  "insights": ["Key insights about the log data..."],
  "recommendations": ["Actionable recommendations..."]
}
```

### Get Analysis Results
Retrieve complete analysis results by ID.

**GET** `/analysis/{analysis_id}`

**Response:**
```json
{
  "analysis_id": "sample.log_20250112_103000",
  "filename": "sample.log",
  "upload_time": "2025-01-12T10:30:00.000Z",
  "analysis_results": {
    "summary": {...},
    "error_analysis": {...},
    "pattern_analysis": {...},
    "time_analysis": {...},
    "anomaly_detection": {...},
    "insights": [...],
    "recommendations": [...]
  }
}
```

### Get Analysis Summary
Get a summary of analysis results.

**GET** `/analysis/{analysis_id}/summary`

### Get Error Analysis
Get detailed error analysis.

**GET** `/analysis/{analysis_id}/errors`

### Get Pattern Analysis
Get pattern analysis results.

**GET** `/analysis/{analysis_id}/patterns`

### Get Timeline Analysis
Get time-based analysis.

**GET** `/analysis/{analysis_id}/timeline`

### Get Anomaly Detection
Get anomaly detection results.

**GET** `/analysis/{analysis_id}/anomalies`

### Search Logs
Search through log entries with filters.

**GET** `/analysis/{analysis_id}/search`

**Query Parameters:**
- `q`: Search query string
- `level`: Log level filter (ERROR, WARN, INFO, DEBUG)
- `start_date`: Start date filter (ISO format)
- `end_date`: End date filter (ISO format)
- `page`: Page number (default: 1)
- `per_page`: Results per page (default: 50)

**Response:**
```json
{
  "analysis_id": "sample.log_20250112_103000",
  "entries": [...],
  "pagination": {
    "page": 1,
    "per_page": 50,
    "total": 1000,
    "pages": 20
  },
  "filters": {
    "query": "error",
    "level": "ERROR",
    "start_date": null,
    "end_date": null
  }
}
```

### Export Analysis
Export analysis results as JSON or CSV.

**GET** `/analysis/{analysis_id}/export`

**Query Parameters:**
- `format`: Export format (`json` or `csv`)

**Response:** File download

### List Analyses
Get a list of all cached analyses.

**GET** `/analyses`

**Response:**
```json
{
  "analyses": [
    {
      "analysis_id": "sample.log_20250112_103000",
      "filename": "sample.log",
      "upload_time": "2025-01-12T10:30:00.000Z",
      "total_entries": 1000,
      "health_status": "Caution"
    }
  ]
}
```

## Error Responses

All endpoints may return the following error responses:

**400 Bad Request**
```json
{
  "error": "Invalid request parameters"
}
```

**404 Not Found**
```json
{
  "error": "Analysis not found"
}
```

**413 Payload Too Large**
```json
{
  "error": "File too large. Maximum size is 100MB."
}
```

**500 Internal Server Error**
```json
{
  "error": "Internal server error"
}
```

## Rate Limits
Currently, no rate limits are enforced.

## File Size Limits
- Maximum file size: 100MB
- Supported formats: .log, .txt, .json

## Data Retention
Analysis results are stored in memory and will be lost when the server restarts. In a production environment, consider implementing persistent storage.
