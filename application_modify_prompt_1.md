Please modify the log analyzer application to handle the specific log format found in the existing `logs` folder. The logs follow this structured format:

**Log Format Pattern:**
```
[timestamp][level][module][sessionId][platform][customerId][clientIp][requestId][auditType][appName][responseTime][status][method][originUrl][extraData] : rawData
```

**Requirements:**
1. **Update the log parser** to recognize and extract each bracketed field as a separate structured component
2. **Enhance the format detector** to identify this specific pattern as a distinct log format type
3. **Modify the analysis engine** to provide insights specific to this format, including:
   - Response time analysis and performance metrics
   - Customer and session-based analytics
   - Platform and application breakdown
   - Request method and URL pattern analysis
   - Audit type categorization

4. **Update the frontend visualizations** to display:
   - Response time trends and distribution charts
   - Customer activity patterns
   - Platform usage statistics
   - Application performance comparisons
   - Geographic analysis based on client IP (if applicable)

5. **Ensure the system can handle multiple applications** with different variations of this log pattern, making the parser flexible enough to adapt to slight format differences between applications

6. **Test the implementation** using the existing log files in the `/logs/activities/` directory to ensure proper parsing and analysis

Please continue with the implementation, focusing on making the log parser robust enough to handle enterprise-level structured logs while maintaining the zero-configuration approach for end users.