#!/bin/bash

# Backend Flask Application Start Script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5000
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend"
VENV_DIR="$PROJECT_DIR/venv"
LOG_DIR="$PROJECT_DIR/logs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
is_port_available() {
    ! lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for backend to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "Backend is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_error "Backend failed to start within $max_attempts seconds"
    return 1
}

# Function to setup Python virtual environment
setup_python_env() {
    print_status "Setting up Python virtual environment..."
    
    if [ ! -d "$VENV_DIR" ]; then
        print_status "Creating virtual environment..."
        python3 -m venv "$VENV_DIR"
    fi
    
    print_status "Activating virtual environment..."
    source "$VENV_DIR/bin/activate"
    
    print_status "Installing Python dependencies..."
    pip install -q --upgrade pip
    pip install -q -r "$BACKEND_DIR/requirements.txt"
    
    print_success "Python environment ready"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Python
    if ! command_exists python3; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check if backend directory exists
    if [ ! -d "$BACKEND_DIR" ]; then
        print_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    # Check if requirements.txt exists
    if [ ! -f "$BACKEND_DIR/requirements.txt" ]; then
        print_error "requirements.txt not found in backend directory"
        exit 1
    fi
    
    # Check if app.py exists
    if [ ! -f "$BACKEND_DIR/app.py" ]; then
        print_error "app.py not found in backend directory"
        exit 1
    fi
    
    print_success "All prerequisites are available"
}

# Function to start backend
start_backend() {
    print_status "Starting backend Flask application..."
    
    if ! is_port_available $BACKEND_PORT; then
        print_warning "Port $BACKEND_PORT is already in use."
        read -p "Do you want to kill the existing process? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pkill -f "flask.*run.*port.*$BACKEND_PORT" || true
            pkill -f "python.*app.py" || true
            sleep 2
        else
            print_error "Cannot start backend on port $BACKEND_PORT"
            exit 1
        fi
    fi
    
    cd "$BACKEND_DIR"
    source "$VENV_DIR/bin/activate"
    
    # Set environment variables
    export FLASK_APP=app.py
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    
    # Create logs directory if it doesn't exist
    mkdir -p "$LOG_DIR/backend"
    
    print_status "Starting Flask application on port $BACKEND_PORT..."
    
    # Check if we should run in development or production mode
    if [ "${1:-}" = "--dev" ] || [ "${1:-}" = "-d" ]; then
        print_status "Running in development mode with auto-reload..."
        python3 app.py
    elif [ "${1:-}" = "--prod" ] || [ "${1:-}" = "-p" ]; then
        print_status "Running in production mode..."
        # Install gunicorn if not present
        pip install -q gunicorn
        gunicorn --bind 0.0.0.0:$BACKEND_PORT --workers 4 app:app
    else
        print_status "Running in background mode..."
        nohup python3 app.py > "$LOG_DIR/backend/flask.log" 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > "$LOG_DIR/backend/flask.pid"
        
        print_success "Backend started with PID $BACKEND_PID"
        
        # Wait for backend to be ready
        if wait_for_service "http://localhost:$BACKEND_PORT/api/health"; then
            print_success "Backend API is running at http://localhost:$BACKEND_PORT"
            print_status "Log file: $LOG_DIR/backend/flask.log"
            print_status "PID file: $LOG_DIR/backend/flask.pid"
        else
            print_error "Failed to start backend"
            return 1
        fi
    fi
    
    cd "$PROJECT_DIR"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Start the Log Analyzer backend Flask application"
    echo ""
    echo "Options:"
    echo "  -d, --dev      Run in development mode (foreground with auto-reload)"
    echo "  -p, --prod     Run in production mode with gunicorn"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Default: Run in background mode"
    echo ""
    echo "Examples:"
    echo "  $0              # Start in background"
    echo "  $0 --dev        # Start in development mode"
    echo "  $0 --prod       # Start in production mode"
    echo ""
}

# Function to show startup information
show_startup_info() {
    echo ""
    echo "=============================================="
    echo "🔧 Backend Flask Application Started!"
    echo "=============================================="
    echo ""
    echo "🔧 Backend API:       http://localhost:$BACKEND_PORT"
    echo "📋 API Documentation: http://localhost:$BACKEND_PORT/api/docs"
    echo "❤️  Health Check:     http://localhost:$BACKEND_PORT/api/health"
    echo ""
    echo "📁 Log file: $LOG_DIR/backend/flask.log"
    echo "📄 PID file: $LOG_DIR/backend/flask.pid"
    echo ""
    echo "🛑 To stop the backend, run: ./stop.sh or kill the process"
    echo "=============================================="
    echo ""
}

# Main execution
main() {
    echo "🔧 Log Analyzer Backend - Starting Flask Application..."
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Setup Python environment
    setup_python_env
    
    # Start backend
    start_backend "$1"
    
    # Show startup information (only for background mode)
    if [ "${1:-}" != "--dev" ] && [ "${1:-}" != "-d" ] && [ "${1:-}" != "--prod" ] && [ "${1:-}" != "-p" ]; then
        show_startup_info
    fi
}

# Handle command line arguments
case "${1:-}" in
    --dev|-d)
        main "$1"
        ;;
    --prod|-p)
        main "$1"
        ;;
    --help|-h)
        show_usage
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
