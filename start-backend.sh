#!/bin/bash

# Backend Flask Application Start Script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5000
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend-node"
LOG_DIR="$PROJECT_DIR/logs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
is_port_available() {
    ! lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for backend to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "Backend is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_error "Backend failed to start within $max_attempts seconds"
    return 1
}

# Function to setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."

    cd "$BACKEND_DIR"

    if [ ! -d "node_modules" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
    else
        print_status "Node.js dependencies already installed"
    fi

    print_success "Node.js environment ready"
    cd "$PROJECT_DIR"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi

    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check if backend directory exists
    if [ ! -d "$BACKEND_DIR" ]; then
        print_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    # Check if package.json exists
    if [ ! -f "$BACKEND_DIR/package.json" ]; then
        print_error "package.json not found in backend directory"
        exit 1
    fi

    # Check if app.js exists
    if [ ! -f "$BACKEND_DIR/app.js" ]; then
        print_error "app.js not found in backend directory"
        exit 1
    fi
    
    print_success "All prerequisites are available"
}

# Function to start backend
start_backend() {
    print_status "Starting backend Node.js application..."

    if ! is_port_available $BACKEND_PORT; then
        print_warning "Port $BACKEND_PORT is already in use."
        read -p "Do you want to kill the existing process? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pkill -f "node.*app.js" || true
            pkill -f "nodemon.*app.js" || true
            sleep 2
        else
            print_error "Cannot start backend on port $BACKEND_PORT"
            exit 1
        fi
    fi

    cd "$BACKEND_DIR"

    # Set environment variables
    export NODE_ENV=development
    export PORT=$BACKEND_PORT
    
    # Create logs directory if it doesn't exist
    mkdir -p "$LOG_DIR/backend"

    print_status "Starting Node.js application on port $BACKEND_PORT..."

    # Check if we should run in development or production mode
    if [ "${1:-}" = "--dev" ] || [ "${1:-}" = "-d" ]; then
        print_status "Running in development mode with auto-reload..."
        if command_exists nodemon; then
            nodemon app.js
        else
            print_status "Installing nodemon for development..."
            npm install -g nodemon
            nodemon app.js
        fi
    elif [ "${1:-}" = "--prod" ] || [ "${1:-}" = "-p" ]; then
        print_status "Running in production mode..."
        export NODE_ENV=production
        node app.js
    else
        print_status "Running in background mode..."
        nohup node app.js > "$LOG_DIR/backend/node.log" 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > "$LOG_DIR/backend/node.pid"

        print_success "Backend started with PID $BACKEND_PID"

        # Wait for backend to be ready
        if wait_for_service "http://localhost:$BACKEND_PORT/api/health"; then
            print_success "Backend API is running at http://localhost:$BACKEND_PORT"
            print_status "Log file: $LOG_DIR/backend/node.log"
            print_status "PID file: $LOG_DIR/backend/node.pid"
        else
            print_error "Failed to start backend"
            return 1
        fi
    fi
    
    cd "$PROJECT_DIR"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Start the Log Analyzer backend Node.js application"
    echo ""
    echo "Options:"
    echo "  -d, --dev      Run in development mode (foreground with auto-reload)"
    echo "  -p, --prod     Run in production mode"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Default: Run in background mode"
    echo ""
    echo "Examples:"
    echo "  $0              # Start in background"
    echo "  $0 --dev        # Start in development mode"
    echo "  $0 --prod       # Start in production mode"
    echo ""
}

# Function to show startup information
show_startup_info() {
    echo ""
    echo "=============================================="
    echo "🔧 Backend Node.js Application Started!"
    echo "=============================================="
    echo ""
    echo "🔧 Backend API:       http://localhost:$BACKEND_PORT"
    echo "📋 API Documentation: http://localhost:$BACKEND_PORT/api/docs"
    echo "❤️  Health Check:     http://localhost:$BACKEND_PORT/api/health"
    echo ""
    echo "📁 Log file: $LOG_DIR/backend/node.log"
    echo "📄 PID file: $LOG_DIR/backend/node.pid"
    echo ""
    echo "🛑 To stop the backend, run: ./stop.sh or kill the process"
    echo "=============================================="
    echo ""
}

# Main execution
main() {
    echo "🔧 Log Analyzer Backend - Starting Node.js Application..."
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Setup Node.js environment
    setup_node_env
    
    # Start backend
    start_backend "$1"
    
    # Show startup information (only for background mode)
    if [ "${1:-}" != "--dev" ] && [ "${1:-}" != "-d" ] && [ "${1:-}" != "--prod" ] && [ "${1:-}" != "-p" ]; then
        show_startup_info
    fi
}

# Handle command line arguments
case "${1:-}" in
    --dev|-d)
        main "$1"
        ;;
    --prod|-p)
        main "$1"
        ;;
    --help|-h)
        show_usage
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
