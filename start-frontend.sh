#!/bin/bash

# Frontend React Application Start Script

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3000
BACKEND_PORT=5000
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$PROJECT_DIR/frontend-nextjs"
LOG_DIR="$PROJECT_DIR/logs"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
is_port_available() {
    ! lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local max_attempts=60  # React takes longer to start
    local attempt=1
    
    print_status "Waiting for frontend to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_success "Frontend is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "Frontend failed to start within $((max_attempts * 2)) seconds"
    return 1
}

# Function to setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."
    
    cd "$FRONTEND_DIR"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found in frontend directory"
        exit 1
    fi
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
    else
        print_status "Checking for dependency updates..."
        npm audit fix --silent || true
    fi
    
    print_success "Node.js environment ready"
    cd "$PROJECT_DIR"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 14 or higher."
        exit 1
    fi
    
    # Check npm
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 14 ]; then
        print_error "Node.js version 14 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check if frontend directory exists
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
    
    print_success "All prerequisites are available"
}

# Function to check backend availability
check_backend() {
    print_status "Checking if backend is available..."
    
    if curl -s "http://localhost:$BACKEND_PORT/api/health" >/dev/null 2>&1; then
        print_success "Backend is running and accessible"
    else
        print_warning "Backend is not running on port $BACKEND_PORT"
        print_warning "Frontend will still start but API calls will fail"
        print_status "To start the backend, run: ./start-backend.sh"
    fi
}

# Function to start frontend
start_frontend() {
    print_status "Starting frontend React application..."
    
    if ! is_port_available $FRONTEND_PORT; then
        print_warning "Port $FRONTEND_PORT is already in use."
        read -p "Do you want to kill the existing process? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            pkill -f "node.*react-scripts.*start" || true
            pkill -f "npm.*start" || true
            sleep 2
        else
            print_error "Cannot start frontend on port $FRONTEND_PORT"
            exit 1
        fi
    fi
    
    cd "$FRONTEND_DIR"
    
    # Create logs directory if it doesn't exist
    mkdir -p "$LOG_DIR/frontend"
    
    # Set environment variables
    export REACT_APP_API_URL=http://localhost:$BACKEND_PORT
    export PORT=$FRONTEND_PORT
    
    print_status "Starting React application on port $FRONTEND_PORT..."
    
    # Check if we should run in development or production mode
    if [ "${1:-}" = "--dev" ] || [ "${1:-}" = "-d" ]; then
        print_status "Running in development mode..."
        export BROWSER=none  # Don't auto-open browser
        npm start
    elif [ "${1:-}" = "--build" ] || [ "${1:-}" = "-b" ]; then
        print_status "Building production version..."
        npm run build
        print_success "Production build completed in build/ directory"
        return 0
    elif [ "${1:-}" = "--serve" ] || [ "${1:-}" = "-s" ]; then
        print_status "Building and serving production version..."
        npm run build
        
        # Install serve if not present
        if ! command_exists serve; then
            print_status "Installing serve globally..."
            npm install -g serve
        fi
        
        print_status "Serving production build on port $FRONTEND_PORT..."
        serve -s build -l $FRONTEND_PORT
    else
        print_status "Running in background mode..."
        export BROWSER=none  # Don't auto-open browser
        
        nohup npm start > "$LOG_DIR/frontend/react.log" 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > "$LOG_DIR/frontend/react.pid"
        
        print_success "Frontend started with PID $FRONTEND_PID"
        
        # Wait for frontend to be ready
        if wait_for_service "http://localhost:$FRONTEND_PORT"; then
            print_success "Frontend application is running at http://localhost:$FRONTEND_PORT"
            print_status "Log file: $LOG_DIR/frontend/react.log"
            print_status "PID file: $LOG_DIR/frontend/react.pid"
        else
            print_error "Failed to start frontend"
            return 1
        fi
    fi
    
    cd "$PROJECT_DIR"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Start the Log Analyzer frontend React application"
    echo ""
    echo "Options:"
    echo "  -d, --dev      Run in development mode (foreground with hot reload)"
    echo "  -b, --build    Build production version only"
    echo "  -s, --serve    Build and serve production version"
    echo "  -h, --help     Show this help message"
    echo ""
    echo "Default: Run in background development mode"
    echo ""
    echo "Examples:"
    echo "  $0              # Start in background development mode"
    echo "  $0 --dev        # Start in foreground development mode"
    echo "  $0 --build      # Build production version"
    echo "  $0 --serve      # Build and serve production version"
    echo ""
}

# Function to show startup information
show_startup_info() {
    echo ""
    echo "=============================================="
    echo "📊 Frontend React Application Started!"
    echo "=============================================="
    echo ""
    echo "📊 Frontend:     http://localhost:$FRONTEND_PORT"
    echo "🔧 Backend API:  http://localhost:$BACKEND_PORT"
    echo ""
    echo "📁 Log file: $LOG_DIR/frontend/react.log"
    echo "📄 PID file: $LOG_DIR/frontend/react.pid"
    echo ""
    echo "🛑 To stop the frontend, run: ./stop.sh or kill the process"
    echo "=============================================="
    echo ""
}

# Main execution
main() {
    echo "📊 Log Analyzer Frontend - Starting React Application..."
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Setup Node.js environment
    setup_node_env
    
    # Check backend availability
    check_backend
    
    # Start frontend
    start_frontend "$1"
    
    # Show startup information (only for background mode)
    if [ "${1:-}" != "--dev" ] && [ "${1:-}" != "-d" ] && [ "${1:-}" != "--serve" ] && [ "${1:-}" != "-s" ]; then
        if [ "${1:-}" != "--build" ] && [ "${1:-}" != "-b" ]; then
            show_startup_info
        fi
    fi
}

# Handle command line arguments
case "${1:-}" in
    --dev|-d)
        main "$1"
        ;;
    --build|-b)
        main "$1"
        ;;
    --serve|-s)
        main "$1"
        ;;
    --help|-h)
        show_usage
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
