# Log Analyzer - Intelligent Log Analysis Application

A comprehensive web-based log analysis application that automatically parses, analyzes, and visualizes log data without requiring manual configuration.

## Features

### Core Functionality
- **Zero-configuration setup**: Automatically detects log formats and structure
- **Intelligent parsing**: Handles JSON, plain text, structured logs, and more
- **Visual analytics**: Charts, graphs, and dashboards for log patterns and trends
- **Error detection**: Automatically identifies errors, warnings, and anomalies
- **Search and filtering**: Easy-to-use search without technical knowledge
- **Summary reports**: High-level insights in plain language

### User Experience
- Simple drag-and-drop file upload interface
- No technical configuration required
- Clear, non-technical explanations
- Export capabilities for reports and visualizations
- Responsive design for desktop and mobile

### Technical Features
- Support for large log files
- Real-time log monitoring capabilities
- Web-based interface
- RESTful API
- Modern React frontend with Python Flask backend

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd log_analyzer
```

2. Install backend dependencies:
```bash
cd backend
pip install -r requirements.txt
```

3. Install frontend dependencies:
```bash
cd ../frontend
npm install
```

4. Start the backend server:
```bash
cd ../backend
python app.py
```

5. Start the frontend development server:
```bash
cd ../frontend
npm start
```

6. Open your browser and navigate to `http://localhost:3000`

## Project Structure

```
log_analyzer/
├── backend/                 # Python Flask backend
│   ├── app.py              # Main Flask application
│   ├── log_parser/         # Log parsing modules
│   ├── analysis/           # Log analysis engines
│   ├── api/                # REST API endpoints
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend
│   ├── src/                # React source code
│   ├── public/             # Static assets
│   └── package.json        # Node.js dependencies
├── sample_logs/            # Sample log files for testing
├── docs/                   # Documentation
└── README.md               # This file
```

## Usage

1. **Upload Log Files**: Drag and drop or select log files through the web interface
2. **Automatic Analysis**: The application automatically detects format and analyzes the logs
3. **View Results**: Browse insights, charts, and summaries in the dashboard
4. **Search and Filter**: Use the intuitive search interface to find specific entries
5. **Export Reports**: Download analysis results and visualizations

## Supported Log Formats

- JSON logs
- Plain text logs
- Apache/Nginx access logs
- Application logs with timestamps
- System logs
- Custom structured logs

## API Documentation

The REST API provides endpoints for:
- File upload and processing
- Log analysis results
- Search and filtering
- Report generation

See `/docs/api.md` for detailed API documentation.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
