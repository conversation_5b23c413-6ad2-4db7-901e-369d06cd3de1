#!/usr/bin/env python3
"""
Test script for enterprise structured log parsing
"""

import sys
import os
import json
sys.path.append('backend')

from log_parser.detector import LogFormatDetector
from log_parser.parser import <PERSON>g<PERSON>arser

def test_enterprise_log_parsing():
    """Test parsing of enterprise structured logs."""
    print("Testing Enterprise Structured Log Parsing")
    print("=" * 50)
    
    # Test with sample enterprise log
    log_file = "sample_logs/enterprise_structured.log"
    
    if not os.path.exists(log_file):
        print(f"Error: {log_file} not found")
        return
    
    # Initialize components
    detector = LogFormatDetector()
    parser = LogParser()
    
    # Test format detection
    print("\n1. Testing Format Detection:")
    with open(log_file, 'r') as f:
        sample_lines = [f.readline().strip() for _ in range(10)]
    
    format_info = detector.detect_format(log_file, sample_lines)
    print(f"Detected format: {format_info['format']}")
    print(f"Confidence: {format_info['confidence']:.2%}")
    
    if format_info['format'] == 'enterprise_structured':
        print("✅ Enterprise structured format detected correctly!")
        print(f"Field structure: {format_info.get('field_structure', {})}")
    else:
        print("❌ Failed to detect enterprise structured format")
        return
    
    # Test parsing
    print("\n2. Testing Log Parsing:")
    parsed_data = parser.parse_file(log_file, max_lines=50)
    
    print(f"Total lines: {parsed_data['total_lines']}")
    print(f"Parsed lines: {parsed_data['parsed_lines']}")
    print(f"Parse success rate: {parsed_data['stats']['parse_success_rate']:.2%}")
    
    # Show sample parsed entry
    if parsed_data['entries']:
        sample_entry = parsed_data['entries'][0]
        print(f"\nSample parsed entry:")
        print(f"  Timestamp: {sample_entry['timestamp']}")
        print(f"  Level: {sample_entry['level']}")
        print(f"  Module: {sample_entry['fields'].get('module', 'N/A')}")
        print(f"  App Name: {sample_entry['fields'].get('app_name', 'N/A')}")
        print(f"  Response Time: {sample_entry['fields'].get('response_time', 'N/A')}ms")
        print(f"  Customer ID: {sample_entry['fields'].get('customer_id', 'N/A')}")
        print(f"  Platform: {sample_entry['fields'].get('platform', 'N/A')}")
    
    # Test basic analysis of parsed data
    print("\n3. Testing Basic Analysis:")

    # Count log levels
    level_counts = {}
    app_counts = {}
    customer_counts = {}
    response_times = []

    for entry in parsed_data['entries']:
        level = entry.get('level', 'UNKNOWN')
        level_counts[level] = level_counts.get(level, 0) + 1

        fields = entry.get('fields', {})
        app_name = fields.get('app_name', 'unknown')
        app_counts[app_name] = app_counts.get(app_name, 0) + 1

        customer_id = fields.get('customer_id', '')
        if customer_id and customer_id != 'na':
            customer_counts[customer_id] = customer_counts.get(customer_id, 0) + 1

        response_time = fields.get('response_time', 0)
        if isinstance(response_time, (int, float)) and response_time > 0:
            response_times.append(response_time)

    print("Summary:")
    print(f"  Total entries: {len(parsed_data['entries'])}")
    print(f"  Log levels: {level_counts}")
    print(f"  Unique applications: {len(app_counts)}")
    print(f"  Unique customers: {len(customer_counts)}")

    if response_times:
        avg_response_time = sum(response_times) / len(response_times)
        slow_requests = [rt for rt in response_times if rt > 5000]
        print(f"  Avg response time: {avg_response_time:.2f}ms")
        print(f"  Slow requests (>5s): {len(slow_requests)}")

    print("\n✅ Enterprise log parsing test completed successfully!")

def test_existing_logs():
    """Test with existing logs in the logs/activities directory."""
    print("\n" + "=" * 50)
    print("Testing Existing Logs")
    print("=" * 50)
    
    logs_dir = "logs/activities"
    if not os.path.exists(logs_dir):
        print(f"Directory {logs_dir} not found")
        return
    
    # Find log files with content
    log_files = []
    for filename in os.listdir(logs_dir):
        if filename.endswith('.log') or '.log.' in filename:
            filepath = os.path.join(logs_dir, filename)
            if os.path.getsize(filepath) > 0:
                log_files.append(filepath)
    
    if not log_files:
        print("No log files with content found")
        return
    
    print(f"Found {len(log_files)} log files with content")
    
    # Test first few files
    detector = LogFormatDetector()
    parser = LogParser()
    
    for log_file in log_files[:3]:
        print(f"\nTesting: {log_file}")
        
        try:
            # Test format detection
            format_info = detector.detect_format(log_file)
            print(f"  Format: {format_info['format']} (confidence: {format_info['confidence']:.2%})")
            
            # Test parsing
            parsed_data = parser.parse_file(log_file, max_lines=10)
            print(f"  Parsed: {parsed_data['parsed_lines']}/{parsed_data['total_lines']} lines")
            
            if parsed_data['entries']:
                entry = parsed_data['entries'][0]
                if entry['fields']:
                    print(f"  Sample fields: {list(entry['fields'].keys())[:5]}")
                
        except Exception as e:
            print(f"  Error: {e}")

if __name__ == "__main__":
    test_enterprise_log_parsing()
    test_existing_logs()
