# 🚀 Log Analyzer - Startup Guide

This guide explains how to start and manage the Log Analyzer application using the provided startup scripts.

## 📋 Prerequisites

Before starting the application, ensure you have the following installed:

- **Python 3.8+** - For the backend Flask API
- **Node.js 14+** - For the frontend React application
- **npm** - Node package manager (comes with Node.js)
- **curl** - For health checks (optional but recommended)

## 🎯 Quick Start

### 1. Start Everything (Recommended)
```bash
./start.sh
```
This will:
- Set up Python virtual environment
- Install backend dependencies
- Install frontend dependencies
- Start backend API on port 5000
- Start frontend on port 3000
- Show startup information

### 2. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api/docs
- **Health Check**: http://localhost:5000/api/health

### 3. Stop Everything
```bash
./stop.sh
```

## 📜 Available Scripts

### Main Scripts

| Script | Description | Usage |
|--------|-------------|-------|
| `start.sh` | Start both backend and frontend | `./start.sh` |
| `stop.sh` | Stop both backend and frontend | `./stop.sh` |
| `start-backend.sh` | Start only backend | `./start-backend.sh [options]` |
| `start-frontend.sh` | Start only frontend | `./start-frontend.sh [options]` |
| `dev.sh` | Development utilities | `./dev.sh <command>` |

### Backend Options

```bash
# Start in background (default)
./start-backend.sh

# Start in development mode (foreground with auto-reload)
./start-backend.sh --dev

# Start in production mode with gunicorn
./start-backend.sh --prod

# Show help
./start-backend.sh --help
```

### Frontend Options

```bash
# Start in background development mode (default)
./start-frontend.sh

# Start in foreground development mode
./start-frontend.sh --dev

# Build production version only
./start-frontend.sh --build

# Build and serve production version
./start-frontend.sh --serve

# Show help
./start-frontend.sh --help
```

### Development Commands

```bash
# Set up development environment
./dev.sh setup

# Start both services in development mode
./dev.sh start

# Start only backend in development mode
./dev.sh backend

# Start only frontend in development mode
./dev.sh frontend

# Run all tests
./dev.sh test

# Run linting
./dev.sh lint

# Format code
./dev.sh format

# Build for production
./dev.sh build

# Clean build artifacts
./dev.sh clean

# Show application logs
./dev.sh logs

# Check application status
./dev.sh status

# Stop all services
./dev.sh stop
```

## 🔧 Configuration

### Environment Variables

The scripts automatically set the following environment variables:

**Backend:**
- `FLASK_APP=app.py`
- `FLASK_ENV=development`
- `FLASK_DEBUG=1`

**Frontend:**
- `REACT_APP_API_URL=http://localhost:5000`
- `PORT=3000`
- `BROWSER=none` (prevents auto-opening browser)

### Ports

- **Backend**: 5000 (configurable in scripts)
- **Frontend**: 3000 (configurable in scripts)

## 📁 File Structure

```
log_analyzer/
├── start.sh              # Main startup script
├── stop.sh               # Main stop script
├── start-backend.sh      # Backend startup script
├── start-frontend.sh     # Frontend startup script
├── dev.sh               # Development utilities
├── backend/             # Flask backend
│   ├── app.py
│   ├── requirements.txt
│   └── ...
├── frontend/            # React frontend
│   ├── package.json
│   ├── src/
│   └── ...
├── venv/               # Python virtual environment (created automatically)
├── logs/               # Application logs (created automatically)
│   ├── backend/
│   │   ├── flask.log
│   │   └── flask.pid
│   └── frontend/
│       ├── react.log
│       └── react.pid
└── sample_logs/        # Sample log files
```

## 🔍 Monitoring and Logs

### Log Files

Application logs are stored in the `logs/` directory:

- **Backend logs**: `logs/backend/flask.log`
- **Frontend logs**: `logs/frontend/react.log`
- **Process IDs**: `logs/backend/flask.pid`, `logs/frontend/react.pid`

### Health Checks

Check if services are running:

```bash
# Backend health check
curl http://localhost:5000/api/health

# Frontend check
curl http://localhost:3000

# Or use the development script
./dev.sh status
```

### View Logs

```bash
# View recent logs
./dev.sh logs

# View backend logs directly
tail -f logs/backend/flask.log

# View frontend logs directly
tail -f logs/frontend/react.log
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill processes on specific ports
   sudo lsof -ti :5000 | xargs kill -9  # Backend
   sudo lsof -ti :3000 | xargs kill -9  # Frontend
   
   # Or use force stop
   ./stop.sh --force
   ```

2. **Python dependencies issues**
   ```bash
   # Clean and reinstall
   rm -rf venv/
   ./dev.sh setup
   ```

3. **Node.js dependencies issues**
   ```bash
   # Clean and reinstall
   cd frontend/
   rm -rf node_modules/
   npm install
   ```

4. **Permission denied**
   ```bash
   # Make scripts executable
   chmod +x *.sh
   ```

### Debug Mode

For detailed debugging:

```bash
# Start backend in development mode
./start-backend.sh --dev

# Start frontend in development mode
./start-frontend.sh --dev
```

## 🚀 Production Deployment

### Build for Production

```bash
# Build frontend
./start-frontend.sh --build

# Or use development script
./dev.sh build
```

### Production Mode

```bash
# Start backend in production mode
./start-backend.sh --prod

# Serve frontend production build
./start-frontend.sh --serve
```

## 📊 Features

The Log Analyzer supports:

- **Multiple log formats**: Enterprise structured, JSON, Apache, Nginx, Syslog
- **Advanced analytics**: Performance, customer, and application analysis
- **Real-time parsing**: Automatic format detection
- **Rich visualizations**: Charts and dashboards
- **Export capabilities**: JSON and CSV export
- **Search functionality**: Full-text search with filters

## 🆘 Getting Help

- **Script help**: Add `--help` to any script
- **API documentation**: Visit http://localhost:5000/api/docs
- **Health status**: Visit http://localhost:5000/api/health

## 📝 Examples

### Basic Usage

```bash
# 1. Start the application
./start.sh

# 2. Open browser to http://localhost:3000

# 3. Upload a log file and analyze

# 4. Stop when done
./stop.sh
```

### Development Workflow

```bash
# 1. Set up development environment
./dev.sh setup

# 2. Start in development mode
./dev.sh start

# 3. Make changes to code (auto-reload enabled)

# 4. Run tests
./dev.sh test

# 5. Format and lint code
./dev.sh format
./dev.sh lint

# 6. Build for production
./dev.sh build
```

---

**Happy analyzing! 🎉**
